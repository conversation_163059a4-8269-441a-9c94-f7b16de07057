import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OnboardingProvider with ChangeNotifier {
  bool _isFirstLaunch = true;
  bool _isLoading = true;

  bool get isFirstLaunch => _isFirstLaunch;
  bool get isLoading => _isLoading;

  OnboardingProvider() {
    _checkFirstLaunch();
  }

  Future<void> _checkFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _isFirstLaunch = prefs.getBool('is_first_launch') ?? true;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error checking first launch: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> completeOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_first_launch', false);
      _isFirstLaunch = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error completing onboarding: $e');
    }
  }

  Future<void> resetOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_first_launch', true);
      _isFirstLaunch = true;
      notifyListeners();
    } catch (e) {
      debugPrint('Error resetting onboarding: $e');
    }
  }
}
