import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/providers/customer_provider.dart';
import 'package:pos_app/providers/invoice_provider.dart';
import 'package:pos_app/models/customer.dart';
import 'package:pos_app/models/invoice.dart';
import 'package:pos_app/screens/customer_form_screen.dart';
import 'package:pos_app/screens/invoice_details_screen.dart';

class DebtManagementScreen extends StatefulWidget {
  const DebtManagementScreen({super.key});

  @override
  State<DebtManagementScreen> createState() => _DebtManagementScreenState();
}

class _DebtManagementScreenState extends State<DebtManagementScreen> {
  String _searchQuery = '';
  String _sortBy = 'debt_amount'; // debt_amount, customer_name, invoice_count
  bool _sortAscending = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    Future.microtask(() {
      if (mounted) {
        Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
        Provider.of<InvoiceProvider>(context, listen: false).loadInvoices();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isRTL = localeProvider.isRTL;

    return Scaffold(
      body: Column(
        children: [
          _buildHeader(theme, isRTL),
          _buildSearchAndFilters(theme, isRTL),
          Expanded(child: _buildDebtList(theme, isRTL)),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF4CAF50),
            const Color(0xFF4CAF50).withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const FaIcon(
                  FontAwesomeIcons.creditCard,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isRTL ? 'إدارة الديون' : 'Debt Management',
                      style: GoogleFonts.cairo(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      isRTL
                          ? 'تتبع وإدارة ديون العملاء'
                          : 'Track and manage customer debts',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildDebtSummaryCards(theme, isRTL),
        ],
      ),
    );
  }

  Widget _buildDebtSummaryCards(ThemeData theme, bool isRTL) {
    return Consumer2<CustomerProvider, InvoiceProvider>(
      builder: (context, customerProvider, invoiceProvider, _) {
        final customersWithDebt = _getCustomersWithDebt(
          customerProvider.customers,
          invoiceProvider.invoices,
        );

        final totalDebt = customersWithDebt.fold<double>(
          0,
          (sum, data) => sum + (data['debtAmount'] as double),
        );

        final totalCustomersWithDebt = customersWithDebt.length;

        return Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                title: isRTL ? 'إجمالي الديون' : 'Total Debt',
                value: Provider.of<CurrencyProvider>(
                  context,
                ).formatCurrency(totalDebt),
                icon: FontAwesomeIcons.coins,
                color: Colors.red,
                isRTL: isRTL,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                title: isRTL ? 'العملاء المدينون' : 'Customers with Debt',
                value: totalCustomersWithDebt.toString(),
                icon: FontAwesomeIcons.users,
                color: Colors.orange,
                isRTL: isRTL,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required bool isRTL,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              FaIcon(icon, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(ThemeData theme, bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          TextField(
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: isRTL ? 'البحث عن عميل...' : 'Search customer...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: theme.colorScheme.surface,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _sortBy,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'ترتيب حسب' : 'Sort by',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: [
                    DropdownMenuItem(
                      value: 'debt_amount',
                      child: Text(isRTL ? 'مبلغ الدين' : 'Debt Amount'),
                    ),
                    DropdownMenuItem(
                      value: 'customer_name',
                      child: Text(isRTL ? 'اسم العميل' : 'Customer Name'),
                    ),
                    DropdownMenuItem(
                      value: 'invoice_count',
                      child: Text(isRTL ? 'عدد الفواتير' : 'Invoice Count'),
                    ),
                  ],
                  onChanged: (value) => setState(() => _sortBy = value!),
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                onPressed:
                    () => setState(() => _sortAscending = !_sortAscending),
                icon: FaIcon(
                  _sortAscending
                      ? FontAwesomeIcons.arrowUp
                      : FontAwesomeIcons.arrowDown,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.primaryContainer,
                  foregroundColor: theme.colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDebtList(ThemeData theme, bool isRTL) {
    return Consumer2<CustomerProvider, InvoiceProvider>(
      builder: (context, customerProvider, invoiceProvider, _) {
        if (invoiceProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final customersWithDebt = _getFilteredAndSortedCustomersWithDebt(
          customerProvider.customers,
          invoiceProvider.invoices,
        );

        if (customersWithDebt.isEmpty) {
          return _buildEmptyState(isRTL);
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: customersWithDebt.length,
          itemBuilder: (context, index) {
            final customerData = customersWithDebt[index];
            return _buildDebtCard(customerData, theme, isRTL);
          },
        );
      },
    );
  }

  Widget _buildEmptyState(bool isRTL) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.handHoldingDollar,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            isRTL ? 'لا توجد ديون' : 'No debts found',
            style: GoogleFonts.cairo(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL
                ? 'جميع العملاء قد سددوا مستحقاتهم'
                : 'All customers have paid their dues',
            style: GoogleFonts.cairo(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getCustomersWithDebt(
    List<Customer> customers,
    List<Invoice> invoices,
  ) {
    final customersWithDebt = <Map<String, dynamic>>[];

    for (final customer in customers) {
      final customerId = customer.id;
      if (customerId == null) continue;

      final unpaidInvoices =
          invoices
              .where(
                (invoice) =>
                    invoice.customerId == customerId &&
                    invoice.status.toLowerCase() != 'paid',
              )
              .toList();

      if (unpaidInvoices.isNotEmpty) {
        final debtAmount = unpaidInvoices.fold<double>(
          0,
          (sum, invoice) => sum + invoice.finalAmount,
        );

        customersWithDebt.add({
          'customer': customer,
          'debtAmount': debtAmount,
          'invoiceCount': unpaidInvoices.length,
          'unpaidInvoices': unpaidInvoices,
        });
      }
    }

    return customersWithDebt;
  }

  List<Map<String, dynamic>> _getFilteredAndSortedCustomersWithDebt(
    List<Customer> customers,
    List<Invoice> invoices,
  ) {
    var customersWithDebt = _getCustomersWithDebt(customers, invoices);

    // Filter by search query
    if (_searchQuery.isNotEmpty) {
      customersWithDebt =
          customersWithDebt.where((data) {
            final customer = data['customer'] as Customer;
            return customer.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                customer.phone.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                );
          }).toList();
    }

    // Sort
    customersWithDebt.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'debt_amount':
          comparison = (a['debtAmount'] as double).compareTo(
            b['debtAmount'] as double,
          );
          break;
        case 'customer_name':
          comparison = (a['customer'] as Customer).name.compareTo(
            (b['customer'] as Customer).name,
          );
          break;
        case 'invoice_count':
          comparison = (a['invoiceCount'] as int).compareTo(
            b['invoiceCount'] as int,
          );
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return customersWithDebt;
  }

  Widget _buildDebtCard(
    Map<String, dynamic> customerData,
    ThemeData theme,
    bool isRTL,
  ) {
    final customer = customerData['customer'] as Customer;
    final debtAmount = customerData['debtAmount'] as double;
    final invoiceCount = customerData['invoiceCount'] as int;
    final unpaidInvoices = customerData['unpaidInvoices'] as List<Invoice>;
    final currencyProvider = Provider.of<CurrencyProvider>(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showCustomerDebtDetails(customer, unpaidInvoices),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: theme.colorScheme.primaryContainer,
                    child: Text(
                      customer.name.isNotEmpty
                          ? customer.name[0].toUpperCase()
                          : '?',
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          customer.name,
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (customer.phone.isNotEmpty)
                          Text(
                            customer.phone,
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      currencyProvider.formatCurrency(debtAmount),
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.red[700],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildInfoChip(
                    icon: FontAwesomeIcons.fileInvoice,
                    label: isRTL ? 'فاتورة غير مدفوعة' : 'unpaid invoices',
                    value: invoiceCount.toString(),
                    color: Colors.orange,
                    isRTL: isRTL,
                  ),
                  const SizedBox(width: 12),
                  _buildInfoChip(
                    icon: FontAwesomeIcons.calendar,
                    label: isRTL ? 'آخر فاتورة' : 'last invoice',
                    value: _getLastInvoiceDate(unpaidInvoices),
                    color: Colors.blue,
                    isRTL: isRTL,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showPaymentDialog(customer, debtAmount),
                      icon: const FaIcon(FontAwesomeIcons.creditCard, size: 16),
                      label: Text(isRTL ? 'تسديد دين' : 'Pay Debt'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: const Color(0xFF4CAF50),
                        side: const BorderSide(color: Color(0xFF4CAF50)),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editCustomer(customer),
                      icon: const FaIcon(
                        FontAwesomeIcons.penToSquare,
                        size: 16,
                      ),
                      label: Text(isRTL ? 'تعديل' : 'Edit'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue,
                        side: const BorderSide(color: Colors.blue),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isRTL,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(icon, size: 12, color: color),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                '$value $label',
                style: GoogleFonts.cairo(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getLastInvoiceDate(List<Invoice> invoices) {
    if (invoices.isEmpty) return '-';

    invoices.sort((a, b) => b.date.compareTo(a.date));
    final lastInvoice = invoices.first;

    final now = DateTime.now();
    final difference = now.difference(lastInvoice.date).inDays;

    if (difference == 0) return 'Today';
    if (difference == 1) return 'Yesterday';
    if (difference < 7) return '${difference}d ago';
    if (difference < 30) return '${(difference / 7).round()}w ago';
    return '${(difference / 30).round()}m ago';
  }

  void _showCustomerDebtDetails(
    Customer customer,
    List<Invoice> unpaidInvoices,
  ) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            expand: false,
            builder:
                (context, scrollController) => _buildDebtDetailsSheet(
                  customer,
                  unpaidInvoices,
                  scrollController,
                  isRTL,
                ),
          ),
    );
  }

  Widget _buildDebtDetailsSheet(
    Customer customer,
    List<Invoice> unpaidInvoices,
    ScrollController scrollController,
    bool isRTL,
  ) {
    final theme = Theme.of(context);
    final currencyProvider = Provider.of<CurrencyProvider>(context);

    final totalDebt = unpaidInvoices.fold<double>(
      0,
      (sum, invoice) => sum + invoice.finalAmount,
    );

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: theme.colorScheme.primaryContainer,
                child: Text(
                  customer.name.isNotEmpty
                      ? customer.name[0].toUpperCase()
                      : '?',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      customer.name,
                      style: GoogleFonts.cairo(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (customer.phone.isNotEmpty)
                      Text(
                        customer.phone,
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                Text(
                  isRTL ? 'إجمالي المبلغ المستحق' : 'Total Outstanding Amount',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.red[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  currencyProvider.formatCurrency(totalDebt),
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Text(
            isRTL ? 'الفواتير غير المدفوعة' : 'Unpaid Invoices',
            style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              controller: scrollController,
              itemCount: unpaidInvoices.length,
              itemBuilder: (context, index) {
                final invoice = unpaidInvoices[index];
                return _buildInvoiceCard(invoice, theme, isRTL);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice, ThemeData theme, bool isRTL) {
    final currencyProvider = Provider.of<CurrencyProvider>(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.orange.withValues(alpha: 0.2),
          child: FaIcon(
            FontAwesomeIcons.fileInvoice,
            color: Colors.orange,
            size: 16,
          ),
        ),
        title: Text(
          '${isRTL ? 'فاتورة رقم' : 'Invoice'} #${invoice.invoiceNumber}',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          '${isRTL ? 'التاريخ:' : 'Date:'} ${invoice.date.day}/${invoice.date.month}/${invoice.date.year}',
          style: GoogleFonts.cairo(fontSize: 12),
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              currencyProvider.formatCurrency(invoice.finalAmount),
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
                color: Colors.red[700],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                invoice.status,
                style: GoogleFonts.cairo(fontSize: 10, color: Colors.red[700]),
              ),
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => InvoiceDetailsScreen(invoice: invoice),
            ),
          );
        },
      ),
    );
  }

  void _showPaymentDialog(Customer customer, double debtAmount) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    final currencyProvider = Provider.of<CurrencyProvider>(
      context,
      listen: false,
    );
    final paymentController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'تسديد دين' : 'Pay Debt'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${isRTL ? 'العميل:' : 'Customer:'} ${customer.name}',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Text(
                  '${isRTL ? 'إجمالي الدين:' : 'Total Debt:'} ${currencyProvider.formatCurrency(debtAmount)}',
                  style: GoogleFonts.cairo(color: Colors.red[700]),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: paymentController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'مبلغ الدفع' : 'Payment Amount',
                    border: const OutlineInputBorder(),
                    prefixText: currencyProvider.currentCurrencyInfo.symbol,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  final paymentAmount = double.tryParse(paymentController.text);
                  if (paymentAmount != null && paymentAmount > 0) {
                    _processPayment(customer, paymentAmount);
                    Navigator.pop(context);
                  }
                },
                child: Text(isRTL ? 'دفع' : 'Pay'),
              ),
            ],
          ),
    );
  }

  void _processPayment(Customer customer, double paymentAmount) {
    // This is a simplified payment processing
    // In a real app, you would update the invoice statuses and customer balance
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'تم تسجيل الدفع بنجاح'
              : 'Payment recorded successfully',
        ),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );

    // Refresh the data
    _loadData();
  }

  void _editCustomer(Customer customer) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CustomerFormScreen(customer: customer),
      ),
    ).then((_) => _loadData());
  }
}
