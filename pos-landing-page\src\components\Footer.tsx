import React from 'react'
import { motion } from 'framer-motion'
import { 
  ShoppingCart, 
  Mail, 
  Phone, 
  MapPin,
  Twitter,
  Facebook,
  Linkedin,
  Github,
  ArrowUp
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { scrollToSection } from '@/lib/utils'

const Footer: React.FC = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const footerLinks = {
    product: [
      { label: 'Features', href: '#features' },
      { label: 'Pricing', href: '#contact' },
      { label: 'Live Demo', href: 'http://localhost:8080' },
      { label: 'Documentation', href: '#' },
    ],
    company: [
      { label: 'About Us', href: '#' },
      { label: 'Careers', href: '#' },
      { label: 'Blog', href: '#' },
      { label: 'News', href: '#' },
    ],
    support: [
      { label: 'Help Center', href: '#' },
      { label: 'Technical Support', href: '#contact' },
      { label: 'System Status', href: '#' },
      { label: 'Training', href: '#' },
    ],
    legal: [
      { label: 'Privacy Policy', href: '#' },
      { label: 'Terms of Service', href: '#' },
      { label: 'Cookie Policy', href: '#' },
      { label: 'Data Protection', href: '#' },
    ],
  }

  const socialLinks = [
    { icon: Twitter, href: '#', label: 'Twitter' },
    { icon: Facebook, href: '#', label: 'Facebook' },
    { icon: Linkedin, href: '#', label: 'LinkedIn' },
    { icon: Github, href: '#', label: 'GitHub' },
  ]

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-64 h-64 bg-primary-500 rounded-full mix-blend-multiply filter blur-xl opacity-10" />
        <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-10" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12">
            {/* Company Info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-2"
            >
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                  <ShoppingCart className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold">POS Pro Algeria</h3>
                  <p className="text-gray-400 text-sm">Professional Point of Sale System</p>
                </div>
              </div>

              <p className="text-gray-300 mb-6 leading-relaxed">
                The most advanced point of sale system designed for modern businesses in Algeria.
                Features partial payments, multi-language support, and comprehensive inventory management.
                Works completely offline with automatic cloud backup and synchronization.
              </p>

              <div className="space-y-4">
                <div className="flex items-center text-gray-300">
                  <Mail className="w-5 h-5 mr-3 text-green-400" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Phone className="w-5 h-5 mr-3 text-green-400" />
                  <span>+213 (0) 21 123 456</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <Phone className="w-5 h-5 mr-3 text-green-400" />
                  <span>+213 (0) 555 789 123</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <MapPin className="w-5 h-5 mr-3 text-green-400" />
                  <span>شارع ديدوش مراد، الجزائر العاصمة 16000</span>
                </div>
                <div className="flex items-center text-gray-300">
                  <MapPin className="w-5 h-5 mr-3 text-green-400" />
                  <span>حي النصر، وهران 31000</span>
                </div>
              </div>
            </motion.div>

            {/* Links Sections */}
            {Object.entries(footerLinks).map(([category, links], index) => (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 + index * 0.1 }}
              >
                <h4 className="text-lg font-semibold mb-4 capitalize">
                  {category === 'product' ? 'Product' : 
                   category === 'company' ? 'Company' :
                   category === 'support' ? 'Support' : 'Legal'}
                </h4>
                <ul className="space-y-3">
                  {links.map((link) => (
                    <li key={link.label}>
                      <button
                        onClick={() => {
                          if (link.href.startsWith('#')) {
                            if (link.href === '#features' || link.href === '#contact') {
                              scrollToSection(link.href.substring(1))
                            }
                          } else if (link.href.startsWith('http')) {
                            window.open(link.href, '_blank')
                          }
                        }}
                        className="text-gray-300 hover:text-primary-400 transition-colors duration-200 text-left"
                      >
                        {link.label}
                      </button>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 py-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            {/* Copyright */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              className="text-gray-400 text-sm"
            >
              © 2024 POS Pro Algeria. All rights reserved. Built with ❤️ using Flutter & React.
            </motion.div>

            {/* Social Links */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex items-center space-x-4"
            >
              {socialLinks.map((social) => (
                <button
                  key={social.label}
                  onClick={() => window.open(social.href, '_blank')}
                  className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-primary-400 hover:bg-gray-700 transition-all duration-200"
                  aria-label={social.label}
                >
                  <social.icon className="w-5 h-5" />
                </button>
              ))}
            </motion.div>

            {/* Back to Top */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Button
                variant="outline"
                size="sm"
                onClick={scrollToTop}
                className="border-gray-700 text-gray-300 hover:bg-primary-500 hover:border-primary-500 hover:text-white"
              >
                <ArrowUp className="w-4 h-4 mr-2" />
                Back to Top
              </Button>
            </motion.div>
          </div>
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="border-t border-gray-800 py-6"
        >
          <div className="text-center">
            <p className="text-gray-500 text-sm mb-4">
              This POS system supports multiple languages (Arabic RTL, English, French, Spanish, German)
              and multiple currencies (Algerian Dinar, USD, EUR, GBP, CAD)
            </p>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-gray-600">
              <span className="flex items-center justify-center">🌍 Multi-Language</span>
              <span className="flex items-center justify-center">💰 Multi-Currency</span>
              <span className="flex items-center justify-center">🖨️ Thermal Printing</span>
              <span className="flex items-center justify-center">📱 Cross-Platform</span>
              <span className="flex items-center justify-center">☁️ Cloud Backup</span>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-800">
              <p className="text-gray-600 text-xs">
                🇩🇿 Designed specifically for the Algerian market | Supports Algerian Dinar | Compliant with local regulations
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}

export default Footer
