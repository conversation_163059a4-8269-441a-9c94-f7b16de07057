class Supplier {
  final int? id;
  final String name;
  final String phone;
  final String email;
  final String address;
  final double balance;
  final String notes;

  Supplier({
    this.id,
    required this.name,
    required this.phone,
    this.email = '',
    this.address = '',
    this.balance = 0.0,
    this.notes = '',
  });

  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'],
      name: map['name'],
      phone: map['phone'],
      email: map['email'] ?? '',
      address: map['address'] ?? '',
      balance: map['balance'] ?? 0.0,
      notes: map['notes'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'balance': balance,
      'notes': notes,
    };
  }

  Supplier copyWith({
    int? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    double? balance,
    String? notes,
  }) {
    return Supplier(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
    );
  }
}
