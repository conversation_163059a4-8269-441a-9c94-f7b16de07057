import 'package:flutter/material.dart';
import 'package:pos_app/models/invoice_settings.dart';

// مزود مبسط لإعدادات الفاتورة (غير متصل بقاعدة البيانات)
class InvoiceSettingsProvider with ChangeNotifier {
  // إعدادات افتراضية للفاتورة
  InvoiceSettings _settings = InvoiceSettings(
    storeName: 'المتجر',
    storeAddress: 'العنوان',
    phoneNumber: '**********',
    currencySymbol: 'دج',
    rtlDirection: true,
  );
  
  InvoiceSettings get settings => _settings;
  
  // تحديث إعدادات الفاتورة
  void updateInvoiceSettings(InvoiceSettings settings) {
    _settings = settings;
    notifyListeners();
  }
  

}
