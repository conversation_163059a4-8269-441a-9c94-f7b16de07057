import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/customer_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/models/customer.dart';
import 'package:pos_app/screens/receipt_screen.dart';
import 'package:pos_app/models/invoice.dart';
import 'package:pos_app/db/database_bridge.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _customerNameController = TextEditingController();
  String _paymentMethod = 'Cash';
  double _amountPaid = 0.0;
  double _change = 0.0;
  Customer? _selectedCustomer;
  bool _isPartialPayment = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cartProvider = Provider.of<CartProvider>(context, listen: false);
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );

      _customerNameController.text = cartProvider.customerName;
      _paymentMethod = cartProvider.paymentMethod;

      // Load customers
      customerProvider.loadCustomers();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _customerNameController.dispose();
    super.dispose();
  }

  void _calculateChange() {
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final totalAmount = cartProvider.totalAmount;
    setState(() {
      _amountPaid = double.tryParse(_amountController.text) ?? 0.0;
      _change = _amountPaid - totalAmount;
    });
  }

  void _completeCheckout() async {
    if (!_formKey.currentState!.validate()) return;
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final totalAmount = cartProvider.totalAmount;

    // Validate partial payment requirements
    if (_isPartialPayment && _selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'يجب اختيار عميل للدفع الجزئي'
                : 'Customer must be selected for partial payment',
          ),
          backgroundColor: Colors.red,
        ),
      );
      setState(() {
        _isProcessing = false;
      });
      return;
    }

    cartProvider.customerName = _customerNameController.text;
    cartProvider.paymentMethod = _paymentMethod;

    try {
      // إتمام عملية الدفع
      final transaction = await cartProvider.checkout(_amountPaid);

      if (!mounted) return;

      // استخدام جسر قاعدة البيانات
      final dbBridge = DatabaseBridge();

      // إنشاء رقم الفاتورة بشكل بسيط
      final String invoicePrefix = 'INV';
      final int invoiceNumber = DateTime.now().millisecondsSinceEpoch % 10000;
      final String formattedInvoiceNumber = '$invoicePrefix$invoiceNumber';

      // Determine invoice status based on payment
      String invoiceStatus;
      if (_amountPaid >= totalAmount) {
        invoiceStatus = 'paid';
      } else if (_amountPaid > 0) {
        invoiceStatus = 'partial';
      } else {
        invoiceStatus = 'unpaid';
      }

      // إنشاء كائن الفاتورة
      final invoice = Invoice(
        id: 0, // سيتم تعيينه في قاعدة البيانات
        invoiceNumber: formattedInvoiceNumber,
        date: DateTime.now(),
        customerId: _selectedCustomer?.id,
        customerName: _customerNameController.text,
        totalAmount: transaction.total,
        discountAmount: 0.0, // transaction.discount,
        taxAmount: 0.0, // transaction.tax,
        finalAmount: transaction.total,
        paymentMethod: _paymentMethod,
        status: invoiceStatus,
        notes:
            _isPartialPayment
                ? 'Partial payment: ${_amountPaid.toStringAsFixed(2)}'
                : '',
        items:
            cartProvider.items
                .map(
                  (cartItem) => InvoiceItem(
                    productId: cartItem.product.id!,
                    productName: cartItem.product.name,
                    quantity: cartItem.quantity.toDouble(),
                    unitPrice: cartItem.price,
                    totalPrice: cartItem.price * cartItem.quantity,
                  ),
                )
                .toList(),
        // itemsJson تم إزالة هذا المعامل من النموذج
      );

      // إضافة الفاتورة إلى قاعدة البيانات
      try {
        final invoiceId = await dbBridge.addInvoice(invoice);
        debugPrint('تم حفظ الفاتورة برقم معرّف: $invoiceId');

        // زيادة رقم الفاتورة التالي
        await dbBridge.incrementNextInvoiceNumber();

        // Create debt record if partial payment
        if (_isPartialPayment &&
            _selectedCustomer != null &&
            _amountPaid < totalAmount) {
          final debtAmount = totalAmount - _amountPaid;

          // Update customer balance
          final updatedCustomer = Customer(
            id: _selectedCustomer!.id,
            name: _selectedCustomer!.name,
            phone: _selectedCustomer!.phone,
            email: _selectedCustomer!.email,
            address: _selectedCustomer!.address,
            balance: _selectedCustomer!.balance + debtAmount,
            notes: _selectedCustomer!.notes,
          );

          // Update customer in database
          if (mounted) {
            final customerProvider = Provider.of<CustomerProvider>(
              context,
              listen: false,
            );
            await customerProvider.updateCustomer(updatedCustomer);
          }

          debugPrint(
            'تم إنشاء دين بمبلغ: $debtAmount للعميل: ${_selectedCustomer!.name}',
          );
        }

        // تم إنشاء الفاتورة بنجاح مع ID: $invoiceId
      } catch (e) {
        debugPrint('خطأ في حفظ الفاتورة: $e');
        // استمر على أي حال حتى لو فشل حفظ الفاتورة
      }

      // تحديد نوع شاشة العرض بناءً على إعدادات التطبيق
      if (!mounted) return;

      // عرض إيصال بسيط
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ReceiptScreen(transaction: transaction),
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      final localeProvider = Provider.of<LocaleProvider>(
        context,
        listen: false,
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            localeProvider.isRTL
                ? 'فشلت عملية الدفع: $e'
                : 'Checkout failed: $e',
          ),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<
      CartProvider,
      CustomerProvider,
      LocaleProvider,
      CurrencyProvider
    >(
      builder: (
        context,
        cartProvider,
        customerProvider,
        localeProvider,
        currencyProvider,
        _,
      ) {
        final totalAmount = cartProvider.totalAmount;

        return Scaffold(
          appBar: AppBar(
            title: Text(localeProvider.isRTL ? 'الدفع' : 'Checkout'),
          ),
          body: Form(
            key: _formKey,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Total Amount Section with Green Background
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(20.0),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localeProvider.isRTL
                              ? 'المجموع الإجمالي: ${currencyProvider.formatCurrency(totalAmount)}'
                              : 'Total: ${currencyProvider.formatCurrency(totalAmount)}',
                          style: Theme.of(
                            context,
                          ).textTheme.headlineMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          textDirection:
                              localeProvider.isRTL
                                  ? ui.TextDirection.rtl
                                  : ui.TextDirection.ltr,
                        ),
                        if (_amountPaid > 0) ...[
                          const SizedBox(height: 12),
                          Text(
                            localeProvider.isRTL
                                ? 'المبلغ المدفوع: ${currencyProvider.formatCurrency(_amountPaid)}'
                                : 'Amount Paid: ${currencyProvider.formatCurrency(_amountPaid)}',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: Colors.white),
                            textDirection:
                                localeProvider.isRTL
                                    ? ui.TextDirection.rtl
                                    : ui.TextDirection.ltr,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            localeProvider.isRTL
                                ? 'الباقي: ${currencyProvider.formatCurrency(_change)}'
                                : 'Change: ${currencyProvider.formatCurrency(_change)}',
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              color:
                                  _change >= 0
                                      ? Colors.white
                                      : Colors.red.shade100,
                              fontWeight: FontWeight.bold,
                            ),
                            textDirection:
                                localeProvider.isRTL
                                    ? ui.TextDirection.rtl
                                    : ui.TextDirection.ltr,
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Customer Selection Dropdown
                  DropdownButtonFormField<Customer?>(
                    value: _selectedCustomer,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'العميل' : 'Customer',
                      prefixIcon: const Icon(Icons.person),
                      border: const OutlineInputBorder(),
                    ),
                    items: [
                      DropdownMenuItem<Customer?>(
                        value: null,
                        child: Text(
                          localeProvider.isRTL ? 'عميل نقدي' : 'Cash Customer',
                          textDirection:
                              localeProvider.isRTL
                                  ? ui.TextDirection.rtl
                                  : ui.TextDirection.ltr,
                        ),
                      ),
                      ...customerProvider.customers.map(
                        (customer) => DropdownMenuItem<Customer?>(
                          value: customer,
                          child: Text(
                            customer.name,
                            textDirection:
                                localeProvider.isRTL
                                    ? ui.TextDirection.rtl
                                    : ui.TextDirection.ltr,
                          ),
                        ),
                      ),
                    ],
                    onChanged: (Customer? customer) {
                      setState(() {
                        _selectedCustomer = customer;
                        _customerNameController.text = customer?.name ?? '';
                        cartProvider.customerName = customer?.name ?? '';
                      });
                    },
                  ),

                  // Customer Debt Balance Display
                  if (_selectedCustomer != null) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color:
                            _selectedCustomer!.balance > 0
                                ? Colors.orange.shade50
                                : Colors.green.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              _selectedCustomer!.balance > 0
                                  ? Colors.orange
                                  : Colors.green,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _selectedCustomer!.balance > 0
                                ? Icons.warning_amber
                                : Icons.check_circle,
                            color:
                                _selectedCustomer!.balance > 0
                                    ? Colors.orange
                                    : Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              localeProvider.isRTL
                                  ? 'الرصيد: ${currencyProvider.formatCurrency(_selectedCustomer!.balance)}'
                                  : 'Balance: ${currencyProvider.formatCurrency(_selectedCustomer!.balance)}',
                              style: TextStyle(
                                color:
                                    _selectedCustomer!.balance > 0
                                        ? Colors.orange.shade700
                                        : Colors.green.shade700,
                                fontWeight: FontWeight.w600,
                              ),
                              textDirection:
                                  localeProvider.isRTL
                                      ? ui.TextDirection.rtl
                                      : ui.TextDirection.ltr,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _paymentMethod,
                    decoration: const InputDecoration(
                      labelText: 'Payment Method',
                      prefixIcon: Icon(Icons.payment),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'Cash', child: Text('Cash')),
                      DropdownMenuItem(value: 'Card', child: Text('Card')),
                      DropdownMenuItem(
                        value: 'Mobile Payment',
                        child: Text('Mobile Payment'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _paymentMethod = value!;
                        cartProvider.paymentMethod = value;
                      });
                    },
                  ),
                  const SizedBox(height: 16),

                  // Partial Payment Toggle
                  Card(
                    elevation: 1,
                    child: SwitchListTile(
                      title: Text(
                        localeProvider.isRTL ? 'دفع جزئي' : 'Partial Payment',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      subtitle: Text(
                        localeProvider.isRTL
                            ? 'السماح بالدفع الجزئي وإنشاء دين للعميل'
                            : 'Allow partial payment and create debt for customer',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      value: _isPartialPayment,
                      onChanged:
                          _selectedCustomer != null
                              ? (value) {
                                setState(() {
                                  _isPartialPayment = value;
                                  if (!value) {
                                    // Reset amount to full amount when disabling partial payment
                                    _amountController.text = totalAmount
                                        .toStringAsFixed(2);
                                    _calculateChange();
                                  }
                                });
                              }
                              : null,
                      activeColor: Colors.orange,
                      secondary: Icon(
                        _isPartialPayment
                            ? Icons.account_balance_wallet
                            : Icons.payment,
                        color: _isPartialPayment ? Colors.orange : Colors.grey,
                      ),
                    ),
                  ),

                  if (!_isPartialPayment && _selectedCustomer == null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        localeProvider.isRTL
                            ? 'يجب اختيار عميل لتفعيل الدفع الجزئي'
                            : 'Select a customer to enable partial payment',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),

                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _amountController,
                    decoration: InputDecoration(
                      labelText:
                          _isPartialPayment
                              ? (localeProvider.isRTL
                                  ? 'المبلغ المدفوع'
                                  : 'Amount Paid')
                              : (localeProvider.isRTL
                                  ? 'المبلغ المدفوع'
                                  : 'Amount Paid'),
                      prefixIcon: const Icon(Icons.attach_money),
                      helperText:
                          _isPartialPayment
                              ? (localeProvider.isRTL
                                  ? 'يمكن أن يكون أقل من المجموع الكلي'
                                  : 'Can be less than total amount')
                              : null,
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return localeProvider.isRTL
                            ? 'أدخل المبلغ'
                            : 'Please enter the amount';
                      }
                      final amount = double.tryParse(value);
                      if (amount == null) {
                        return localeProvider.isRTL
                            ? 'أدخل مبلغ صحيح'
                            : 'Please enter a valid amount';
                      }
                      if (amount <= 0) {
                        return localeProvider.isRTL
                            ? 'المبلغ يجب أن يكون أكبر من صفر'
                            : 'Amount must be greater than zero';
                      }
                      if (!_isPartialPayment && amount < totalAmount) {
                        return localeProvider.isRTL
                            ? 'المبلغ يجب أن يكون على الأقل المجموع الكلي'
                            : 'Amount must be at least the total';
                      }
                      return null;
                    },
                    onChanged: (_) => _calculateChange(),
                  ),
                  const Spacer(),

                  // Invoice Action Buttons
                  if (cartProvider.itemCount > 0) ...[
                    Text(
                      localeProvider.isRTL
                          ? 'إجراءات الفاتورة'
                          : 'Invoice Actions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        // Preview Invoice Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () => _previewInvoice(context, localeProvider),
                            icon: const Icon(Icons.preview),
                            label: Text(
                              localeProvider.isRTL ? 'معاينة' : 'Preview',
                              style: const TextStyle(fontSize: 12),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Print Invoice Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () => _printInvoice(context, localeProvider),
                            icon: const Icon(Icons.print),
                            label: Text(
                              localeProvider.isRTL ? 'طباعة' : 'Print',
                              style: const TextStyle(fontSize: 12),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        // Share via WhatsApp Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () =>
                                    _shareViaWhatsApp(context, localeProvider),
                            icon: const Icon(
                              Icons.message,
                              color: Colors.green,
                            ),
                            label: Text(
                              'WhatsApp',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.green,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: const BorderSide(color: Colors.green),
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Share via Email Button
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed:
                                () => _shareViaEmail(context, localeProvider),
                            icon: const Icon(Icons.email, color: Colors.blue),
                            label: Text(
                              localeProvider.isRTL ? 'إيميل' : 'Email',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.blue,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: const BorderSide(color: Colors.blue),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Complete Sale Button
                  ElevatedButton(
                    onPressed:
                        _isProcessing
                            ? null
                            : (_isPartialPayment
                                ? (_amountPaid > 0 && _selectedCustomer != null
                                    ? _completeCheckout
                                    : null)
                                : (_amountPaid >= totalAmount
                                    ? _completeCheckout
                                    : null)),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor:
                          _isPartialPayment ? Colors.orange : Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isProcessing
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                            : Text(
                              _isPartialPayment
                                  ? (localeProvider.isRTL
                                      ? 'دفع جزئي'
                                      : 'Partial Payment')
                                  : (localeProvider.isRTL
                                      ? 'إتمام البيع'
                                      : 'Complete Sale'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),

                  // Show debt information for partial payment
                  if (_isPartialPayment &&
                      _amountPaid > 0 &&
                      _amountPaid < totalAmount)
                    Padding(
                      padding: const EdgeInsets.only(top: 12.0),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.shade200),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  localeProvider.isRTL
                                      ? 'المبلغ المدفوع:'
                                      : 'Amount Paid:',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  currencyProvider.formatCurrency(_amountPaid),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  localeProvider.isRTL
                                      ? 'المبلغ المتبقي:'
                                      : 'Remaining Amount:',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  currencyProvider.formatCurrency(
                                    totalAmount - _amountPaid,
                                  ),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _previewInvoice(BuildContext context, LocaleProvider localeProvider) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              localeProvider.isRTL ? 'معاينة الفاتورة' : 'Invoice Preview',
            ),
            content: Text(
              localeProvider.isRTL
                  ? 'ستتم إضافة معاينة الفاتورة قريباً'
                  : 'Invoice preview will be added soon',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localeProvider.isRTL ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  void _printInvoice(BuildContext context, LocaleProvider localeProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeProvider.isRTL
              ? 'ستتم إضافة ميزة الطباعة قريباً'
              : 'Print feature will be added soon',
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _shareViaWhatsApp(BuildContext context, LocaleProvider localeProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeProvider.isRTL
              ? 'ستتم إضافة مشاركة WhatsApp قريباً'
              : 'WhatsApp sharing will be added soon',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _shareViaEmail(BuildContext context, LocaleProvider localeProvider) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeProvider.isRTL
              ? 'ستتم إضافة مشاركة الإيميل قريباً'
              : 'Email sharing will be added soon',
        ),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
