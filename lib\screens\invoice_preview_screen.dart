import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/invoice_settings_provider.dart';
import 'package:pos_app/models/invoice_settings.dart';

class InvoicePreviewScreen extends StatelessWidget {
  final String invoiceId;
  final List<Map<String, dynamic>> items;
  final double total;
  final String customerName;
  final DateTime date;

  const InvoicePreviewScreen({
    super.key,
    required this.invoiceId,
    required this.items,
    required this.total,
    this.customerName = 'عميل',
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final invoiceSettings = Provider.of<InvoiceSettingsProvider>(context).settings;
    final isRtl = invoiceSettings.rtlDirection;

    return Scaffold(
      appBar: AppBar(
        title: const Text('معاينة الفاتورة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تعطيل الطباعة في هذا الإصدار')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم تعطيل مشاركة الفاتورة في هذا الإصدار')),
              );
            },
          ),
        ],
      ),
      body: Directionality(
        textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildInvoiceHeader(invoiceSettings),
              const SizedBox(height: 20),
              _buildInvoiceInfo(invoiceId, date, customerName),
              const SizedBox(height: 20),
              _buildItemsTable(items, invoiceSettings.currencySymbol),
              const SizedBox(height: 20),
              _buildTotalSection(total, invoiceSettings.currencySymbol),
              const SizedBox(height: 40),
              _buildFooter(invoiceSettings),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoiceHeader(InvoiceSettings settings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          settings.storeName,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          settings.storeAddress,
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          'هاتف: ${settings.phoneNumber}',
          style: const TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        const Divider(thickness: 2),
        const Text(
          'فاتورة مبيعات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildInvoiceInfo(String invoiceId, DateTime date, String customerName) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('رقم الفاتورة:'),
              Text(invoiceId),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('التاريخ:'),
              Text('${date.day}/${date.month}/${date.year}'),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('العميل:'),
              Text(customerName),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemsTable(List<Map<String, dynamic>> items, String currencySymbol) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          color: Colors.grey.shade200,
          child: Row(
            children: const [
              Expanded(
                flex: 1,
                child: Text(
                  '#',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 4,
                child: Text(
                  'المنتج',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'السعر',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  'الكمية',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  'المجموع',
                  style: TextStyle(fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: items.length,
          itemBuilder: (context, index) {
            final item = items[index];
            return Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey.shade300),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${index + 1}',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 4,
                    child: Text(item['name'] ?? ''),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${item['price']} $currencySymbol',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      '${item['quantity']}',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${(item['price'] * item['quantity']).toStringAsFixed(2)} $currencySymbol',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildTotalSection(double total, String currencySymbol) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(5),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'المجموع الكلي:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              Text(
                '$total $currencySymbol',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFooter(InvoiceSettings settings) {
    return Column(
      children: const [
        Divider(),
        SizedBox(height: 8),
        Text(
          'شكراً لتعاملكم معنا',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8),
        Text(
          'هذه الفاتورة صالحة لمدة 30 يوماً من تاريخ الإصدار',
          style: TextStyle(fontSize: 14),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
