import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/providers/dashboard_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/customer_provider.dart';
import 'package:pos_app/providers/invoice_provider.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/models/customer.dart';
import 'package:pos_app/models/invoice.dart';

class AdvancedReportsScreen extends StatefulWidget {
  const AdvancedReportsScreen({super.key});

  @override
  State<AdvancedReportsScreen> createState() => _AdvancedReportsScreenState();
}

class _AdvancedReportsScreenState extends State<AdvancedReportsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTimeRange? _selectedDateRange;
  final String _selectedCategory = 'All';
  final String _selectedPaymentStatus = 'All';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _selectedDateRange = DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 30)),
      end: DateTime.now(),
    );

    // Load all required data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DashboardProvider>(
        context,
        listen: false,
      ).loadDashboardData();
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
      Provider.of<CustomerProvider>(context, listen: false).loadCustomers();
      Provider.of<InvoiceProvider>(context, listen: false).loadInvoices();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'التقارير المتقدمة' : 'Advanced Reports'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(Icons.filter_list),
            tooltip: isRTL ? 'تصفية' : 'Filter',
          ),
          IconButton(
            onPressed: _exportReports,
            icon: const Icon(Icons.download),
            tooltip: isRTL ? 'تصدير' : 'Export',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              icon: const FaIcon(FontAwesomeIcons.chartLine, size: 16),
              text: isRTL ? 'المبيعات' : 'Sales',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.boxesStacked, size: 16),
              text: isRTL ? 'المخزون' : 'Inventory',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.userGroup, size: 16),
              text: isRTL ? 'العملاء' : 'Customers',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.chartPie, size: 16),
              text: isRTL ? 'الأرباح' : 'Profits',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSalesReportTab(),
          _buildInventoryReportTab(),
          _buildCustomersReportTab(),
          _buildProfitsReportTab(),
        ],
      ),
    );
  }

  Widget _buildSalesReportTab() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeSelector(isRTL, theme),
          const SizedBox(height: 24),
          _buildSalesOverviewCards(isRTL, theme),
          const SizedBox(height: 24),
          _buildSalesChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildTopProductsChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildSalesTable(isRTL, theme),
        ],
      ),
    );
  }

  Widget _buildInventoryReportTab() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInventoryOverviewCards(isRTL, theme),
          const SizedBox(height: 24),
          _buildStockLevelsChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildCategoryDistributionChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildLowStockTable(isRTL, theme),
        ],
      ),
    );
  }

  Widget _buildCustomersReportTab() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCustomerOverviewCards(isRTL, theme),
          const SizedBox(height: 24),
          _buildCustomerGrowthChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildTopCustomersChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildCustomerDebtTable(isRTL, theme),
        ],
      ),
    );
  }

  Widget _buildProfitsReportTab() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProfitOverviewCards(isRTL, theme),
          const SizedBox(height: 24),
          _buildProfitTrendChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildProfitMarginChart(isRTL, theme),
          const SizedBox(height: 24),
          _buildExpensesBreakdownChart(isRTL, theme),
        ],
      ),
    );
  }

  Widget _buildDateRangeSelector(bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(Icons.date_range, color: theme.colorScheme.primary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isRTL ? 'فترة التقرير' : 'Report Period',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    _selectedDateRange != null
                        ? '${_formatDate(_selectedDateRange!.start)} - ${_formatDate(_selectedDateRange!.end)}'
                        : isRTL
                        ? 'اختر الفترة'
                        : 'Select Period',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                ],
              ),
            ),
            TextButton(
              onPressed: _selectDateRange,
              child: Text(isRTL ? 'تغيير' : 'Change'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesOverviewCards(bool isRTL, ThemeData theme) {
    return Consumer2<DashboardProvider, CurrencyProvider>(
      builder: (context, dashboard, currency, _) {
        final stats = dashboard.stats;
        if (stats == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          children: [
            _buildMetricCard(
              title: isRTL ? 'إجمالي المبيعات' : 'Total Sales',
              value: currency.formatCurrency(stats.monthlySales),
              icon: FontAwesomeIcons.coins,
              color: Colors.green,
              trend: '+12.5%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'عدد الفواتير' : 'Total Invoices',
              value: '${stats.totalInvoices}',
              icon: FontAwesomeIcons.receipt,
              color: Colors.blue,
              trend: '+8.3%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'متوسط الفاتورة' : 'Average Invoice',
              value: currency.formatCurrency(
                stats.totalInvoices > 0
                    ? stats.monthlySales / stats.totalInvoices
                    : 0,
              ),
              icon: FontAwesomeIcons.calculator,
              color: Colors.orange,
              trend: '+5.7%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'المبيعات اليومية' : 'Daily Sales',
              value: currency.formatCurrency(stats.dailySales),
              icon: FontAwesomeIcons.chartLine,
              color: Colors.purple,
              trend: '+15.2%',
              isRTL: isRTL,
              theme: theme,
            ),
          ],
        );
      },
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String trend,
    required bool isRTL,
    required ThemeData theme,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                FaIcon(icon, color: color, size: 24),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    trend,
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesChart(bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRTL ? 'اتجاه المبيعات' : 'Sales Trend',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 300,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _generateSalesData(),
                      isCurved: true,
                      color: theme.colorScheme.primary,
                      barWidth: 3,
                      dotData: FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProductsChart(bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRTL ? 'أفضل المنتجات مبيعاً' : 'Top Selling Products',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 300,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: 100,
                  barTouchData: BarTouchData(enabled: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          const products = [
                            'منتج 1',
                            'منتج 2',
                            'منتج 3',
                            'منتج 4',
                            'منتج 5',
                          ];
                          if (value.toInt() < products.length) {
                            return Text(
                              products[value.toInt()],
                              style: const TextStyle(fontSize: 12),
                            );
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  barGroups: _generateTopProductsData(theme),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesTable(bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isRTL ? 'تفاصيل المبيعات' : 'Sales Details',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton.icon(
                  onPressed: _exportSalesData,
                  icon: const Icon(Icons.download, size: 16),
                  label: Text(isRTL ? 'تصدير' : 'Export'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: [
                  DataColumn(label: Text(isRTL ? 'التاريخ' : 'Date')),
                  DataColumn(label: Text(isRTL ? 'رقم الفاتورة' : 'Invoice #')),
                  DataColumn(label: Text(isRTL ? 'العميل' : 'Customer')),
                  DataColumn(label: Text(isRTL ? 'المبلغ' : 'Amount')),
                  DataColumn(label: Text(isRTL ? 'الحالة' : 'Status')),
                ],
                rows: _generateSalesTableData(isRTL),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Inventory Report Methods
  Widget _buildInventoryOverviewCards(bool isRTL, ThemeData theme) {
    return Consumer2<ProductProvider, CurrencyProvider>(
      builder: (context, productProvider, currency, _) {
        final products = productProvider.products;
        final totalProducts = products.length;
        final lowStockProducts = products.where((p) => p.stock <= 10).length;
        final totalValue = products.fold<double>(
          0,
          (sum, p) => sum + (p.price * p.stock),
        );
        final totalCostValue = products.fold<double>(
          0,
          (sum, p) => sum + (p.costPrice * p.stock),
        );

        return GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          children: [
            _buildMetricCard(
              title: isRTL ? 'إجمالي المنتجات' : 'Total Products',
              value: '$totalProducts',
              icon: FontAwesomeIcons.box,
              color: Colors.blue,
              trend: '+5.2%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'منتجات قليلة المخزون' : 'Low Stock Items',
              value: '$lowStockProducts',
              icon: FontAwesomeIcons.triangleExclamation,
              color: Colors.red,
              trend: '-12.3%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'قيمة المخزون' : 'Inventory Value',
              value: currency.formatCurrency(totalValue),
              icon: FontAwesomeIcons.coins,
              color: Colors.green,
              trend: '+8.7%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'قيمة التكلفة' : 'Cost Value',
              value: currency.formatCurrency(totalCostValue),
              icon: FontAwesomeIcons.calculator,
              color: Colors.orange,
              trend: '+3.1%',
              isRTL: isRTL,
              theme: theme,
            ),
          ],
        );
      },
    );
  }

  Widget _buildStockLevelsChart(bool isRTL, ThemeData theme) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, _) {
        final products = productProvider.products;

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'مستويات المخزون' : 'Stock Levels',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child: BarChart(
                    BarChartData(
                      alignment: BarChartAlignment.spaceAround,
                      maxY:
                          products.isNotEmpty
                              ? products
                                      .map((p) => p.stock.toDouble())
                                      .reduce((a, b) => a > b ? a : b) +
                                  20
                              : 100,
                      barTouchData: BarTouchData(enabled: true),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              if (value.toInt() < products.length) {
                                return Text(
                                  products[value.toInt()].name.length > 8
                                      ? '${products[value.toInt()].name.substring(0, 8)}...'
                                      : products[value.toInt()].name,
                                  style: const TextStyle(fontSize: 10),
                                );
                              }
                              return const Text('');
                            },
                          ),
                        ),
                        rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(show: true),
                      barGroups: _generateStockLevelsData(products, theme),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCategoryDistributionChart(bool isRTL, ThemeData theme) {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, _) {
        final products = productProvider.products;
        final categoryData = _getCategoryDistribution(products);

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'توزيع الفئات' : 'Category Distribution',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child:
                      categoryData.isNotEmpty
                          ? PieChart(
                            PieChartData(
                              sections: _generateCategoryPieData(
                                categoryData,
                                theme,
                              ),
                              centerSpaceRadius: 40,
                              sectionsSpace: 2,
                            ),
                          )
                          : Center(
                            child: Text(
                              isRTL ? 'لا توجد بيانات' : 'No data available',
                              style: theme.textTheme.bodyLarge,
                            ),
                          ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLowStockTable(bool isRTL, ThemeData theme) {
    return Consumer2<ProductProvider, CurrencyProvider>(
      builder: (context, productProvider, currency, _) {
        final lowStockProducts =
            productProvider.products.where((p) => p.stock <= 10).toList();

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isRTL ? 'المنتجات قليلة المخزون' : 'Low Stock Products',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${lowStockProducts.length} ${isRTL ? 'منتج' : 'items'}',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (lowStockProducts.isEmpty)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        children: [
                          FaIcon(
                            FontAwesomeIcons.circleCheck,
                            size: 60,
                            color: Colors.green.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            isRTL
                                ? 'جميع المنتجات لديها مخزون كافي'
                                : 'All products have sufficient stock',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columns: [
                        DataColumn(label: Text(isRTL ? 'المنتج' : 'Product')),
                        DataColumn(label: Text(isRTL ? 'الفئة' : 'Category')),
                        DataColumn(
                          label: Text(
                            isRTL ? 'المخزون الحالي' : 'Current Stock',
                          ),
                        ),
                        DataColumn(label: Text(isRTL ? 'السعر' : 'Price')),
                        DataColumn(label: Text(isRTL ? 'الإجراء' : 'Action')),
                      ],
                      rows:
                          lowStockProducts
                              .map(
                                (product) => DataRow(
                                  cells: [
                                    DataCell(Text(product.name)),
                                    DataCell(Text(product.category)),
                                    DataCell(
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              product.stock <= 5
                                                  ? Colors.red.withValues(
                                                    alpha: 0.2,
                                                  )
                                                  : Colors.orange.withValues(
                                                    alpha: 0.2,
                                                  ),
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                        child: Text(
                                          '${product.stock}',
                                          style: TextStyle(
                                            color:
                                                product.stock <= 5
                                                    ? Colors.red.shade700
                                                    : Colors.orange.shade700,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataCell(
                                      Text(
                                        currency.formatCurrency(product.price),
                                      ),
                                    ),
                                    DataCell(
                                      ElevatedButton.icon(
                                        onPressed: () {
                                          // TODO: Navigate to restock screen
                                          ScaffoldMessenger.of(
                                            context,
                                          ).showSnackBar(
                                            SnackBar(
                                              content: Text(
                                                isRTL
                                                    ? 'إعادة تخزين ${product.name}'
                                                    : 'Restock ${product.name}',
                                              ),
                                            ),
                                          );
                                        },
                                        icon: const FaIcon(
                                          FontAwesomeIcons.plus,
                                          size: 12,
                                        ),
                                        label: Text(
                                          isRTL ? 'إعادة تخزين' : 'Restock',
                                        ),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              theme.colorScheme.primary,
                                          foregroundColor: Colors.white,
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 8,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                              .toList(),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Customer Report Methods
  Widget _buildCustomerOverviewCards(bool isRTL, ThemeData theme) {
    return Consumer3<CustomerProvider, InvoiceProvider, CurrencyProvider>(
      builder: (context, customerProvider, invoiceProvider, currency, _) {
        final customers = customerProvider.customers;
        final invoices = invoiceProvider.invoices;
        final totalCustomers = customers.length;
        final activeCustomers = _getActiveCustomers(customers, invoices);
        final totalDebt = _getTotalCustomerDebt(customers, invoices);
        final avgOrderValue = _getAverageOrderValue(invoices);

        return GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          children: [
            _buildMetricCard(
              title: isRTL ? 'إجمالي العملاء' : 'Total Customers',
              value: '$totalCustomers',
              icon: FontAwesomeIcons.users,
              color: Colors.blue,
              trend: '+12.5%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'العملاء النشطون' : 'Active Customers',
              value: '$activeCustomers',
              icon: FontAwesomeIcons.userCheck,
              color: Colors.green,
              trend: '+8.3%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'إجمالي الديون' : 'Total Debt',
              value: currency.formatCurrency(totalDebt),
              icon: FontAwesomeIcons.handHoldingDollar,
              color: Colors.red,
              trend: '-5.2%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'متوسط قيمة الطلب' : 'Avg Order Value',
              value: currency.formatCurrency(avgOrderValue),
              icon: FontAwesomeIcons.chartLine,
              color: Colors.purple,
              trend: '+15.7%',
              isRTL: isRTL,
              theme: theme,
            ),
          ],
        );
      },
    );
  }

  Widget _buildCustomerGrowthChart(bool isRTL, ThemeData theme) {
    return Consumer<CustomerProvider>(
      builder: (context, customerProvider, _) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'نمو العملاء' : 'Customer Growth',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child: LineChart(
                    LineChartData(
                      gridData: FlGridData(show: true),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              const months = [
                                'Jan',
                                'Feb',
                                'Mar',
                                'Apr',
                                'May',
                                'Jun',
                              ];
                              if (value.toInt() < months.length) {
                                return Text(months[value.toInt()]);
                              }
                              return const Text('');
                            },
                          ),
                        ),
                        rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(show: true),
                      lineBarsData: [
                        LineChartBarData(
                          spots: _generateCustomerGrowthData(),
                          isCurved: true,
                          color: theme.colorScheme.primary,
                          barWidth: 3,
                          dotData: FlDotData(show: true),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopCustomersChart(bool isRTL, ThemeData theme) {
    return Consumer3<CustomerProvider, InvoiceProvider, CurrencyProvider>(
      builder: (context, customerProvider, invoiceProvider, currency, _) {
        final topCustomers = _getTopCustomers(
          customerProvider.customers,
          invoiceProvider.invoices,
        );

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'أفضل العملاء' : 'Top Customers',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child:
                      topCustomers.isNotEmpty
                          ? BarChart(
                            BarChartData(
                              alignment: BarChartAlignment.spaceAround,
                              maxY:
                                  topCustomers.isNotEmpty
                                      ? topCustomers.first['amount'] + 1000
                                      : 1000,
                              barTouchData: BarTouchData(enabled: true),
                              titlesData: FlTitlesData(
                                leftTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: true),
                                ),
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(
                                    showTitles: true,
                                    getTitlesWidget: (value, meta) {
                                      if (value.toInt() < topCustomers.length) {
                                        final name =
                                            topCustomers[value.toInt()]['name']
                                                as String;
                                        return Text(
                                          name.length > 8
                                              ? '${name.substring(0, 8)}...'
                                              : name,
                                          style: const TextStyle(fontSize: 10),
                                        );
                                      }
                                      return const Text('');
                                    },
                                  ),
                                ),
                                rightTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                topTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                              ),
                              borderData: FlBorderData(show: true),
                              barGroups: _generateTopCustomersData(
                                topCustomers,
                                theme,
                              ),
                            ),
                          )
                          : Center(
                            child: Text(
                              isRTL ? 'لا توجد بيانات' : 'No data available',
                              style: theme.textTheme.bodyLarge,
                            ),
                          ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomerDebtTable(bool isRTL, ThemeData theme) {
    return Consumer3<CustomerProvider, InvoiceProvider, CurrencyProvider>(
      builder: (context, customerProvider, invoiceProvider, currency, _) {
        final customersWithDebt = _getCustomersWithDebt(
          customerProvider.customers,
          invoiceProvider.invoices,
        );

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      isRTL ? 'ديون العملاء' : 'Customer Debts',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${customersWithDebt.length} ${isRTL ? 'عميل' : 'customers'}',
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (customersWithDebt.isEmpty)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(40),
                      child: Column(
                        children: [
                          FaIcon(
                            FontAwesomeIcons.circleCheck,
                            size: 60,
                            color: Colors.green.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            isRTL
                                ? 'لا توجد ديون معلقة'
                                : 'No outstanding debts',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: Colors.green.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columns: [
                        DataColumn(label: Text(isRTL ? 'العميل' : 'Customer')),
                        DataColumn(label: Text(isRTL ? 'الهاتف' : 'Phone')),
                        DataColumn(
                          label: Text(isRTL ? 'مبلغ الدين' : 'Debt Amount'),
                        ),
                        DataColumn(
                          label: Text(isRTL ? 'عدد الفواتير' : 'Invoices'),
                        ),
                        DataColumn(label: Text(isRTL ? 'الإجراء' : 'Action')),
                      ],
                      rows:
                          customersWithDebt.map((customerData) {
                            final customer = customerData['customer'];
                            final debtAmount =
                                customerData['debtAmount'] as double;
                            final invoiceCount =
                                customerData['invoiceCount'] as int;

                            return DataRow(
                              cells: [
                                DataCell(Text(customer.name)),
                                DataCell(Text(customer.phone)),
                                DataCell(
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.red.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Text(
                                      currency.formatCurrency(debtAmount),
                                      style: TextStyle(
                                        color: Colors.red.shade700,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                DataCell(Text('$invoiceCount')),
                                DataCell(
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      // TODO: Navigate to customer details
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            isRTL
                                                ? 'عرض تفاصيل ${customer.name}'
                                                : 'View ${customer.name} details',
                                          ),
                                        ),
                                      );
                                    },
                                    icon: const FaIcon(
                                      FontAwesomeIcons.eye,
                                      size: 12,
                                    ),
                                    label: Text(isRTL ? 'عرض' : 'View'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          theme.colorScheme.primary,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }).toList(),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Profit Report Methods
  Widget _buildProfitOverviewCards(bool isRTL, ThemeData theme) {
    return Consumer2<InvoiceProvider, CurrencyProvider>(
      builder: (context, invoiceProvider, currency, _) {
        final invoices = invoiceProvider.invoices;
        final totalRevenue = _getTotalRevenue(invoices);
        final totalCost = _getTotalCost(invoices);
        final totalProfit = totalRevenue - totalCost;
        final profitMargin =
            totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

        return GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          childAspectRatio: 1.5,
          children: [
            _buildMetricCard(
              title: isRTL ? 'إجمالي الإيرادات' : 'Total Revenue',
              value: currency.formatCurrency(totalRevenue),
              icon: FontAwesomeIcons.chartLine,
              color: Colors.green,
              trend: '+18.5%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'إجمالي التكلفة' : 'Total Cost',
              value: currency.formatCurrency(totalCost),
              icon: FontAwesomeIcons.calculator,
              color: Colors.red,
              trend: '+12.3%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'صافي الربح' : 'Net Profit',
              value: currency.formatCurrency(totalProfit),
              icon: FontAwesomeIcons.coins,
              color: Colors.blue,
              trend: '+25.7%',
              isRTL: isRTL,
              theme: theme,
            ),
            _buildMetricCard(
              title: isRTL ? 'هامش الربح' : 'Profit Margin',
              value: '${profitMargin.toStringAsFixed(1)}%',
              icon: FontAwesomeIcons.percent,
              color: Colors.purple,
              trend: '+3.2%',
              isRTL: isRTL,
              theme: theme,
            ),
          ],
        );
      },
    );
  }

  Widget _buildProfitTrendChart(bool isRTL, ThemeData theme) {
    return Consumer<InvoiceProvider>(
      builder: (context, invoiceProvider, _) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'اتجاه الأرباح' : 'Profit Trend',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child: LineChart(
                    LineChartData(
                      gridData: FlGridData(show: true),
                      titlesData: FlTitlesData(
                        leftTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: true),
                        ),
                        bottomTitles: AxisTitles(
                          sideTitles: SideTitles(
                            showTitles: true,
                            getTitlesWidget: (value, meta) {
                              const months = [
                                'Jan',
                                'Feb',
                                'Mar',
                                'Apr',
                                'May',
                                'Jun',
                              ];
                              if (value.toInt() < months.length) {
                                return Text(months[value.toInt()]);
                              }
                              return const Text('');
                            },
                          ),
                        ),
                        rightTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                        topTitles: AxisTitles(
                          sideTitles: SideTitles(showTitles: false),
                        ),
                      ),
                      borderData: FlBorderData(show: true),
                      lineBarsData: [
                        LineChartBarData(
                          spots: _generateProfitTrendData(),
                          isCurved: true,
                          color: Colors.green,
                          barWidth: 3,
                          dotData: FlDotData(show: true),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfitMarginChart(bool isRTL, ThemeData theme) {
    return Consumer<InvoiceProvider>(
      builder: (context, invoiceProvider, _) {
        final profitMarginData = _getProfitMarginData(invoiceProvider.invoices);

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'هامش الربح حسب الفئة' : 'Profit Margin by Category',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child:
                      profitMarginData.isNotEmpty
                          ? PieChart(
                            PieChartData(
                              sections: _generateProfitMarginPieData(
                                profitMarginData,
                                theme,
                              ),
                              centerSpaceRadius: 40,
                              sectionsSpace: 2,
                            ),
                          )
                          : Center(
                            child: Text(
                              isRTL ? 'لا توجد بيانات' : 'No data available',
                              style: theme.textTheme.bodyLarge,
                            ),
                          ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildExpensesBreakdownChart(bool isRTL, ThemeData theme) {
    return Consumer<InvoiceProvider>(
      builder: (context, invoiceProvider, _) {
        final expensesData = _getExpensesBreakdown(invoiceProvider.invoices);

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isRTL ? 'تفصيل المصروفات' : 'Expenses Breakdown',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  height: 300,
                  child:
                      expensesData.isNotEmpty
                          ? BarChart(
                            BarChartData(
                              alignment: BarChartAlignment.spaceAround,
                              maxY:
                                  expensesData.isNotEmpty
                                      ? expensesData
                                              .map((e) => e['amount'] as double)
                                              .reduce((a, b) => a > b ? a : b) +
                                          1000
                                      : 1000,
                              barTouchData: BarTouchData(enabled: true),
                              titlesData: FlTitlesData(
                                leftTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: true),
                                ),
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(
                                    showTitles: true,
                                    getTitlesWidget: (value, meta) {
                                      if (value.toInt() < expensesData.length) {
                                        final category =
                                            expensesData[value
                                                    .toInt()]['category']
                                                as String;
                                        return Text(
                                          category.length > 8
                                              ? '${category.substring(0, 8)}...'
                                              : category,
                                          style: const TextStyle(fontSize: 10),
                                        );
                                      }
                                      return const Text('');
                                    },
                                  ),
                                ),
                                rightTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                topTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                              ),
                              borderData: FlBorderData(show: true),
                              barGroups: _generateExpensesBarData(
                                expensesData,
                                theme,
                              ),
                            ),
                          )
                          : Center(
                            child: Text(
                              isRTL ? 'لا توجد بيانات' : 'No data available',
                              style: theme.textTheme.bodyLarge,
                            ),
                          ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper methods
  List<FlSpot> _generateSalesData() {
    return [
      const FlSpot(0, 3),
      const FlSpot(1, 1),
      const FlSpot(2, 4),
      const FlSpot(3, 2),
      const FlSpot(4, 5),
      const FlSpot(5, 3),
      const FlSpot(6, 6),
    ];
  }

  List<BarChartGroupData> _generateTopProductsData(ThemeData theme) {
    return [
      BarChartGroupData(
        x: 0,
        barRods: [BarChartRodData(toY: 80, color: theme.colorScheme.primary)],
      ),
      BarChartGroupData(
        x: 1,
        barRods: [BarChartRodData(toY: 65, color: theme.colorScheme.secondary)],
      ),
      BarChartGroupData(
        x: 2,
        barRods: [BarChartRodData(toY: 50, color: Colors.green)],
      ),
      BarChartGroupData(
        x: 3,
        barRods: [BarChartRodData(toY: 40, color: Colors.orange)],
      ),
      BarChartGroupData(
        x: 4,
        barRods: [BarChartRodData(toY: 30, color: Colors.red)],
      ),
    ];
  }

  List<DataRow> _generateSalesTableData(bool isRTL) {
    return [
      DataRow(
        cells: [
          DataCell(Text('2024/01/15')),
          DataCell(Text('INV-001')),
          DataCell(Text('أحمد محمد')),
          DataCell(Text('1,250 دج')),
          DataCell(
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                isRTL ? 'مدفوع' : 'Paid',
                style: const TextStyle(color: Colors.green, fontSize: 12),
              ),
            ),
          ),
        ],
      ),
      // Add more rows as needed
    ];
  }

  void _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _selectedDateRange,
    );
    if (picked != null) {
      setState(() {
        _selectedDateRange = picked;
      });
    }
  }

  void _showFilterDialog() {
    // Implementation for filter dialog
  }

  void _exportReports() {
    // Implementation for export functionality
  }

  void _exportSalesData() {
    // Implementation for sales data export
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Helper methods for inventory reports
  List<BarChartGroupData> _generateStockLevelsData(
    List<Product> products,
    ThemeData theme,
  ) {
    return products.take(10).toList().asMap().entries.map((entry) {
      final index = entry.key;
      final product = entry.value;
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: product.stock.toDouble(),
            color: product.stock <= 10 ? Colors.red : theme.colorScheme.primary,
            width: 20,
          ),
        ],
      );
    }).toList();
  }

  Map<String, int> _getCategoryDistribution(List<Product> products) {
    final Map<String, int> distribution = {};
    for (final product in products) {
      distribution[product.category] =
          (distribution[product.category] ?? 0) + 1;
    }
    return distribution;
  }

  List<PieChartSectionData> _generateCategoryPieData(
    Map<String, int> categoryData,
    ThemeData theme,
  ) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
    ];

    return categoryData.entries.toList().asMap().entries.map((entry) {
      final index = entry.key;
      final categoryEntry = entry.value;
      return PieChartSectionData(
        color: colors[index % colors.length],
        value: categoryEntry.value.toDouble(),
        title: '${categoryEntry.key}\n${categoryEntry.value}',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  // Helper methods for customer reports
  int _getActiveCustomers(List<Customer> customers, List<Invoice> invoices) {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 30));

    final activeCustomerIds =
        invoices
            .where((invoice) => invoice.date.isAfter(thirtyDaysAgo))
            .map((invoice) => invoice.customerId)
            .toSet();

    return activeCustomerIds.length;
  }

  double _getTotalCustomerDebt(
    List<Customer> customers,
    List<Invoice> invoices,
  ) {
    return invoices
        .where((invoice) => invoice.status.toLowerCase() != 'paid')
        .fold<double>(0, (sum, invoice) => sum + invoice.finalAmount);
  }

  double _getAverageOrderValue(List<Invoice> invoices) {
    if (invoices.isEmpty) return 0;
    final totalValue = invoices.fold<double>(
      0,
      (sum, invoice) => sum + invoice.finalAmount,
    );
    return totalValue / invoices.length;
  }

  List<FlSpot> _generateCustomerGrowthData() {
    return [
      const FlSpot(0, 10),
      const FlSpot(1, 15),
      const FlSpot(2, 22),
      const FlSpot(3, 18),
      const FlSpot(4, 28),
      const FlSpot(5, 35),
    ];
  }

  List<Map<String, dynamic>> _getTopCustomers(
    List<Customer> customers,
    List<Invoice> invoices,
  ) {
    final Map<int, double> customerTotals = {};

    for (final invoice in invoices) {
      final customerId = invoice.customerId;
      if (customerId != null) {
        customerTotals[customerId] =
            (customerTotals[customerId] ?? 0) + invoice.finalAmount;
      }
    }

    final topCustomers = <Map<String, dynamic>>[];
    for (final customer in customers) {
      final customerId = customer.id;
      if (customerId == null) continue;
      final total = customerTotals[customerId] ?? 0;
      if (total > 0) {
        topCustomers.add({'name': customer.name, 'amount': total});
      }
    }

    topCustomers.sort(
      (a, b) => (b['amount'] as double).compareTo(a['amount'] as double),
    );
    return topCustomers.take(5).toList();
  }

  List<BarChartGroupData> _generateTopCustomersData(
    List<Map<String, dynamic>> topCustomers,
    ThemeData theme,
  ) {
    return topCustomers.asMap().entries.map((entry) {
      final index = entry.key;
      final customer = entry.value;
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: customer['amount'] as double,
            color: theme.colorScheme.primary,
            width: 20,
          ),
        ],
      );
    }).toList();
  }

  List<Map<String, dynamic>> _getCustomersWithDebt(
    List<Customer> customers,
    List<Invoice> invoices,
  ) {
    final customersWithDebt = <Map<String, dynamic>>[];

    for (final customer in customers) {
      final customerId = customer.id;
      if (customerId == null) continue;

      final unpaidInvoices =
          invoices
              .where(
                (invoice) =>
                    invoice.customerId == customerId &&
                    invoice.status.toLowerCase() != 'paid',
              )
              .toList();

      if (unpaidInvoices.isNotEmpty) {
        final debtAmount = unpaidInvoices.fold<double>(
          0,
          (sum, invoice) => sum + invoice.finalAmount,
        );

        customersWithDebt.add({
          'customer': customer,
          'debtAmount': debtAmount,
          'invoiceCount': unpaidInvoices.length,
        });
      }
    }

    customersWithDebt.sort(
      (a, b) =>
          (b['debtAmount'] as double).compareTo(a['debtAmount'] as double),
    );

    return customersWithDebt;
  }

  // Helper methods for profit reports
  double _getTotalRevenue(List<Invoice> invoices) {
    return invoices.fold<double>(
      0,
      (sum, invoice) => sum + invoice.finalAmount,
    );
  }

  double _getTotalCost(List<Invoice> invoices) {
    // Assuming 70% of revenue is cost (30% profit margin)
    return _getTotalRevenue(invoices) * 0.7;
  }

  List<FlSpot> _generateProfitTrendData() {
    return [
      const FlSpot(0, 1000),
      const FlSpot(1, 1500),
      const FlSpot(2, 1200),
      const FlSpot(3, 1800),
      const FlSpot(4, 2200),
      const FlSpot(5, 2500),
    ];
  }

  Map<String, double> _getProfitMarginData(List<Invoice> invoices) {
    // Sample data for profit margins by category
    return {'Beverages': 25.5, 'Food': 18.2, 'Snacks': 32.1, 'Bakery': 22.8};
  }

  List<PieChartSectionData> _generateProfitMarginPieData(
    Map<String, double> profitMarginData,
    ThemeData theme,
  ) {
    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple];

    return profitMarginData.entries.toList().asMap().entries.map((entry) {
      final index = entry.key;
      final marginEntry = entry.value;
      return PieChartSectionData(
        color: colors[index % colors.length],
        value: marginEntry.value,
        title: '${marginEntry.key}\n${marginEntry.value.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  List<Map<String, dynamic>> _getExpensesBreakdown(List<Invoice> invoices) {
    // Sample expenses data
    return [
      {'category': 'Rent', 'amount': 5000.0},
      {'category': 'Utilities', 'amount': 1500.0},
      {'category': 'Supplies', 'amount': 2500.0},
      {'category': 'Marketing', 'amount': 1000.0},
      {'category': 'Staff', 'amount': 8000.0},
    ];
  }

  List<BarChartGroupData> _generateExpensesBarData(
    List<Map<String, dynamic>> expensesData,
    ThemeData theme,
  ) {
    return expensesData.asMap().entries.map((entry) {
      final index = entry.key;
      final expense = entry.value;
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: expense['amount'] as double,
            color: Colors.red.shade400,
            width: 20,
          ),
        ],
      );
    }).toList();
  }
}
