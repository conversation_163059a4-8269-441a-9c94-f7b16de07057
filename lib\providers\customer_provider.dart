import 'package:flutter/foundation.dart';
import 'package:pos_app/db/database_helper.dart';
import 'package:pos_app/models/customer.dart';

class CustomerProvider with ChangeNotifier {
  List<Customer> _customers = [];
  final DatabaseHelper dbHelper = DatabaseHelper.instance;
  bool _isLoaded = false;
  bool _isLoading = false;

  List<Customer> get customers => _customers;
  bool get isLoading => _isLoading;

  Future<void> loadCustomers() async {
    if (_isLoaded) return;

    _isLoading = true;
    notifyListeners();

    if (kIsWeb) {
      // Use sample data for web
      _customers = [
        Customer(
          id: 1,
          name: '<PERSON>',
          phone: '**********',
          email: '<EMAIL>',
          address: 'Algiers, Algeria',
          balance: 1500.0,
        ),
        Customer(
          id: 2,
          name: '<PERSON><PERSON>',
          phone: '**********',
          email: '<EMAIL>',
          address: 'Oran, Algeria',
          balance: 750.0,
        ),
        Customer(
          id: 3,
          name: '<PERSON>',
          phone: '**********',
          email: '<EMAIL>',
          address: 'Constantine, Algeria',
          balance: 2200.0,
        ),
      ];
    } else {
      try {
        final db = await dbHelper.database;
        final result = await db.query('customers');
        _customers = result.map((json) => Customer.fromMap(json)).toList();
      } catch (e) {
        print('Error loading customers: $e');
      }
    }

    _isLoaded = true;
    _isLoading = false;
    notifyListeners();
  }

  Future<Customer?> getCustomerById(int id) async {
    if (kIsWeb) {
      return _customers.firstWhere((customer) => customer.id == id);
    } else {
      try {
        final db = await dbHelper.database;
        final maps = await db.query(
          'customers',
          where: 'id = ?',
          whereArgs: [id],
        );

        if (maps.isNotEmpty) {
          return Customer.fromMap(maps.first);
        }
      } catch (e) {
        print('Error getting customer by ID: $e');
      }
    }
    return null;
  }

  Future<int> addCustomer(Customer customer) async {
    if (kIsWeb) {
      // Generate a new ID for web
      final newId = _customers.isEmpty ? 1 : (_customers.last.id ?? 0) + 1;
      final newCustomer = Customer(
        id: newId,
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        address: customer.address,
        balance: customer.balance,
        notes: customer.notes,
      );

      _customers.add(newCustomer);
      notifyListeners();
      return newId;
    } else {
      try {
        final db = await dbHelper.database;
        final id = await db.insert('customers', customer.toMap());
        final newCustomer = customer.copyWith(id: id);
        _customers.add(newCustomer);
        notifyListeners();
        return id;
      } catch (e) {
        print('Error adding customer: $e');
        return -1;
      }
    }
  }

  Future<bool> updateCustomer(Customer customer) async {
    if (kIsWeb) {
      final index = _customers.indexWhere((c) => c.id == customer.id);
      if (index != -1) {
        _customers[index] = customer;
        notifyListeners();
        return true;
      }
      return false;
    } else {
      try {
        final db = await dbHelper.database;
        final result = await db.update(
          'customers',
          customer.toMap(),
          where: 'id = ?',
          whereArgs: [customer.id],
        );

        if (result > 0) {
          final index = _customers.indexWhere((c) => c.id == customer.id);
          if (index != -1) {
            _customers[index] = customer;
            notifyListeners();
          }
          return true;
        }
        return false;
      } catch (e) {
        print('Error updating customer: $e');
        return false;
      }
    }
  }

  Future<bool> deleteCustomer(int id) async {
    if (kIsWeb) {
      _customers.removeWhere((customer) => customer.id == id);
      notifyListeners();
      return true;
    } else {
      try {
        final db = await dbHelper.database;
        final result = await db.delete(
          'customers',
          where: 'id = ?',
          whereArgs: [id],
        );

        if (result > 0) {
          _customers.removeWhere((customer) => customer.id == id);
          notifyListeners();
          return true;
        }
        return false;
      } catch (e) {
        print('Error deleting customer: $e');
        return false;
      }
    }
  }

  // Get customers with debt (balance > 0)
  List<Customer> get customersWithDebt {
    return _customers.where((customer) => customer.balance > 0).toList();
  }

  // Refresh customers data
  Future<void> refreshCustomers() async {
    _isLoaded = false;
    await loadCustomers();
  }
}
