<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="deviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFC107;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="syncGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Desktop Computer -->
  <rect x="16" y="32" width="48" height="32" rx="4" fill="url(#deviceGradient)"/>
  <rect x="20" y="36" width="40" height="24" rx="2" fill="#263238"/>
  <rect x="32" y="64" width="16" height="8" rx="2" fill="#FF8F00"/>
  <rect x="28" y="72" width="24" height="4" rx="2" fill="#FF8F00"/>
  
  <!-- Tablet -->
  <rect x="72" y="16" width="32" height="48" rx="6" fill="url(#deviceGradient)"/>
  <rect x="76" y="24" width="24" height="32" rx="2" fill="#263238"/>
  <circle cx="88" cy="60" r="2" fill="#FF8F00"/>
  
  <!-- Mobile Phone -->
  <rect x="48" y="72" width="20" height="36" rx="4" fill="url(#deviceGradient)"/>
  <rect x="52" y="80" width="12" height="20" rx="1" fill="#263238"/>
  <circle cx="58" cy="104" r="1.5" fill="#FF8F00"/>
  <rect x="54" y="76" width="8" height="1" rx="0.5" fill="#FF8F00"/>
  
  <!-- Sync Arrows -->
  <path d="M40 48 Q56 40 72 48" stroke="url(#syncGradient)" stroke-width="3" fill="none" marker-end="url(#arrowhead1)"/>
  <path d="M88 64 Q80 80 58 88" stroke="url(#syncGradient)" stroke-width="3" fill="none" marker-end="url(#arrowhead2)"/>
  <path d="M48 88 Q32 80 40 64" stroke="url(#syncGradient)" stroke-width="3" fill="none" marker-end="url(#arrowhead3)"/>
  
  <!-- Arrow Markers -->
  <defs>
    <marker id="arrowhead1" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4CAF50"/>
    </marker>
    <marker id="arrowhead2" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4CAF50"/>
    </marker>
    <marker id="arrowhead3" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#4CAF50"/>
    </marker>
  </defs>
  
  <!-- Cloud Sync -->
  <path d="M96 88 C96 84 99 80 104 80 C109 80 112 84 112 88 C112 92 109 96 104 96 L100 96 C97 96 96 94 96 92 Z" fill="#E3F2FD"/>
  <path d="M104 84 L104 92 M100 88 L108 88" stroke="#2196F3" stroke-width="1.5"/>
  
  <!-- Sync Animation -->
  <circle cx="64" cy="64" r="24" stroke="url(#syncGradient)" stroke-width="2" fill="none" opacity="0.3">
    <animate attributeName="r" values="20;28;20" dur="3s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite"/>
  </circle>
</svg>
