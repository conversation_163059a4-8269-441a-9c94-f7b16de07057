import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/models/product.dart';

class InventoryScreen extends StatefulWidget {
  const InventoryScreen({super.key});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> {
  final _searchController = TextEditingController();
  String _selectedCategory = 'All';
  bool _showLowStockOnly = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Provider.of<ProductProvider>(context, listen: false).loadProducts();
        Provider.of<CategoryProvider>(context, listen: false).loadCategories();
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  List<Product> _getFilteredProducts(List<Product> products) {
    var filtered = products;

    // Filter by search query
    if (_searchController.text.isNotEmpty) {
      filtered =
          filtered
              .where(
                (product) =>
                    product.name.toLowerCase().contains(
                      _searchController.text.toLowerCase(),
                    ) ||
                    product.barcode.toLowerCase().contains(
                      _searchController.text.toLowerCase(),
                    ),
              )
              .toList();
    }

    // Filter by category
    if (_selectedCategory != 'All') {
      filtered =
          filtered
              .where((product) => product.category == _selectedCategory)
              .toList();
    }

    // Filter by low stock
    if (_showLowStockOnly) {
      filtered = filtered.where((product) => product.stock <= 10).toList();
    }

    return filtered;
  }

  void _updateStock(Product product, int newStock) async {
    try {
      final updatedProduct = product.copyWith(stock: newStock);
      await Provider.of<ProductProvider>(
        context,
        listen: false,
      ).updateProduct(updatedProduct);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Stock updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating stock: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showStockUpdateDialog(Product product, LocaleProvider localeProvider) {
    final stockController = TextEditingController(
      text: product.stock.toString(),
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              localeProvider.isRTL ? 'تحديث المخزون' : 'Update Stock',
              textDirection:
                  localeProvider.isRTL
                      ? ui.TextDirection.rtl
                      : ui.TextDirection.ltr,
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.name,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  textDirection:
                      localeProvider.isRTL
                          ? ui.TextDirection.rtl
                          : ui.TextDirection.ltr,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: stockController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText:
                        localeProvider.isRTL
                            ? 'الكمية الجديدة'
                            : 'New Quantity',
                    border: const OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  final newStock = int.tryParse(stockController.text);
                  if (newStock != null && newStock >= 0) {
                    _updateStock(product, newStock);
                    Navigator.of(context).pop();
                  }
                },
                child: Text(localeProvider.isRTL ? 'تحديث' : 'Update'),
              ),
            ],
          ),
    );
  }

  Widget _buildProductCard(Product product, LocaleProvider localeProvider) {
    final isLowStock = product.stock <= 10;
    final stockColor =
        product.stock == 0
            ? Colors.red
            : isLowStock
            ? Colors.orange
            : Colors.green;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Product Info
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textDirection:
                        localeProvider.isRTL
                            ? ui.TextDirection.rtl
                            : ui.TextDirection.ltr,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${localeProvider.isRTL ? 'الباركود:' : 'Barcode:'} ${product.barcode}',
                    style: TextStyle(color: Colors.grey.shade600),
                    textDirection:
                        localeProvider.isRTL
                            ? ui.TextDirection.rtl
                            : ui.TextDirection.ltr,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${localeProvider.isRTL ? 'الفئة:' : 'Category:'} ${product.category}',
                    style: TextStyle(color: Colors.grey.shade600),
                    textDirection:
                        localeProvider.isRTL
                            ? ui.TextDirection.rtl
                            : ui.TextDirection.ltr,
                  ),
                ],
              ),
            ),

            // Stock Info
            Expanded(
              flex: 2,
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: stockColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: stockColor),
                    ),
                    child: Column(
                      children: [
                        Text(
                          localeProvider.isRTL ? 'المخزون' : 'Stock',
                          style: TextStyle(
                            color: stockColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${product.stock}',
                          style: TextStyle(
                            color: stockColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed:
                        () => _showStockUpdateDialog(product, localeProvider),
                    icon: const Icon(Icons.edit, size: 16),
                    label: Text(
                      localeProvider.isRTL ? 'تحديث' : 'Update',
                      style: const TextStyle(fontSize: 12),
                    ),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<ProductProvider, CategoryProvider, LocaleProvider>(
      builder: (context, productProvider, categoryProvider, localeProvider, _) {
        final products = _getFilteredProducts(productProvider.products);
        final lowStockCount =
            productProvider.products.where((p) => p.stock <= 10).length;

        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            backgroundColor: Colors.white,
            foregroundColor: Colors.black87,
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back_ios,
                color: Colors.black87,
                textDirection:
                    localeProvider.isRTL
                        ? ui.TextDirection.rtl
                        : ui.TextDirection.ltr,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            title: Text(
              localeProvider.isRTL ? 'إدارة المخزون' : 'Inventory Management',
              style: TextStyle(
                color: Colors.black87,
                fontWeight: FontWeight.w600,
                fontSize: 20,
              ),
              textDirection:
                  localeProvider.isRTL
                      ? ui.TextDirection.rtl
                      : ui.TextDirection.ltr,
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh, color: Colors.black87),
                onPressed: () {
                  productProvider.loadProducts();
                },
              ),
            ],
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(1),
              child: Container(
                height: 1,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.grey.shade200,
                      Colors.grey.shade100,
                      Colors.grey.shade200,
                    ],
                  ),
                ),
              ),
            ),
          ),
          body: Column(
            children: [
              // Filters and Search
              Container(
                padding: const EdgeInsets.all(16),
                color: Colors.grey.shade50,
                child: Column(
                  children: [
                    // Search Bar
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText:
                            localeProvider.isRTL
                                ? 'البحث عن منتج...'
                                : 'Search products...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      onChanged: (value) => setState(() {}),
                    ),
                    const SizedBox(height: 12),

                    // Filters Row
                    Row(
                      children: [
                        // Category Filter
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _selectedCategory,
                            decoration: InputDecoration(
                              labelText:
                                  localeProvider.isRTL ? 'الفئة' : 'Category',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                            ),
                            items: [
                              DropdownMenuItem(
                                value: 'All',
                                child: Text(
                                  localeProvider.isRTL
                                      ? 'جميع الفئات'
                                      : 'All Categories',
                                ),
                              ),
                              ...categoryProvider.categories.map(
                                (category) => DropdownMenuItem(
                                  value: category.name,
                                  child: Text(category.name),
                                ),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value!;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 12),

                        // Low Stock Filter
                        Expanded(
                          child: CheckboxListTile(
                            title: Text(
                              localeProvider.isRTL
                                  ? 'مخزون قليل فقط'
                                  : 'Low Stock Only',
                              style: const TextStyle(fontSize: 14),
                            ),
                            value: _showLowStockOnly,
                            onChanged: (value) {
                              setState(() {
                                _showLowStockOnly = value!;
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Stats Banner
              if (lowStockCount > 0)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  color: Colors.orange.shade100,
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: Colors.orange.shade700),
                      const SizedBox(width: 8),
                      Text(
                        localeProvider.isRTL
                            ? '$lowStockCount منتج بمخزون قليل'
                            : '$lowStockCount products with low stock',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

              // Products List
              Expanded(
                child:
                    productProvider.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : products.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.inventory_2_outlined,
                                size: 64,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                localeProvider.isRTL
                                    ? 'لا توجد منتجات'
                                    : 'No products found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          itemCount: products.length,
                          itemBuilder: (context, index) {
                            return _buildProductCard(
                              products[index],
                              localeProvider,
                            );
                          },
                        ),
              ),
            ],
          ),
        );
      },
    );
  }
}
