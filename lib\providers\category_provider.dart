import 'package:flutter/foundation.dart' hide Category;
import 'package:pos_app/models/category.dart';
import 'package:pos_app/db/database_helper.dart';

class CategoryProvider with ChangeNotifier {
  List<Category> _categories = [];
  bool _isLoading = false;

  CategoryProvider() {
    loadCategories();
  }

  List<Category> get categories => [..._categories];
  bool get isLoading => _isLoading;

  Future<void> loadCategories() async {
    _isLoading = true;
    notifyListeners();

    try {
      if (kIsWeb) {
        // For web, use sample categories since SQLite is not fully supported
        _categories = [
          Category(id: 1, name: 'Beverages', iconName: 'local_cafe'),
          Category(id: 2, name: 'Bakery', iconName: 'bakery_dining'),
          Category(id: 3, name: 'Snacks', iconName: 'lunch_dining'),
          Category(id: 4, name: 'Food', iconName: 'restaurant'),
          Category(id: 5, name: 'Dairy', iconName: 'egg'),
          Category(id: 6, name: 'Produce', iconName: 'eco'),
        ];
      } else {
        // For mobile, use SQLite database
        _categories = await DatabaseHelper.instance.getAllCategories();
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('Error loading categories: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCategory(Category category) async {
    try {
      if (kIsWeb) {
        // For web, just add to the in-memory list
        final id = _categories.isNotEmpty ? _categories.last.id! + 1 : 1;
        final newCategory = category.copyWith(id: id);
        _categories.add(newCategory);
      } else {
        // For mobile, use SQLite database
        final id = await DatabaseHelper.instance.insertCategory(category);
        final newCategory = category.copyWith(id: id);
        _categories.add(newCategory);
      }
      notifyListeners();
    } catch (e) {
      print('Error adding category: $e');
    }
  }

  Future<void> updateCategory(Category category) async {
    try {
      if (!kIsWeb) {
        // For mobile, use SQLite database
        await DatabaseHelper.instance.updateCategory(category);
      }
      
      // Update in-memory list for both web and mobile
      final index = _categories.indexWhere((c) => c.id == category.id);
      if (index >= 0) {
        _categories[index] = category;
        notifyListeners();
      }
    } catch (e) {
      print('Error updating category: $e');
    }
  }

  Future<void> deleteCategory(int id) async {
    try {
      if (!kIsWeb) {
        // For mobile, use SQLite database
        await DatabaseHelper.instance.deleteCategory(id);
      }
      
      // Update in-memory list for both web and mobile
      _categories.removeWhere((category) => category.id == id);
      notifyListeners();
    } catch (e) {
      print('Error deleting category: $e');
    }
  }

  Category? getCategoryById(int id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  Category? getCategoryByName(String name) {
    try {
      return _categories.firstWhere(
        (category) => category.name.toLowerCase() == name.toLowerCase()
      );
    } catch (e) {
      return null;
    }
  }
}
