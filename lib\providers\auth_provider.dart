import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class AuthProvider with ChangeNotifier {
  User? _currentUser;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _errorMessage;

  User? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get errorMessage => _errorMessage;

  AuthProvider() {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool('is_logged_in') ?? false;
      
      if (isLoggedIn) {
        // Load user data from preferences
        final userId = prefs.getInt('user_id');
        final userName = prefs.getString('user_name');
        final userEmail = prefs.getString('user_email');
        final businessName = prefs.getString('business_name');
        
        if (userId != null && userName != null && userEmail != null) {
          _currentUser = User(
            id: userId,
            fullName: userName,
            email: userEmail,
            businessName: businessName ?? '',
          );
          _isAuthenticated = true;
        }
      }
    } catch (e) {
      debugPrint('Error checking auth status: $e');
    }
    notifyListeners();
  }

  Future<bool> login(String email, String password, {bool rememberMe = false}) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, accept any email/password combination
      // In a real app, this would make an API call to authenticate
      if (email.isNotEmpty && password.isNotEmpty) {
        _currentUser = User(
          id: 1,
          fullName: _getNameFromEmail(email),
          email: email,
          businessName: 'Demo Business',
          phone: '+213555123456',
        );

        _isAuthenticated = true;

        // Save to preferences if remember me is checked
        if (rememberMe) {
          await _saveUserToPreferences(_currentUser!);
        }

        _setLoading(false);
        return true;
      } else {
        _setError('البريد الإلكتروني وكلمة المرور مطلوبان');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء تسجيل الدخول');
      _setLoading(false);
      return false;
    }
  }

  Future<bool> register({
    required String fullName,
    required String email,
    required String phone,
    required String password,
    required String businessName,
    required String businessType,
    required String address,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, accept any valid data
      // In a real app, this would make an API call to register
      if (_validateRegistrationData(fullName, email, phone, password, businessName)) {
        _currentUser = User(
          id: DateTime.now().millisecondsSinceEpoch,
          fullName: fullName,
          email: email,
          phone: phone,
          businessName: businessName,
          businessType: businessType,
          address: address,
        );

        _isAuthenticated = true;
        await _saveUserToPreferences(_currentUser!);

        _setLoading(false);
        return true;
      } else {
        _setError('يرجى التحقق من صحة البيانات المدخلة');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ أثناء إنشاء الحساب');
      _setLoading(false);
      return false;
    }
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      _currentUser = null;
      _isAuthenticated = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error during logout: $e');
    }
  }

  Future<void> _saveUserToPreferences(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_logged_in', true);
      await prefs.setInt('user_id', user.id);
      await prefs.setString('user_name', user.fullName);
      await prefs.setString('user_email', user.email);
      if (user.businessName.isNotEmpty) {
        await prefs.setString('business_name', user.businessName);
      }
    } catch (e) {
      debugPrint('Error saving user to preferences: $e');
    }
  }

  bool _validateRegistrationData(String fullName, String email, String phone, String password, String businessName) {
    return fullName.isNotEmpty &&
           email.isNotEmpty &&
           email.contains('@') &&
           phone.isNotEmpty &&
           password.length >= 6 &&
           businessName.isNotEmpty;
  }

  String _getNameFromEmail(String email) {
    final username = email.split('@')[0];
    return username.replaceAll('.', ' ').replaceAll('_', ' ').split(' ')
        .map((word) => word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }
}
