import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  CheckCircle,
  ArrowRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const Contact: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email Us',
      description: 'Get in touch with our sales team',
      value: '<EMAIL>',
      action: 'mailto:<EMAIL>',
    },
    {
      icon: Phone,
      title: 'Call Us',
      description: 'Speak with our experts',
      value: '+****************',
      action: 'tel:+***********',
    },
    {
      icon: MapPin,
      title: 'Visit Us',
      description: 'Our headquarters',
      value: '123 Business Ave, Tech City',
      action: '#',
    },
    {
      icon: Clock,
      title: 'Business Hours',
      description: 'We\'re here to help',
      value: 'Mon-Fri: 9AM-6PM EST',
      action: '#',
    },
  ]

  const benefits = [
    'Free 30-day trial with full features',
    'Dedicated customer success manager',
    'Free setup and training included',
    'No long-term contracts required',
    '24/7 technical support available',
    'Custom integrations available',
  ]

  return (
    <section id="contact" className="py-20 bg-gradient-to-br from-primary-50 via-white to-primary-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-start">
          {/* Contact Information */}
          <motion.div
            ref={ref}
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8 }}
          >
            <div className="mb-8">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                Ready to Get
                <span className="bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent"> Started?</span>
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                Transform your business with our professional POS system. 
                Contact us today for a personalized demo and see how we can help you grow.
              </p>
            </div>

            {/* Contact Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              {contactInfo.map((info, index) => (
                <motion.div
                  key={info.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={inView ? { opacity: 1, y: 0 } : {}}
                  transition={{ delay: 0.2 + index * 0.1 }}
                >
                  <Card className="h-full hover:shadow-lg transition-shadow duration-300 cursor-pointer group"
                        onClick={() => info.action !== '#' && window.open(info.action, '_blank')}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-200 transition-colors">
                          <info.icon className="w-5 h-5 text-primary-600" />
                        </div>
                        <CardTitle className="text-lg font-semibold text-gray-900">
                          {info.title}
                        </CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-2">{info.description}</p>
                      <p className="font-medium text-gray-900">{info.value}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>

            {/* Benefits List */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-xl p-6 shadow-sm border border-primary-100"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">What You Get:</h3>
              <ul className="space-y-3">
                {benefits.map((benefit, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={inView ? { opacity: 1, x: 0 } : {}}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="flex items-start"
                  >
                    <CheckCircle className="w-5 h-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{benefit}</span>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </motion.div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:sticky lg:top-8"
          >
            <Card className="shadow-xl border-0 bg-gradient-to-br from-primary-500 to-primary-600 text-white overflow-hidden">
              <div className="absolute inset-0 bg-black/10"></div>
              <CardContent className="p-8 relative z-10">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold mb-2">Start Your Free Trial</h3>
                  <p className="text-primary-100">
                    Experience the full power of our POS system with a 30-day free trial
                  </p>
                </div>

                <div className="space-y-6">
                  <Button
                    size="xl"
                    onClick={() => window.open('http://localhost:8080', '_blank')}
                    className="w-full bg-white text-primary-600 hover:bg-gray-100 font-semibold"
                  >
                    Try Live Demo Now
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Button>

                  <div className="text-center">
                    <p className="text-primary-100 text-sm mb-4">Or contact our sales team</p>
                    <Button
                      size="lg"
                      variant="outline"
                      className="w-full border-2 border-white text-white hover:bg-white hover:text-primary-600"
                      onClick={() => window.open('mailto:<EMAIL>', '_blank')}
                    >
                      <Send className="mr-2 w-4 h-4" />
                      Request Demo
                    </Button>
                  </div>

                  <div className="border-t border-primary-400 pt-6">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold">30</div>
                        <div className="text-xs text-primary-100">Day Trial</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold">24/7</div>
                        <div className="text-xs text-primary-100">Support</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold">Free</div>
                        <div className="text-xs text-primary-100">Setup</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.8 }}
              className="mt-6 text-center"
            >
              <p className="text-sm text-gray-600 mb-4">Trusted by businesses worldwide</p>
              <div className="flex justify-center items-center space-x-6 opacity-60">
                {['🏪', '🍕', '☕', '👕', '📱', '💊'].map((emoji, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={inView ? { opacity: 0.6, scale: 1 } : {}}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    className="text-2xl"
                  >
                    {emoji}
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Contact
