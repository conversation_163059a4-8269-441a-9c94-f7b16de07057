import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/screens/product_form_screen.dart';

class BarcodeManagementScreen extends StatefulWidget {
  const BarcodeManagementScreen({super.key});

  @override
  State<BarcodeManagementScreen> createState() =>
      _BarcodeManagementScreenState();
}

class _BarcodeManagementScreenState extends State<BarcodeManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _barcodeController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';
  bool _showOnlyWithoutBarcode = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _barcodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'إدارة الباركود' : 'Barcode Management'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _scanBarcode,
            icon: const FaIcon(FontAwesomeIcons.qrcode),
            tooltip: isRTL ? 'مسح الباركود' : 'Scan Barcode',
          ),
          IconButton(
            onPressed: _showBarcodeGenerator,
            icon: const Icon(Icons.qr_code_2),
            tooltip: isRTL ? 'إنشاء باركود' : 'Generate Barcode',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const FaIcon(FontAwesomeIcons.list, size: 16),
              text: isRTL ? 'جميع المنتجات' : 'All Products',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.qrcode, size: 16),
              text: isRTL ? 'مع باركود' : 'With Barcode',
            ),
            Tab(
              icon: const FaIcon(
                FontAwesomeIcons.triangleExclamation,
                size: 16,
              ),
              text: isRTL ? 'بدون باركود' : 'Without Barcode',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(isRTL, theme),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllProductsTab(),
                _buildWithBarcodeTab(),
                _buildWithoutBarcodeTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showBulkBarcodeDialog,
        icon: const FaIcon(FontAwesomeIcons.barcode),
        label: Text(isRTL ? 'إنشاء مجمع' : 'Bulk Generate'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildSearchAndFilters(bool isRTL, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText:
                        isRTL ? 'البحث في المنتجات...' : 'Search products...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon:
                        _searchQuery.isNotEmpty
                            ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _searchQuery = '';
                                  _searchController.clear();
                                });
                              },
                            )
                            : null,
                    filled: true,
                    fillColor: theme.colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.3),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
              ),
              const SizedBox(width: 12),
              IconButton(
                onPressed: _scanBarcode,
                icon: const FaIcon(FontAwesomeIcons.qrcode),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(12),
                ),
                tooltip: isRTL ? 'مسح الباركود' : 'Scan Barcode',
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'الفئة' : 'Category',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  items:
                      ['All', 'Electronics', 'Clothing', 'Food', 'Books']
                          .map(
                            (category) => DropdownMenuItem(
                              value: category,
                              child: Text(category),
                            ),
                          )
                          .toList(),
                  onChanged:
                      (value) => setState(() => _selectedCategory = value!),
                ),
              ),
              const SizedBox(width: 12),
              FilterChip(
                label: Text(isRTL ? 'بدون باركود فقط' : 'No Barcode Only'),
                selected: _showOnlyWithoutBarcode,
                onSelected:
                    (selected) =>
                        setState(() => _showOnlyWithoutBarcode = selected),
                selectedColor: theme.colorScheme.primaryContainer,
                checkmarkColor: theme.colorScheme.primary,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAllProductsTab() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, _) {
        final filteredProducts = _getFilteredProducts(productProvider.products);

        if (productProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (filteredProducts.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredProducts.length,
          itemBuilder: (context, index) {
            final product = filteredProducts[index];
            return _buildProductCard(product);
          },
        );
      },
    );
  }

  Widget _buildWithBarcodeTab() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, _) {
        final productsWithBarcode =
            productProvider.products
                .where((product) => product.barcode.isNotEmpty)
                .toList();
        final filteredProducts = _getFilteredProducts(productsWithBarcode);

        if (productProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (filteredProducts.isEmpty) {
          return _buildEmptyState(message: 'No products with barcode found');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredProducts.length,
          itemBuilder: (context, index) {
            final product = filteredProducts[index];
            return _buildProductCard(product, showBarcodeActions: true);
          },
        );
      },
    );
  }

  Widget _buildWithoutBarcodeTab() {
    return Consumer<ProductProvider>(
      builder: (context, productProvider, _) {
        final productsWithoutBarcode =
            productProvider.products
                .where((product) => product.barcode.isEmpty)
                .toList();
        final filteredProducts = _getFilteredProducts(productsWithoutBarcode);

        if (productProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (filteredProducts.isEmpty) {
          return _buildEmptyState(message: 'All products have barcodes!');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredProducts.length,
          itemBuilder: (context, index) {
            final product = filteredProducts[index];
            return _buildProductCard(product, showAddBarcodeAction: true);
          },
        );
      },
    );
  }

  Widget _buildProductCard(
    Product product, {
    bool showBarcodeActions = false,
    bool showAddBarcodeAction = false,
  }) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor:
              product.barcode.isNotEmpty
                  ? Colors.green.withValues(alpha: 0.2)
                  : Colors.orange.withValues(alpha: 0.2),
          child: FaIcon(
            product.barcode.isNotEmpty
                ? FontAwesomeIcons.qrcode
                : FontAwesomeIcons.triangleExclamation,
            color: product.barcode.isNotEmpty ? Colors.green : Colors.orange,
            size: 16,
          ),
        ),
        title: Text(
          product.name,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${isRTL ? 'السعر:' : 'Price:'} ${product.price} ${isRTL ? 'دج' : 'DZD'}',
            ),
            Text('${isRTL ? 'المخزون:' : 'Stock:'} ${product.stock}'),
            if (product.barcode.isNotEmpty)
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  product.barcode,
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontFamily: 'monospace',
                    fontSize: 12,
                  ),
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showAddBarcodeAction)
              IconButton(
                onPressed: () => _addBarcodeToProduct(product),
                icon: const FaIcon(FontAwesomeIcons.plus, size: 16),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
                tooltip: isRTL ? 'إضافة باركود' : 'Add Barcode',
              ),
            if (showBarcodeActions) ...[
              IconButton(
                onPressed: () => _previewBarcode(product),
                icon: const FaIcon(FontAwesomeIcons.eye, size: 16),
                tooltip: isRTL ? 'معاينة الباركود' : 'Preview Barcode',
              ),
              IconButton(
                onPressed: () => _printBarcode(product),
                icon: const FaIcon(FontAwesomeIcons.print, size: 16),
                tooltip: isRTL ? 'طباعة الباركود' : 'Print Barcode',
              ),
              IconButton(
                onPressed: () => _editBarcode(product),
                icon: const FaIcon(FontAwesomeIcons.penToSquare, size: 16),
                tooltip: isRTL ? 'تعديل الباركود' : 'Edit Barcode',
              ),
            ],
            IconButton(
              onPressed: () => _editProduct(product),
              icon: const Icon(Icons.edit),
              tooltip: isRTL ? 'تعديل المنتج' : 'Edit Product',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState({String? message}) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.qrcode,
            size: 80,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            message ?? (isRTL ? 'لا توجد منتجات' : 'No products found'),
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL ? 'جرب تغيير المرشحات' : 'Try changing the filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }

  List<Product> _getFilteredProducts(List<Product> products) {
    return products.where((product) {
      final matchesSearch =
          _searchQuery.isEmpty ||
          product.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          product.barcode.contains(_searchQuery);

      final matchesCategory =
          _selectedCategory == 'All' || product.category == _selectedCategory;

      final matchesBarcodeFilter =
          !_showOnlyWithoutBarcode || product.barcode.isEmpty;

      return matchesSearch && matchesCategory && matchesBarcodeFilter;
    }).toList();
  }

  Future<void> _scanBarcode() async {
    try {
      final barcode = await FlutterBarcodeScanner.scanBarcode(
        '#ff6666',
        'Cancel',
        true,
        ScanMode.BARCODE,
      );

      if (barcode != '-1' && mounted) {
        _searchController.text = barcode;
        setState(() => _searchQuery = barcode);

        // Check if product exists with this barcode
        final productProvider = Provider.of<ProductProvider>(
          context,
          listen: false,
        );
        final existingProduct =
            productProvider.products
                .where((p) => p.barcode == barcode)
                .firstOrNull;

        if (existingProduct != null) {
          _showProductFoundDialog(existingProduct);
        } else {
          _showBarcodeNotFoundDialog(barcode);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error scanning barcode: $e')));
      }
    }
  }

  void _showProductFoundDialog(Product product) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'تم العثور على المنتج' : 'Product Found'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${isRTL ? 'الاسم:' : 'Name:'} ${product.name}'),
                Text('${isRTL ? 'السعر:' : 'Price:'} ${product.price}'),
                Text('${isRTL ? 'المخزون:' : 'Stock:'} ${product.stock}'),
                Text('${isRTL ? 'الباركود:' : 'Barcode:'} ${product.barcode}'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إغلاق' : 'Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _editProduct(product);
                },
                child: Text(isRTL ? 'تعديل' : 'Edit'),
              ),
            ],
          ),
    );
  }

  void _showBarcodeNotFoundDialog(String barcode) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'المنتج غير موجود' : 'Product Not Found'),
            content: Text(
              isRTL
                  ? 'لم يتم العثور على منتج بهذا الباركود. هل تريد إنشاء منتج جديد؟'
                  : 'No product found with this barcode. Would you like to create a new product?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _createNewProductWithBarcode(barcode);
                },
                child: Text(isRTL ? 'إنشاء منتج' : 'Create Product'),
              ),
            ],
          ),
    );
  }

  void _createNewProductWithBarcode(String barcode) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => ProductFormScreen(initialBarcode: barcode),
      ),
    );
  }

  void _addBarcodeToProduct(Product product) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'إضافة باركود' : 'Add Barcode'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${isRTL ? 'المنتج:' : 'Product:'} ${product.name}'),
                const SizedBox(height: 16),
                TextField(
                  controller: _barcodeController,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'الباركود' : 'Barcode',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      onPressed: () async {
                        final barcode = await FlutterBarcodeScanner.scanBarcode(
                          '#ff6666',
                          'Cancel',
                          true,
                          ScanMode.BARCODE,
                        );
                        if (barcode != '-1') {
                          _barcodeController.text = barcode;
                        }
                      },
                      icon: const FaIcon(FontAwesomeIcons.qrcode),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  _barcodeController.clear();
                  Navigator.of(context).pop();
                },
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (_barcodeController.text.isNotEmpty) {
                    _updateProductBarcode(product, _barcodeController.text);
                    _barcodeController.clear();
                    Navigator.of(context).pop();
                  }
                },
                child: Text(isRTL ? 'حفظ' : 'Save'),
              ),
            ],
          ),
    );
  }

  void _updateProductBarcode(Product product, String barcode) {
    final updatedProduct = Product(
      id: product.id,
      name: product.name,
      price: product.price,
      costPrice: product.costPrice,
      wholesalePrice: product.wholesalePrice,
      enableWholesale: product.enableWholesale,
      minWholesaleQty: product.minWholesaleQty,
      stock: product.stock,
      category: product.category,
      barcode: barcode,
      imageUrl: product.imageUrl,
    );

    Provider.of<ProductProvider>(
      context,
      listen: false,
    ).updateProduct(updatedProduct);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'تم تحديث الباركود بنجاح'
              : 'Barcode updated successfully',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _editBarcode(Product product) {
    _barcodeController.text = product.barcode;
    _addBarcodeToProduct(product);
  }

  void _previewBarcode(Product product) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'معاينة الباركود' : 'Barcode Preview'),
            content: SizedBox(
              width: 300,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        // Barcode visualization (simplified)
                        Container(
                          height: 60,
                          width: 200,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: Colors.black),
                          ),
                          child: CustomPaint(
                            painter: BarcodePainter(product.barcode),
                            size: const Size(200, 60),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          product.barcode,
                          style: const TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          product.name,
                          style: const TextStyle(fontSize: 10),
                          textAlign: TextAlign.center,
                        ),
                        Text(
                          '${product.price} ${isRTL ? 'دج' : 'DZD'}',
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed:
                            () => _copyBarcodeToClipboard(product.barcode),
                        icon: const Icon(Icons.copy, size: 16),
                        label: Text(isRTL ? 'نسخ' : 'Copy'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _printBarcode(product);
                        },
                        icon: const FaIcon(FontAwesomeIcons.print, size: 16),
                        label: Text(isRTL ? 'طباعة' : 'Print'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  void _copyBarcodeToClipboard(String barcode) {
    Clipboard.setData(ClipboardData(text: barcode));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'تم نسخ الباركود'
              : 'Barcode copied to clipboard',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _printBarcode(Product product) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'خيارات الطباعة' : 'Print Options'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const FaIcon(FontAwesomeIcons.print),
                  title: Text(
                    isRTL ? 'طباعة باركود واحد' : 'Print Single Barcode',
                  ),
                  subtitle: Text(product.name),
                  onTap: () {
                    Navigator.of(context).pop();
                    _executePrint([product], 1);
                  },
                ),
                ListTile(
                  leading: const FaIcon(FontAwesomeIcons.clone),
                  title: Text(isRTL ? 'طباعة متعددة' : 'Print Multiple'),
                  subtitle: Text(isRTL ? 'اختر عدد النسخ' : 'Choose quantity'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showPrintQuantityDialog(product);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
            ],
          ),
    );
  }

  void _showPrintQuantityDialog(Product product) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final quantityController = TextEditingController(text: '1');

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'عدد النسخ' : 'Print Quantity'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${isRTL ? 'المنتج:' : 'Product:'} ${product.name}'),
                const SizedBox(height: 16),
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: isRTL ? 'عدد النسخ' : 'Quantity',
                    border: const OutlineInputBorder(),
                    suffixIcon: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        InkWell(
                          onTap: () {
                            final current =
                                int.tryParse(quantityController.text) ?? 1;
                            quantityController.text = (current + 1).toString();
                          },
                          child: const Icon(Icons.keyboard_arrow_up),
                        ),
                        InkWell(
                          onTap: () {
                            final current =
                                int.tryParse(quantityController.text) ?? 1;
                            if (current > 1) {
                              quantityController.text =
                                  (current - 1).toString();
                            }
                          },
                          child: const Icon(Icons.keyboard_arrow_down),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  final quantity = int.tryParse(quantityController.text) ?? 1;
                  Navigator.of(context).pop();
                  _executePrint([product], quantity);
                },
                child: Text(isRTL ? 'طباعة' : 'Print'),
              ),
            ],
          ),
    );
  }

  void _executePrint(List<Product> products, int quantity) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    // Simulate printing process
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(isRTL ? 'جاري الطباعة...' : 'Printing...'),
              ],
            ),
          ),
    );

    // Simulate printing delay
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL
                ? 'تم إرسال ${products.length * quantity} باركود للطباعة'
                : 'Sent ${products.length * quantity} barcodes to printer',
          ),
          backgroundColor: Colors.green,
        ),
      );
    });
  }

  void _editProduct(Product product) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => ProductFormScreen(product: product)),
    );
  }

  void _showBarcodeGenerator() {
    // Implementation for barcode generator
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'مولد الباركود قيد التطوير'
              : 'Barcode generator coming soon',
        ),
      ),
    );
  }

  void _showBulkBarcodeDialog() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isRTL ? 'الإنشاء المجمع للباركود' : 'Bulk Barcode Generation',
            ),
            content: SizedBox(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    leading: const FaIcon(
                      FontAwesomeIcons.triangleExclamation,
                      color: Colors.orange,
                    ),
                    title: Text(
                      isRTL
                          ? 'المنتجات بدون باركود'
                          : 'Products Without Barcode',
                    ),
                    subtitle: Text(
                      isRTL
                          ? 'إنشاء باركود للمنتجات التي لا تحتوي على باركود'
                          : 'Generate barcodes for products without barcodes',
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _generateBarcodeForProductsWithoutBarcode();
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const FaIcon(
                      FontAwesomeIcons.print,
                      color: Colors.green,
                    ),
                    title: Text(
                      isRTL ? 'طباعة جميع الباركودات' : 'Print All Barcodes',
                    ),
                    subtitle: Text(
                      isRTL
                          ? 'طباعة باركودات جميع المنتجات'
                          : 'Print barcodes for all products',
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _printAllBarcodes();
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: const FaIcon(
                      FontAwesomeIcons.fileExport,
                      color: Colors.blue,
                    ),
                    title: Text(
                      isRTL ? 'تصدير قائمة الباركود' : 'Export Barcode List',
                    ),
                    subtitle: Text(
                      isRTL
                          ? 'تصدير جميع الباركودات إلى ملف'
                          : 'Export all barcodes to file',
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _exportBarcodeList();
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  void _generateBarcodeForProductsWithoutBarcode() {
    final productProvider = Provider.of<ProductProvider>(
      context,
      listen: false,
    );
    final productsWithoutBarcode =
        productProvider.products
            .where((product) => product.barcode.isEmpty)
            .toList();

    if (productsWithoutBarcode.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'جميع المنتجات لديها باركود بالفعل'
                : 'All products already have barcodes',
          ),
          backgroundColor: Colors.green,
        ),
      );
      return;
    }

    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isRTL ? 'إنشاء باركود مجمع' : 'Bulk Barcode Generation',
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isRTL
                      ? 'سيتم إنشاء باركود لـ ${productsWithoutBarcode.length} منتج'
                      : 'Will generate barcodes for ${productsWithoutBarcode.length} products',
                ),
                const SizedBox(height: 16),
                Text(
                  isRTL ? 'هل تريد المتابعة؟' : 'Do you want to continue?',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _executeBulkBarcodeGeneration(productsWithoutBarcode);
                },
                child: Text(isRTL ? 'إنشاء' : 'Generate'),
              ),
            ],
          ),
    );
  }

  void _executeBulkBarcodeGeneration(List<Product> products) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    // Show progress dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  isRTL ? 'جاري إنشاء الباركودات...' : 'Generating barcodes...',
                ),
              ],
            ),
          ),
    );

    // Generate barcodes
    int processed = 0;
    for (final product in products) {
      final barcode = _generateUniqueBarcode();
      final updatedProduct = Product(
        id: product.id,
        name: product.name,
        price: product.price,
        costPrice: product.costPrice,
        wholesalePrice: product.wholesalePrice,
        enableWholesale: product.enableWholesale,
        minWholesaleQty: product.minWholesaleQty,
        stock: product.stock,
        category: product.category,
        barcode: barcode,
        imageUrl: product.imageUrl,
      );

      Provider.of<ProductProvider>(
        context,
        listen: false,
      ).updateProduct(updatedProduct);
      processed++;
    }

    // Close progress dialog and show success
    if (mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isRTL
                ? 'تم إنشاء $processed باركود بنجاح'
                : 'Successfully generated $processed barcodes',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  String _generateUniqueBarcode() {
    // Generate a simple 13-digit barcode (EAN-13 format)
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 1000000).toString().padLeft(6, '0');
    return '200$random${_calculateCheckDigit('200$random')}';
  }

  String _calculateCheckDigit(String barcode) {
    int sum = 0;
    for (int i = 0; i < barcode.length; i++) {
      int digit = int.parse(barcode[i]);
      sum += (i % 2 == 0) ? digit : digit * 3;
    }
    int checkDigit = (10 - (sum % 10)) % 10;
    return checkDigit.toString();
  }

  void _printAllBarcodes() {
    final productProvider = Provider.of<ProductProvider>(
      context,
      listen: false,
    );
    final productsWithBarcode =
        productProvider.products
            .where((product) => product.barcode.isNotEmpty)
            .toList();

    if (productsWithBarcode.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'لا توجد منتجات بباركود للطباعة'
                : 'No products with barcodes to print',
          ),
        ),
      );
      return;
    }

    _executePrint(productsWithBarcode, 1);
  }

  void _exportBarcodeList() {
    final productProvider = Provider.of<ProductProvider>(
      context,
      listen: false,
    );
    final productsWithBarcode =
        productProvider.products
            .where((product) => product.barcode.isNotEmpty)
            .toList();

    if (productsWithBarcode.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleProvider>(context, listen: false).isRTL
                ? 'لا توجد منتجات بباركود للتصدير'
                : 'No products with barcodes to export',
          ),
        ),
      );
      return;
    }

    // Simulate export process
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'تم تصدير ${productsWithBarcode.length} باركود'
              : 'Exported ${productsWithBarcode.length} barcodes',
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}

// Custom painter for barcode visualization
class BarcodePainter extends CustomPainter {
  final String barcode;

  BarcodePainter(this.barcode);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.black
          ..strokeWidth = 1;

    // Simple barcode visualization (vertical lines)
    final barWidth = size.width / (barcode.length * 2);

    for (int i = 0; i < barcode.length; i++) {
      final digit = int.tryParse(barcode[i]) ?? 0;
      final x = i * barWidth * 2;

      // Draw bars based on digit (simplified)
      for (int j = 0; j < digit % 4 + 1; j++) {
        canvas.drawLine(
          Offset(x + j * (barWidth / 2), 0),
          Offset(x + j * (barWidth / 2), size.height),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
