import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/models/cart_item.dart';
import 'package:pos_app/models/transaction.dart' as model;
import 'package:pos_app/models/invoice.dart';
import 'package:pos_app/db/database_helper.dart';
import 'package:intl/intl.dart';

class CartProvider with ChangeNotifier {
  List<CartItem> _items = [];
  String _customerName = '';
  String _paymentMethod = 'Cash';
  bool _isLoading = false;
  static const String _cartKey = 'cart_items';
  static const String _customerKey = 'cart_customer';
  static const String _paymentKey = 'cart_payment_method';

  CartProvider() {
    _loadCartFromStorage();
  }

  List<CartItem> get items => [..._items];
  int get itemCount => _items.length;
  bool get isLoading => _isLoading;
  double get totalAmount {
    return _items.fold(0.0, (sum, item) => sum + item.total);
  }

  // Adding total getter as an alias for totalAmount for compatibility
  double get total => totalAmount;

  String get customerName => _customerName;
  set customerName(String name) {
    _customerName = name;
    _saveCartToStorage();
    notifyListeners();
  }

  String get paymentMethod => _paymentMethod;
  set paymentMethod(String method) {
    _paymentMethod = method;
    _saveCartToStorage();
    notifyListeners();
  }

  void addItem(Product product) {
    final existingIndex = _items.indexWhere(
      (item) => item.product.id == product.id,
    );

    if (existingIndex >= 0) {
      // Product already in cart, update quantity
      _items[existingIndex] = _items[existingIndex].copyWith(
        quantity: _items[existingIndex].quantity + 1,
      );
    } else {
      // Add new product to cart
      _items.add(CartItem(product: product, quantity: 1, price: product.price));
    }
    _saveCartToStorage();
    notifyListeners();
  }

  // Alias for addItem to maintain compatibility
  void addToCart(Product product) {
    addItem(product);
  }

  void removeItem(int productId) {
    _items.removeWhere((item) => item.product.id == productId);
    _saveCartToStorage();
    notifyListeners();
  }

  // Legacy method for backward compatibility
  void decreaseQuantity(int productId) {
    final index = _items.indexWhere((item) => item.product.id == productId);
    if (index >= 0) {
      if (_items[index].quantity > 1) {
        _items[index] = _items[index].copyWith(
          quantity: _items[index].quantity - 1,
        );
      } else {
        _items.removeAt(index);
      }
      notifyListeners();
    }
  }

  void incrementQuantity(Product product) {
    final index = _items.indexWhere((item) => item.product.id == product.id);
    if (index >= 0) {
      _items[index] = _items[index].copyWith(
        quantity: _items[index].quantity + 1,
      );
    } else {
      _items.add(CartItem(product: product, quantity: 1, price: product.price));
    }
    notifyListeners();
  }

  void decrementQuantity(Product product) {
    final index = _items.indexWhere((item) => item.product.id == product.id);
    if (index >= 0) {
      if (_items[index].quantity > 1) {
        _items[index] = _items[index].copyWith(
          quantity: _items[index].quantity - 1,
        );
      } else {
        _items.removeAt(index);
      }
      notifyListeners();
    }
  }

  int getQuantity(Product product) {
    final index = _items.indexWhere((item) => item.product.id == product.id);
    return index >= 0 ? _items[index].quantity : 0;
  }

  void increaseQuantity(int productId) {
    final index = _items.indexWhere((item) => item.product.id == productId);
    if (index >= 0) {
      _items[index] = _items[index].copyWith(
        quantity: _items[index].quantity + 1,
      );
      notifyListeners();
    }
  }

  void updateQuantity(int productId, int quantity) {
    final index = _items.indexWhere((item) => item.product.id == productId);
    if (index >= 0 && quantity > 0) {
      _items[index] = _items[index].copyWith(quantity: quantity);
      notifyListeners();
    } else if (index >= 0 && quantity <= 0) {
      _items.removeAt(index);
      notifyListeners();
    }
  }

  void clear() {
    _items = [];
    _customerName = '';
    _paymentMethod = 'Cash';
    _saveCartToStorage();
    notifyListeners();
  }

  Future<model.Transaction> checkout(
    double amountPaid, {
    String? customerName,
    String paymentMethod = 'Cash',
  }) async {
    final now = DateTime.now();
    final transactionId = DateFormat('yyyyMMddHHmmss').format(now);

    final transaction = model.Transaction(
      transactionId: transactionId,
      date: now,
      items: [..._items],
      total: totalAmount,
      amountPaid: amountPaid,
      change: amountPaid - totalAmount,
      paymentMethod: paymentMethod,
    );

    // Save transaction to database
    if (!kIsWeb) {
      await DatabaseHelper.instance.insertTransaction(transaction);

      // Create and save invoice
      final invoice = Invoice(
        invoiceNumber: 'INV-$transactionId',
        date: now,
        totalAmount: totalAmount,
        discountAmount: 0, // Can be updated based on discounts
        taxAmount: 0, // Can be updated based on your tax rules
        finalAmount: totalAmount,
        paymentMethod: paymentMethod,
        status:
            amountPaid >= totalAmount
                ? 'paid'
                : (amountPaid > 0 ? 'partial' : 'unpaid'),
        customerName: customerName ?? 'عميل نقدي',
        items:
            _items
                .map(
                  (item) => InvoiceItem(
                    productId: item.product.id,
                    productName: item.product.name,
                    quantity: item.quantity.toDouble(),
                    unitPrice: item.price,
                    totalPrice: item.total,
                  ),
                )
                .toList(),
        notes: '',
      );

      await DatabaseHelper.instance.insertInvoice(invoice);
    }

    // Clear cart after successful checkout
    clear();

    return transaction;
  }

  // Cart persistence methods
  Future<void> _loadCartFromStorage() async {
    try {
      _isLoading = true;
      final prefs = await SharedPreferences.getInstance();

      // Load cart items
      final cartData = prefs.getString(_cartKey);
      if (cartData != null) {
        final List<dynamic> itemsJson = json.decode(cartData);
        _items = itemsJson.map((item) => CartItem.fromJson(item)).toList();
      }

      // Load customer name
      _customerName = prefs.getString(_customerKey) ?? '';

      // Load payment method
      _paymentMethod = prefs.getString(_paymentKey) ?? 'Cash';

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading cart from storage: $e');
      _isLoading = false;
    }
  }

  Future<void> _saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save cart items
      final itemsJson = _items.map((item) => item.toJson()).toList();
      await prefs.setString(_cartKey, json.encode(itemsJson));

      // Save customer name
      await prefs.setString(_customerKey, _customerName);

      // Save payment method
      await prefs.setString(_paymentKey, _paymentMethod);
    } catch (e) {
      debugPrint('Error saving cart to storage: $e');
    }
  }

  Future<void> clearStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cartKey);
      await prefs.remove(_customerKey);
      await prefs.remove(_paymentKey);
    } catch (e) {
      debugPrint('Error clearing cart storage: $e');
    }
  }
}
