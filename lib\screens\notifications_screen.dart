import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/providers/notification_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/widgets/notification_settings_modal.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<NotificationProvider, LocaleProvider>(
      builder: (context, notificationProvider, localeProvider, _) {
        final isRTL = localeProvider.isRTL;

        return Scaffold(
          appBar: AppBar(
            title: Text(
              isRTL ? 'الإشعارات' : 'Notifications',
              style:
                  isRTL
                      ? GoogleFonts.cairo(fontWeight: FontWeight.w600)
                      : GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            actions: [
              // Mark all as read
              IconButton(
                icon: const FaIcon(FontAwesomeIcons.checkDouble, size: 20),
                tooltip: isRTL ? 'تحديد الكل كمقروء' : 'Mark All as Read',
                onPressed:
                    notificationProvider.unreadCount > 0
                        ? () => notificationProvider.markAllAsRead()
                        : null,
              ),
              // Clear all notifications
              IconButton(
                icon: const FaIcon(FontAwesomeIcons.trash, size: 20),
                tooltip: isRTL ? 'حذف الكل' : 'Clear All',
                onPressed:
                    notificationProvider.notifications.isNotEmpty
                        ? () => _showClearAllDialog(
                          context,
                          notificationProvider,
                          isRTL,
                        )
                        : null,
              ),
              // Settings
              IconButton(
                icon: const FaIcon(FontAwesomeIcons.gear, size: 20),
                tooltip: isRTL ? 'إعدادات الإشعارات' : 'Notification Settings',
                onPressed:
                    () => _showNotificationSettings(
                      context,
                      notificationProvider,
                      isRTL,
                    ),
              ),
            ],
          ),
          body: Column(
            children: [
              // Statistics Card
              Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF4CAF50),
                      const Color(0xFF4CAF50).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF4CAF50).withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        isRTL ? 'المجموع' : 'Total',
                        notificationProvider.notifications.length.toString(),
                        FontAwesomeIcons.bell,
                        isRTL,
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 40,
                      color: Colors.white.withValues(alpha: 0.3),
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        isRTL ? 'غير مقروء' : 'Unread',
                        notificationProvider.unreadCount.toString(),
                        FontAwesomeIcons.bellSlash,
                        isRTL,
                      ),
                    ),
                  ],
                ),
              ),

              // Tab Bar
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: TabBar(
                  controller: _tabController,
                  indicator: BoxDecoration(
                    color: const Color(0xFF4CAF50),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  labelColor: Colors.white,
                  unselectedLabelColor: Colors.grey.shade600,
                  labelStyle: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                  tabs: [
                    Tab(
                      text: isRTL ? 'الكل' : 'All',
                      icon: const FaIcon(FontAwesomeIcons.list, size: 16),
                    ),
                    Tab(
                      text: isRTL ? 'غير مقروء' : 'Unread',
                      icon: const FaIcon(
                        FontAwesomeIcons.exclamation,
                        size: 16,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Notifications List
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // All notifications
                    _buildNotificationsList(
                      notificationProvider.notifications,
                      notificationProvider,
                      isRTL,
                    ),
                    // Unread notifications
                    _buildNotificationsList(
                      notificationProvider.unreadNotifications,
                      notificationProvider,
                      isRTL,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, bool isRTL) {
    return Column(
      children: [
        FaIcon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: GoogleFonts.cairo(
            color: Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: GoogleFonts.cairo(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationsList(
    List<AppNotification> notifications,
    NotificationProvider provider,
    bool isRTL,
  ) {
    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.bellSlash,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              isRTL ? 'لا توجد إشعارات' : 'No notifications',
              style: GoogleFonts.cairo(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isRTL
                  ? 'ستظهر الإشعارات هنا عند وصولها'
                  : 'Notifications will appear here when they arrive',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationCard(notification, provider, isRTL);
      },
    );
  }

  Widget _buildNotificationCard(
    AppNotification notification,
    NotificationProvider provider,
    bool isRTL,
  ) {
    final isUnread = !notification.isRead;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isUnread ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color:
              isUnread
                  ? const Color(0xFF4CAF50).withValues(alpha: 0.3)
                  : Colors.transparent,
          width: isUnread ? 1 : 0,
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (isUnread) {
            provider.markAsRead(notification.id);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notification Icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _getNotificationColor(
                    notification.type,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: FaIcon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 20,
                ),
              ),

              const SizedBox(width: 12),

              // Notification Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: GoogleFonts.cairo(
                              fontWeight:
                                  isUnread ? FontWeight.bold : FontWeight.w600,
                              fontSize: 16,
                              color:
                                  isUnread
                                      ? Colors.black87
                                      : Colors.grey.shade700,
                            ),
                          ),
                        ),
                        if (isUnread)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Color(0xFF4CAF50),
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.body,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          DateFormat(
                            'dd/MM/yyyy HH:mm',
                          ).format(notification.createdAt),
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                        Text(
                          _getNotificationTypeLabel(notification.type, isRTL),
                          style: GoogleFonts.cairo(
                            fontSize: 12,
                            color: _getNotificationColor(notification.type),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Actions
              PopupMenuButton<String>(
                icon: FaIcon(
                  FontAwesomeIcons.ellipsisVertical,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                onSelected: (value) {
                  switch (value) {
                    case 'mark_read':
                      provider.markAsRead(notification.id);
                      break;
                    case 'delete':
                      provider.deleteNotification(notification.id);
                      break;
                  }
                },
                itemBuilder:
                    (context) => [
                      if (isUnread)
                        PopupMenuItem(
                          value: 'mark_read',
                          child: Row(
                            children: [
                              const FaIcon(FontAwesomeIcons.check, size: 16),
                              const SizedBox(width: 8),
                              Text(isRTL ? 'تحديد كمقروء' : 'Mark as Read'),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const FaIcon(
                              FontAwesomeIcons.trash,
                              size: 16,
                              color: Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              isRTL ? 'حذف' : 'Delete',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.lowStock:
        return FontAwesomeIcons.triangleExclamation;
      case NotificationType.outOfStock:
        return FontAwesomeIcons.circleExclamation;
      case NotificationType.saleCompleted:
        return FontAwesomeIcons.cashRegister;
      case NotificationType.backupCompleted:
        return FontAwesomeIcons.cloudArrowUp;
      case NotificationType.backupFailed:
        return FontAwesomeIcons.cloudArrowDown;
      case NotificationType.systemAlert:
        return FontAwesomeIcons.gear;
      case NotificationType.reminder:
        return FontAwesomeIcons.clock;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.lowStock:
        return Colors.orange;
      case NotificationType.outOfStock:
        return Colors.red;
      case NotificationType.saleCompleted:
        return const Color(0xFF4CAF50);
      case NotificationType.backupCompleted:
        return Colors.blue;
      case NotificationType.backupFailed:
        return Colors.red;
      case NotificationType.systemAlert:
        return Colors.purple;
      case NotificationType.reminder:
        return Colors.indigo;
    }
  }

  String _getNotificationTypeLabel(NotificationType type, bool isRTL) {
    switch (type) {
      case NotificationType.lowStock:
        return isRTL ? 'مخزون قليل' : 'Low Stock';
      case NotificationType.outOfStock:
        return isRTL ? 'نفد المخزون' : 'Out of Stock';
      case NotificationType.saleCompleted:
        return isRTL ? 'بيع مكتمل' : 'Sale Completed';
      case NotificationType.backupCompleted:
        return isRTL ? 'نسخ احتياطي' : 'Backup';
      case NotificationType.backupFailed:
        return isRTL ? 'فشل النسخ' : 'Backup Failed';
      case NotificationType.systemAlert:
        return isRTL ? 'تنبيه النظام' : 'System Alert';
      case NotificationType.reminder:
        return isRTL ? 'تذكير' : 'Reminder';
    }
  }

  void _showClearAllDialog(
    BuildContext context,
    NotificationProvider provider,
    bool isRTL,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isRTL ? 'حذف جميع الإشعارات' : 'Clear All Notifications',
              style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
            ),
            content: Text(
              isRTL
                  ? 'هل أنت متأكد من حذف جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'
                  : 'Are you sure you want to clear all notifications? This action cannot be undone.',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  isRTL ? 'إلغاء' : 'Cancel',
                  style: GoogleFonts.cairo(),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  provider.clearAllNotifications();
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  isRTL ? 'حذف الكل' : 'Clear All',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
    );
  }

  void _showNotificationSettings(
    BuildContext context,
    NotificationProvider provider,
    bool isRTL,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) =>
              NotificationSettingsModal(provider: provider, isRTL: isRTL),
    );
  }
}
