<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E91E63;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F44336;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="speedGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF5722;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9800;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFEB3B;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Speedometer Background -->
  <circle cx="64" cy="80" r="40" fill="#F5F5F5" stroke="#E0E0E0" stroke-width="2"/>
  <circle cx="64" cy="80" r="32" fill="#FAFAFA"/>
  
  <!-- Speed Lines -->
  <path d="M32 80 L40 80" stroke="#666" stroke-width="2"/>
  <path d="M36 60 L42 66" stroke="#666" stroke-width="2"/>
  <path d="M64 48 L64 56" stroke="#666" stroke-width="2"/>
  <path d="M92 60 L86 66" stroke="#666" stroke-width="2"/>
  <path d="M96 80 L88 80" stroke="#666" stroke-width="2"/>
  <path d="M92 100 L86 94" stroke="#666" stroke-width="2"/>
  <path d="M36 100 L42 94" stroke="#666" stroke-width="2"/>
  
  <!-- Speed Needle -->
  <line x1="64" y1="80" x2="84" y2="64" stroke="url(#speedGradient)" stroke-width="3"/>
  <circle cx="64" cy="80" r="4" fill="url(#speedGradient)"/>
  
  <!-- Lightning Bolt -->
  <path d="M64 16 L56 48 L68 48 L60 80 L72 48 L60 48 Z" fill="url(#lightningGradient)"/>
  
  <!-- Speed Indicators -->
  <text x="32" y="85" font-family="Arial" font-size="8" fill="#666">0</text>
  <text x="60" y="52" font-family="Arial" font-size="8" fill="#666">50</text>
  <text x="92" y="85" font-family="Arial" font-size="8" fill="#666">100</text>
  
  <!-- Real-time Pulse -->
  <circle cx="64" cy="64" r="48" stroke="url(#glowGradient)" stroke-width="2" fill="none" opacity="0.6">
    <animate attributeName="r" values="45;55;45" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Speed Burst Lines -->
  <path d="M100 64 L112 64" stroke="url(#lightningGradient)" stroke-width="2" opacity="0.8">
    <animate attributeName="opacity" values="0.8;0.3;0.8" dur="1s" repeatCount="indefinite"/>
  </path>
  <path d="M104 56 L116 48" stroke="url(#lightningGradient)" stroke-width="2" opacity="0.6">
    <animate attributeName="opacity" values="0.6;0.2;0.6" dur="1.5s" repeatCount="indefinite"/>
  </path>
  <path d="M104 72 L116 80" stroke="url(#lightningGradient)" stroke-width="2" opacity="0.7">
    <animate attributeName="opacity" values="0.7;0.2;0.7" dur="1.2s" repeatCount="indefinite"/>
  </path>
  
  <!-- Digital Display -->
  <rect x="48" y="96" width="32" height="16" rx="4" fill="#263238"/>
  <text x="64" y="107" text-anchor="middle" font-family="monospace" font-size="8" fill="#4CAF50">REAL-TIME</text>
  
  <!-- Performance Bars -->
  <rect x="16" y="104" width="4" height="16" fill="url(#speedGradient)"/>
  <rect x="22" y="100" width="4" height="20" fill="url(#speedGradient)"/>
  <rect x="28" y="96" width="4" height="24" fill="url(#speedGradient)"/>
  
  <rect x="96" y="104" width="4" height="16" fill="url(#speedGradient)"/>
  <rect x="102" y="100" width="4" height="20" fill="url(#speedGradient)"/>
  <rect x="108" y="96" width="4" height="24" fill="url(#speedGradient)"/>
</svg>
