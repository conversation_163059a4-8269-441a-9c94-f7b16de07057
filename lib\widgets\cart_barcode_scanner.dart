import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

class CartBarcodeScanner extends StatefulWidget {
  const CartBarcodeScanner({super.key});

  @override
  State<CartBarcodeScanner> createState() => _CartBarcodeScannerState();
}

class _CartBarcodeScannerState extends State<CartBarcodeScanner> {
  bool _isScanning = false;

  Future<void> _scanBarcode() async {
    if (kIsWeb) {
      _showWebNotSupportedDialog();
      return;
    }

    setState(() {
      _isScanning = true;
    });

    try {
      final barcode = await FlutterBarcodeScanner.scanBarcode(
        '#4CAF50', // Green color matching app theme
        'Cancel',
        true,
        ScanMode.BARCODE,
      );

      if (barcode != '-1' && mounted) {
        await _processScannedBarcode(barcode);
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error scanning barcode: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }

  Future<void> _processScannedBarcode(String barcode) async {
    final productProvider = Provider.of<ProductProvider>(context, listen: false);
    final cartProvider = Provider.of<CartProvider>(context, listen: false);
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    try {
      final product = await productProvider.getProductByBarcode(barcode);

      if (product != null) {
        cartProvider.addItem(product);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const FaIcon(
                    FontAwesomeIcons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      localeProvider.isRTL
                          ? 'تم إضافة ${product.name} إلى السلة'
                          : '${product.name} added to cart',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green.shade700,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        }
      } else {
        if (mounted) {
          _showProductNotFoundDialog(barcode);
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Error processing barcode: $e');
      }
    }
  }

  void _showWebNotSupportedDialog() {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            FaIcon(
              FontAwesomeIcons.triangleExclamation,
              color: Colors.orange.shade600,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              localeProvider.isRTL ? 'غير مدعوم' : 'Not Supported',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          localeProvider.isRTL
              ? 'مسح الباركود غير مدعوم في متصفح الويب. يرجى استخدام التطبيق على الهاتف المحمول.'
              : 'Barcode scanning is not supported in web browsers. Please use the mobile app.',
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              localeProvider.isRTL ? 'موافق' : 'OK',
              style: GoogleFonts.cairo(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showProductNotFoundDialog(String barcode) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            FaIcon(
              FontAwesomeIcons.magnifyingGlass,
              color: Colors.red.shade600,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              localeProvider.isRTL ? 'منتج غير موجود' : 'Product Not Found',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              localeProvider.isRTL
                  ? 'لم يتم العثور على منتج بالباركود:'
                  : 'No product found with barcode:',
              style: GoogleFonts.cairo(),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                barcode,
                style: GoogleFonts.robotoMono(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              localeProvider.isRTL ? 'موافق' : 'OK',
              style: GoogleFonts.cairo(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            FaIcon(
              FontAwesomeIcons.triangleExclamation,
              color: Colors.red.shade600,
              size: 24,
            ),
            const SizedBox(width: 12),
            Text(
              localeProvider.isRTL ? 'خطأ' : 'Error',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          error,
          style: GoogleFonts.cairo(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              localeProvider.isRTL ? 'موافق' : 'OK',
              style: GoogleFonts.cairo(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Tooltip(
      message: localeProvider.isRTL ? 'مسح الباركود' : 'Scan Barcode',
      child: Container(
        decoration: BoxDecoration(
          color: Colors.green.shade700,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.green.shade700.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _isScanning ? null : _scanBarcode,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(12),
              child: _isScanning
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const FaIcon(
                      FontAwesomeIcons.qrcode,
                      color: Colors.white,
                      size: 20,
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
