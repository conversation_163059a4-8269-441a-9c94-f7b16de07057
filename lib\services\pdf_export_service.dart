import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/models/customer.dart';
import 'package:pos_app/models/invoice.dart';

class PDFExportService {
  static Future<pw.Font> _loadArabicFont() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // Fallback to default font if Arabic font is not available
      return pw.Font.helvetica();
    }
  }

  static Future<Uint8List> generateInventoryReport({
    required List<Product> products,
    required String title,
    required DateTime dateRange,
    required bool isRTL,
    String? companyName,
    String? companyLogo,
  }) async {
    final pdf = pw.Document();
    final font = await _loadArabicFont();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            _buildReportHeader(
              title: title,
              dateRange: dateRange,
              isRTL: isRTL,
              font: font,
              companyName: companyName,
            ),
            
            pw.SizedBox(height: 20),
            
            // Summary Statistics
            _buildInventorySummary(products, isRTL, font),
            
            pw.SizedBox(height: 20),
            
            // Products Table
            _buildInventoryTable(products, isRTL, font),
            
            pw.SizedBox(height: 20),
            
            // Low Stock Alert
            _buildLowStockAlert(products, isRTL, font),
          ];
        },
      ),
    );

    return pdf.save();
  }

  static Future<Uint8List> generateSalesReport({
    required List<Invoice> invoices,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required bool isRTL,
    String? companyName,
    String? companyLogo,
  }) async {
    final pdf = pw.Document();
    final font = await _loadArabicFont();

    final totalSales = invoices.fold<double>(0, (sum, invoice) => sum + invoice.finalAmount);
    final totalInvoices = invoices.length;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            _buildReportHeader(
              title: title,
              dateRange: startDate,
              endDate: endDate,
              isRTL: isRTL,
              font: font,
              companyName: companyName,
            ),
            
            pw.SizedBox(height: 20),
            
            // Sales Summary
            _buildSalesSummary(totalSales, totalInvoices, isRTL, font),
            
            pw.SizedBox(height: 20),
            
            // Sales Table
            _buildSalesTable(invoices, isRTL, font),
          ];
        },
      ),
    );

    return pdf.save();
  }

  static Future<Uint8List> generateCustomerReport({
    required List<Customer> customers,
    required String title,
    required DateTime dateRange,
    required bool isRTL,
    String? companyName,
    String? companyLogo,
  }) async {
    final pdf = pw.Document();
    final font = await _loadArabicFont();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            // Header
            _buildReportHeader(
              title: title,
              dateRange: dateRange,
              isRTL: isRTL,
              font: font,
              companyName: companyName,
            ),
            
            pw.SizedBox(height: 20),
            
            // Customer Summary
            _buildCustomerSummary(customers, isRTL, font),
            
            pw.SizedBox(height: 20),
            
            // Customers Table
            _buildCustomersTable(customers, isRTL, font),
          ];
        },
      ),
    );

    return pdf.save();
  }

  static pw.Widget _buildReportHeader({
    required String title,
    required DateTime dateRange,
    DateTime? endDate,
    required bool isRTL,
    required pw.Font font,
    String? companyName,
  }) {
    return pw.Column(
      crossAxisAlignment: isRTL ? pw.CrossAxisAlignment.end : pw.CrossAxisAlignment.start,
      children: [
        if (companyName != null)
          pw.Text(
            companyName,
            style: pw.TextStyle(
              font: font,
              fontSize: 24,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green,
            ),
            textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
          ),
        
        pw.SizedBox(height: 10),
        
        pw.Text(
          title,
          style: pw.TextStyle(
            font: font,
            fontSize: 20,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
        ),
        
        pw.SizedBox(height: 5),
        
        pw.Text(
          endDate != null
              ? (isRTL 
                  ? 'من ${_formatDate(dateRange)} إلى ${_formatDate(endDate)}'
                  : 'From ${_formatDate(dateRange)} to ${_formatDate(endDate)}')
              : (isRTL 
                  ? 'تاريخ التقرير: ${_formatDate(dateRange)}'
                  : 'Report Date: ${_formatDate(dateRange)}'),
          style: pw.TextStyle(
            font: font,
            fontSize: 12,
            color: PdfColors.grey700,
          ),
          textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
        ),
        
        pw.Divider(thickness: 2, color: PdfColors.green),
      ],
    );
  }

  static pw.Widget _buildInventorySummary(List<Product> products, bool isRTL, pw.Font font) {
    final totalProducts = products.length;
    final totalValue = products.fold<double>(0, (sum, product) => sum + (product.price * product.stock));
    final lowStockCount = products.where((p) => p.stock < 10).length;

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            isRTL ? 'إجمالي المنتجات' : 'Total Products',
            totalProducts.toString(),
            isRTL,
            font,
          ),
          _buildSummaryItem(
            isRTL ? 'قيمة المخزون' : 'Inventory Value',
            totalValue.toStringAsFixed(2),
            isRTL,
            font,
          ),
          _buildSummaryItem(
            isRTL ? 'مخزون قليل' : 'Low Stock',
            lowStockCount.toString(),
            isRTL,
            font,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSalesSummary(double totalSales, int totalInvoices, bool isRTL, pw.Font font) {
    final averageSale = totalInvoices > 0 ? totalSales / totalInvoices : 0.0;

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            isRTL ? 'إجمالي المبيعات' : 'Total Sales',
            totalSales.toStringAsFixed(2),
            isRTL,
            font,
          ),
          _buildSummaryItem(
            isRTL ? 'عدد الفواتير' : 'Total Invoices',
            totalInvoices.toString(),
            isRTL,
            font,
          ),
          _buildSummaryItem(
            isRTL ? 'متوسط البيع' : 'Average Sale',
            averageSale.toStringAsFixed(2),
            isRTL,
            font,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildCustomerSummary(List<Customer> customers, bool isRTL, pw.Font font) {
    final totalCustomers = customers.length;
    final totalDebt = customers.fold<double>(0, (sum, customer) => sum + customer.balance);
    final customersWithDebt = customers.where((c) => c.balance > 0).length;

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.orange50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem(
            isRTL ? 'إجمالي العملاء' : 'Total Customers',
            totalCustomers.toString(),
            isRTL,
            font,
          ),
          _buildSummaryItem(
            isRTL ? 'إجمالي الديون' : 'Total Debt',
            totalDebt.toStringAsFixed(2),
            isRTL,
            font,
          ),
          _buildSummaryItem(
            isRTL ? 'عملاء مدينون' : 'Customers with Debt',
            customersWithDebt.toString(),
            isRTL,
            font,
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSummaryItem(String title, String value, bool isRTL, pw.Font font) {
    return pw.Column(
      children: [
        pw.Text(
          value,
          style: pw.TextStyle(
            font: font,
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.green,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          title,
          style: pw.TextStyle(
            font: font,
            fontSize: 10,
            color: PdfColors.grey700,
          ),
          textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
        ),
      ],
    );
  }

  static pw.Widget _buildInventoryTable(List<Product> products, bool isRTL, pw.Font font) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell(isRTL ? 'اسم المنتج' : 'Product Name', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'الفئة' : 'Category', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'المخزون' : 'Stock', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'السعر' : 'Price', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'القيمة' : 'Value', font, isHeader: true, isRTL: isRTL),
          ],
        ),
        // Data rows
        ...products.take(20).map((product) => pw.TableRow(
          children: [
            _buildTableCell(product.name, font, isRTL: isRTL),
            _buildTableCell(product.category, font, isRTL: isRTL),
            _buildTableCell(product.stock.toString(), font, isRTL: isRTL),
            _buildTableCell(product.price.toStringAsFixed(2), font, isRTL: isRTL),
            _buildTableCell((product.price * product.stock).toStringAsFixed(2), font, isRTL: isRTL),
          ],
        )),
      ],
    );
  }

  static pw.Widget _buildSalesTable(List<Invoice> invoices, bool isRTL, pw.Font font) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell(isRTL ? 'رقم الفاتورة' : 'Invoice #', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'العميل' : 'Customer', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'التاريخ' : 'Date', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'المبلغ' : 'Amount', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'الحالة' : 'Status', font, isHeader: true, isRTL: isRTL),
          ],
        ),
        // Data rows
        ...invoices.take(20).map((invoice) => pw.TableRow(
          children: [
            _buildTableCell(invoice.invoiceNumber, font, isRTL: isRTL),
            _buildTableCell(invoice.customerName, font, isRTL: isRTL),
            _buildTableCell(_formatDate(invoice.date), font, isRTL: isRTL),
            _buildTableCell(invoice.finalAmount.toStringAsFixed(2), font, isRTL: isRTL),
            _buildTableCell(invoice.status, font, isRTL: isRTL),
          ],
        )),
      ],
    );
  }

  static pw.Widget _buildCustomersTable(List<Customer> customers, bool isRTL, pw.Font font) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell(isRTL ? 'اسم العميل' : 'Customer Name', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'الهاتف' : 'Phone', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'الإيميل' : 'Email', font, isHeader: true, isRTL: isRTL),
            _buildTableCell(isRTL ? 'الرصيد' : 'Balance', font, isHeader: true, isRTL: isRTL),
          ],
        ),
        // Data rows
        ...customers.take(20).map((customer) => pw.TableRow(
          children: [
            _buildTableCell(customer.name, font, isRTL: isRTL),
            _buildTableCell(customer.phone ?? '', font, isRTL: isRTL),
            _buildTableCell(customer.email ?? '', font, isRTL: isRTL),
            _buildTableCell(customer.balance.toStringAsFixed(2), font, isRTL: isRTL),
          ],
        )),
      ],
    );
  }

  static pw.Widget _buildTableCell(String text, pw.Font font, {bool isHeader = false, required bool isRTL}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
      ),
    );
  }

  static pw.Widget _buildLowStockAlert(List<Product> products, bool isRTL, pw.Font font) {
    final lowStockProducts = products.where((p) => p.stock < 10).toList();
    
    if (lowStockProducts.isEmpty) {
      return pw.SizedBox.shrink();
    }

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.red50,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: PdfColors.red200),
      ),
      child: pw.Column(
        crossAxisAlignment: isRTL ? pw.CrossAxisAlignment.end : pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            isRTL ? '⚠️ تنبيه: منتجات بمخزون قليل' : '⚠️ Alert: Low Stock Products',
            style: pw.TextStyle(
              font: font,
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.red,
            ),
            textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
          ),
          pw.SizedBox(height: 8),
          ...lowStockProducts.take(5).map((product) => pw.Text(
            '• ${product.name}: ${product.stock} ${isRTL ? 'قطعة متبقية' : 'remaining'}',
            style: pw.TextStyle(font: font, fontSize: 10),
            textDirection: isRTL ? pw.TextDirection.rtl : pw.TextDirection.ltr,
          )),
        ],
      ),
    );
  }

  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static Future<void> shareReport(Uint8List pdfBytes, String fileName) async {
    final tempDir = await getTemporaryDirectory();
    final file = File('${tempDir.path}/$fileName.pdf');
    await file.writeAsBytes(pdfBytes);
    
    await Share.shareXFiles([XFile(file.path)], text: 'Report: $fileName');
  }

  static Future<void> printReport(Uint8List pdfBytes) async {
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdfBytes);
  }
}
