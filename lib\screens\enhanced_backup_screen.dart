import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pos_app/providers/backup_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

class EnhancedBackupScreen extends StatefulWidget {
  const EnhancedBackupScreen({super.key});

  @override
  State<EnhancedBackupScreen> createState() => _EnhancedBackupScreenState();
}

class _EnhancedBackupScreenState extends State<EnhancedBackupScreen> {
  final TextEditingController _backupNameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _showPassword = false;

  @override
  void dispose() {
    _backupNameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'النسخ الاحتياطي' : 'Database Backup'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
      ),
      body: Consumer<BackupProvider>(
        builder: (context, backup, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStatusCard(backup, isRTL, theme),
                const SizedBox(height: 24),
                _buildGoogleDriveSection(backup, isRTL, theme),
                const SizedBox(height: 24),
                _buildLocalBackupSection(backup, isRTL, theme),
                const SizedBox(height: 24),
                _buildBackupHistorySection(backup, isRTL, theme),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(BackupProvider backup, bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getStatusIcon(backup.status),
                  color: _getStatusColor(backup.status, theme),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _getStatusText(backup.status, isRTL),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (backup.isOperationInProgress) ...[
              const SizedBox(height: 16),
              LinearProgressIndicator(
                value: backup.progress,
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${(backup.progress * 100).toInt()}%',
                style: theme.textTheme.bodySmall,
              ),
            ],
            if (backup.errorMessage != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.error,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        backup.errorMessage!,
                        style: TextStyle(
                          color: theme.colorScheme.error,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: backup.clearError,
                      icon: Icon(
                        Icons.close,
                        color: theme.colorScheme.error,
                        size: 18,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleDriveSection(BackupProvider backup, bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                FaIcon(
                  FontAwesomeIcons.googleDrive,
                  color: Colors.blue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  isRTL ? 'Google Drive' : 'Google Drive',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (backup.isGoogleDriveSignedIn) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isRTL ? 'متصل بـ Google Drive' : 'Connected to Google Drive',
                            style: TextStyle(
                              color: theme.colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (backup.googleDriveUserEmail.isNotEmpty)
                            Text(
                              backup.googleDriveUserEmail,
                              style: TextStyle(
                                color: theme.colorScheme.primary,
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: backup.isOperationInProgress
                          ? null
                          : () => _exportToGoogleDrive(backup, isRTL),
                      icon: const FaIcon(FontAwesomeIcons.cloudArrowUp, size: 16),
                      label: Text(isRTL ? 'تصدير إلى Drive' : 'Export to Drive'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton(
                    onPressed: backup.isOperationInProgress
                        ? null
                        : backup.signOutFromGoogleDrive,
                    child: Text(isRTL ? 'قطع الاتصال' : 'Disconnect'),
                  ),
                ],
              ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: theme.colorScheme.outline,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isRTL
                            ? 'قم بتسجيل الدخول إلى Google Drive لحفظ النسخ الاحتياطية في السحابة'
                            : 'Sign in to Google Drive to save backups to the cloud',
                        style: TextStyle(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: backup.isOperationInProgress
                      ? null
                      : backup.signInToGoogleDrive,
                  icon: const FaIcon(FontAwesomeIcons.google, size: 16),
                  label: Text(isRTL ? 'تسجيل الدخول إلى Google' : 'Sign in to Google'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocalBackupSection(BackupProvider backup, bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  isRTL ? 'النسخ المحلي' : 'Local Backup',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _backupNameController,
              decoration: InputDecoration(
                labelText: isRTL ? 'اسم النسخة الاحتياطية (اختياري)' : 'Backup Name (Optional)',
                hintText: isRTL ? 'backup_${DateTime.now().day}_${DateTime.now().month}' : 'backup_${DateTime.now().day}_${DateTime.now().month}',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.label_outline),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              obscureText: !_showPassword,
              decoration: InputDecoration(
                labelText: isRTL ? 'كلمة مرور التشفير (اختياري)' : 'Encryption Password (Optional)',
                hintText: isRTL ? 'لحماية إضافية' : 'For extra security',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: const Icon(Icons.lock_outline),
                suffixIcon: IconButton(
                  onPressed: () => setState(() => _showPassword = !_showPassword),
                  icon: Icon(_showPassword ? Icons.visibility_off : Icons.visibility),
                ),
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: backup.isOperationInProgress
                    ? null
                    : () => _createLocalBackup(backup),
                icon: const Icon(Icons.save),
                label: Text(isRTL ? 'إنشاء نسخة احتياطية' : 'Create Backup'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupHistorySection(BackupProvider backup, bool isRTL, ThemeData theme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  isRTL ? 'سجل النسخ الاحتياطي' : 'Backup History',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (backup.backupHistory.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(
                        Icons.backup_outlined,
                        size: 64,
                        color: theme.colorScheme.outline,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isRTL ? 'لا توجد نسخ احتياطية' : 'No backups yet',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                      Text(
                        isRTL ? 'قم بإنشاء أول نسخة احتياطية' : 'Create your first backup',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...backup.backupHistory.take(10).map((backupInfo) {
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: backupInfo.isCloudBackup
                        ? Colors.blue.withValues(alpha: 0.2)
                        : theme.colorScheme.primaryContainer,
                    child: FaIcon(
                      backupInfo.isCloudBackup
                          ? FontAwesomeIcons.cloud
                          : FontAwesomeIcons.hardDrive,
                      size: 16,
                      color: backupInfo.isCloudBackup
                          ? Colors.blue
                          : theme.colorScheme.primary,
                    ),
                  ),
                  title: Text(backupInfo.name),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(_formatDate(backupInfo.createdAt, isRTL)),
                      Text(backup.formatFileSize(backupInfo.size)),
                    ],
                  ),
                  trailing: PopupMenuButton(
                    itemBuilder: (context) => [
                      if (!backupInfo.isCloudBackup)
                        PopupMenuItem(
                          value: 'restore',
                          child: Row(
                            children: [
                              const Icon(Icons.restore),
                              const SizedBox(width: 8),
                              Text(isRTL ? 'استعادة' : 'Restore'),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            const Icon(Icons.delete, color: Colors.red),
                            const SizedBox(width: 8),
                            Text(
                              isRTL ? 'حذف' : 'Delete',
                              style: const TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (value) {
                      if (value == 'delete') {
                        _deleteBackup(backup, backupInfo, isRTL);
                      } else if (value == 'restore') {
                        _restoreBackup(backup, backupInfo, isRTL);
                      }
                    },
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  void _createLocalBackup(BackupProvider backup) async {
    final success = await backup.createBackup(
      customName: _backupNameController.text.trim().isEmpty
          ? null
          : _backupNameController.text.trim(),
      encryptionPassword: _passwordController.text.trim().isEmpty
          ? null
          : _passwordController.text.trim(),
    );

    if (success) {
      _backupNameController.clear();
      _passwordController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LocaleProvider>(context, listen: false).isRTL
                  ? 'تم إنشاء النسخة الاحتياطية بنجاح'
                  : 'Backup created successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _exportToGoogleDrive(BackupProvider backup, bool isRTL) async {
    final success = await backup.exportToGoogleDrive(
      customName: _backupNameController.text.trim().isEmpty
          ? null
          : _backupNameController.text.trim(),
      encryptionPassword: _passwordController.text.trim().isEmpty
          ? null
          : _passwordController.text.trim(),
    );

    if (success) {
      _backupNameController.clear();
      _passwordController.clear();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRTL
                  ? 'تم تصدير النسخة الاحتياطية إلى Google Drive بنجاح'
                  : 'Backup exported to Google Drive successfully',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  void _deleteBackup(BackupProvider backup, BackupInfo backupInfo, bool isRTL) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isRTL ? 'حذف النسخة الاحتياطية' : 'Delete Backup'),
        content: Text(
          isRTL
              ? 'هل أنت متأكد من حذف "${backupInfo.name}"؟'
              : 'Are you sure you want to delete "${backupInfo.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(isRTL ? 'إلغاء' : 'Cancel'),
          ),
          TextButton(
            onPressed: () {
              backup.deleteBackup(backupInfo);
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text(isRTL ? 'حذف' : 'Delete'),
          ),
        ],
      ),
    );
  }

  void _restoreBackup(BackupProvider backup, BackupInfo backupInfo, bool isRTL) {
    // Implementation for restore functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL
              ? 'ميزة الاستعادة قيد التطوير'
              : 'Restore feature coming soon',
        ),
      ),
    );
  }

  IconData _getStatusIcon(BackupStatus status) {
    switch (status) {
      case BackupStatus.idle:
        return Icons.backup_outlined;
      case BackupStatus.creating:
      case BackupStatus.uploading:
        return Icons.cloud_upload;
      case BackupStatus.completed:
        return Icons.check_circle;
      case BackupStatus.failed:
        return Icons.error;
      default:
        return Icons.backup_outlined;
    }
  }

  Color _getStatusColor(BackupStatus status, ThemeData theme) {
    switch (status) {
      case BackupStatus.completed:
        return Colors.green;
      case BackupStatus.failed:
        return theme.colorScheme.error;
      case BackupStatus.creating:
      case BackupStatus.uploading:
        return theme.colorScheme.primary;
      default:
        return theme.colorScheme.outline;
    }
  }

  String _getStatusText(BackupStatus status, bool isRTL) {
    switch (status) {
      case BackupStatus.idle:
        return isRTL ? 'جاهز' : 'Ready';
      case BackupStatus.creating:
        return isRTL ? 'إنشاء النسخة الاحتياطية...' : 'Creating backup...';
      case BackupStatus.uploading:
        return isRTL ? 'رفع إلى السحابة...' : 'Uploading to cloud...';
      case BackupStatus.completed:
        return isRTL ? 'مكتمل' : 'Completed';
      case BackupStatus.failed:
        return isRTL ? 'فشل' : 'Failed';
      default:
        return isRTL ? 'جاهز' : 'Ready';
    }
  }

  String _formatDate(DateTime date, bool isRTL) {
    if (isRTL) {
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } else {
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    }
  }
}
