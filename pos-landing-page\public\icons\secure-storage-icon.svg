<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="databaseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00BCD4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="lockGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFC107;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9800;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Database Cylinder -->
  <ellipse cx="64" cy="40" rx="32" ry="12" fill="#B3E5FC"/>
  <rect x="32" y="40" width="64" height="48" fill="url(#databaseGradient)"/>
  <ellipse cx="64" cy="88" rx="32" ry="12" fill="#81D4FA"/>
  
  <!-- Database Layers -->
  <ellipse cx="64" cy="56" rx="32" ry="8" fill="#4FC3F7" opacity="0.8"/>
  <ellipse cx="64" cy="72" rx="32" ry="8" fill="#29B6F6" opacity="0.8"/>
  
  <!-- Shield -->
  <path d="M64 16 L80 24 L80 48 C80 64 72 76 64 80 C56 76 48 64 48 48 L48 24 Z" fill="url(#shieldGradient)" stroke="#388E3C" stroke-width="2"/>
  
  <!-- Lock -->
  <rect x="56" y="96" width="16" height="20" rx="2" fill="url(#lockGradient)"/>
  <path d="M60 96 L60 92 C60 88 62 86 64 86 C66 86 68 88 68 92 L68 96" stroke="#F57C00" stroke-width="2" fill="none"/>
  <circle cx="64" cy="104" r="2" fill="#F57C00"/>
  <line x1="64" y1="106" x2="64" y2="110" stroke="#F57C00" stroke-width="2"/>
  
  <!-- Security Elements -->
  <circle cx="64" cy="40" r="8" fill="#E1F5FE" opacity="0.8"/>
  <path d="M60 36 L62 38 L68 32" stroke="#0277BD" stroke-width="2" fill="none"/>
  
  <!-- Encryption Lines -->
  <line x1="40" y1="48" x2="88" y2="48" stroke="#0288D1" stroke-width="1" opacity="0.6"/>
  <line x1="40" y1="52" x2="88" y2="52" stroke="#0288D1" stroke-width="1" opacity="0.6"/>
  <line x1="40" y1="64" x2="88" y2="64" stroke="#0288D1" stroke-width="1" opacity="0.6"/>
  <line x1="40" y1="68" x2="88" y2="68" stroke="#0288D1" stroke-width="1" opacity="0.6"/>
  <line x1="40" y1="80" x2="88" y2="80" stroke="#0288D1" stroke-width="1" opacity="0.6"/>
  <line x1="40" y1="84" x2="88" y2="84" stroke="#0288D1" stroke-width="1" opacity="0.6"/>
  
  <!-- 256-bit Text -->
  <text x="64" y="36" text-anchor="middle" font-family="monospace" font-size="6" fill="#01579B" font-weight="bold">256-BIT</text>
  
  <!-- Security Glow -->
  <circle cx="64" cy="64" r="40" stroke="url(#shieldGradient)" stroke-width="1" fill="none" opacity="0.3">
    <animate attributeName="opacity" values="0.3;0.6;0.3" dur="3s" repeatCount="indefinite"/>
  </circle>
</svg>
