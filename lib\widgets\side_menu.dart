import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/utils/localization_helper.dart';

class SideMenu extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const SideMenu({
    super.key,
    required this.selectedIndex,
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Drawer(
      backgroundColor: Colors.green.shade50,
      elevation: 0,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.green.shade700, Colors.green.shade600],
              ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.white.withOpacity(0.2),
                      radius: 24,
                      child: Icon(FontAwesomeIcons.store, color: Colors.white),
                    ),
                    const SizedBox(width: 16),
                    Consumer<LocaleProvider>(
                      builder:
                          (context, localeProvider, _) => Text(
                            LocalizationHelper.getAppTitle(context),
                            style: GoogleFonts.cairo(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Consumer<LocaleProvider>(
                  builder:
                      (context, localeProvider, _) => Text(
                        localeProvider.isRTL
                            ? 'أدر أعمالك بكفاءة وسهولة'
                            : 'Manage your business efficiently',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildSectionHeader(context, 'الرئيسية', 'Main'),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.gaugeHigh,
                  label: 'لوحة التحكم',
                  englishLabel: 'Dashboard',
                  index: 0,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.cashRegister,
                  label: 'نقطة البيع',
                  englishLabel: 'POS',
                  index: 1,
                ),
                const Divider(color: Colors.grey),
                _buildSectionHeader(context, 'المنتجات', 'Products'),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.box,
                  label: 'المنتجات',
                  englishLabel: 'Products',
                  index: 2,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.tags,
                  label: 'الفئات',
                  englishLabel: 'Categories',
                  index: 4,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.warehouse,
                  label: 'المخزون',
                  englishLabel: 'Inventory',
                  index: 9,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.qrcode,
                  label: 'إدارة الباركود',
                  englishLabel: 'Barcode Management',
                  index: 12,
                ),
                const Divider(color: Colors.grey),
                _buildSectionHeader(context, 'العملاء والموردين', 'Business'),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.users,
                  label: 'العملاء',
                  englishLabel: 'Customers',
                  index: 3,
                ),

                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.truck,
                  label: 'الموردين',
                  englishLabel: 'Suppliers',
                  index: 6,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.creditCard,
                  label: 'إدارة الديون',
                  englishLabel: 'Debt Management',
                  index: 14,
                ),
                const Divider(color: Colors.grey),
                _buildSectionHeader(context, 'المالية', 'Finance'),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.fileInvoice,
                  label: 'الفواتير',
                  englishLabel: 'Invoices',
                  index: 5,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.moneyBillWave,
                  label: 'المصروفات',
                  englishLabel: 'Expenses',
                  index: 8,
                ),

                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.chartBar,
                  label: 'التقارير',
                  englishLabel: 'Reports',
                  index: 7,
                ),
                const Divider(color: Colors.grey),
                _buildSectionHeader(context, 'النسخ الاحتياطي', 'Backup'),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.cloudArrowUp,
                  label: 'النسخ الاحتياطي',
                  englishLabel: 'Enhanced Backup',
                  index: 13,
                ),
                const Divider(color: Colors.grey),
                _buildSectionHeader(context, 'النظام', 'System'),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.userGear,
                  label: 'الملف الشخصي',
                  englishLabel: 'Profile',
                  index: 10,
                ),
                _buildNavItem(
                  context: context,
                  icon: FontAwesomeIcons.gear,
                  label: 'الإعدادات',
                  englishLabel: 'Settings',
                  index: 11,
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.green.shade100,
                  child: Icon(
                    FontAwesomeIcons.user,
                    color: Colors.green.shade700,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Admin User',
                        style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        'Store Manager',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.logout,
                    size: 20,
                    color: Colors.green.shade700,
                  ),
                  onPressed: () {
                    // Implement logout functionality
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String arTitle,
    String enTitle,
  ) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 8),
      child: Text(
        localeProvider.isRTL ? arTitle : enTitle,
        style: GoogleFonts.cairo(
          color: Colors.green.shade700,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String englishLabel,
    required int index,
  }) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isSelected = selectedIndex == index;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onItemSelected(index),
          borderRadius: BorderRadius.circular(10),
          child: Container(
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? Colors.green.withValues(alpha: 0.15)
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: isSelected ? Colors.green.shade700 : Colors.transparent,
                width: 1,
              ),
            ),
            child: ListTile(
              leading: FaIcon(
                icon,
                size: 18,
                color: isSelected ? Colors.green.shade700 : Colors.grey[600],
              ),
              title: Text(
                localeProvider.isRTL ? label : englishLabel,
                style: GoogleFonts.cairo(
                  fontSize: 15,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? Colors.green.shade700 : Colors.grey[800],
                ),
              ),
              dense: true,
              visualDensity: VisualDensity.compact,
            ),
          ),
        ),
      ),
    );
  }
}
