import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/backup_provider.dart';
import '../providers/localization_provider.dart';

class BackupSettingsScreen extends StatefulWidget {
  const BackupSettingsScreen({super.key});

  @override
  State<BackupSettingsScreen> createState() => _BackupSettingsScreenState();
}

class _BackupSettingsScreenState extends State<BackupSettingsScreen> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _backupNameController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    _backupNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocalizationProvider>(
          builder:
              (context, localization, _) =>
                  Text(localization.translate('backup')),
        ),
      ),
      body: Consumer<BackupProvider>(
        builder:
            (context, backup, _) => ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // Backup Status Card
                _buildStatusCard(backup),

                const SizedBox(height: 16),

                // Quick Actions
                _buildQuickActionsCard(backup),

                const SizedBox(height: 16),

                // Backup Settings
                _buildBackupSettingsCard(backup),

                const SizedBox(height: 16),

                // Backup History
                _buildBackupHistoryCard(backup),
              ],
            ),
      ),
    );
  }

  Widget _buildStatusCard(BackupProvider backup) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.database,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Backup Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (backup.isOperationInProgress) ...[
              LinearProgressIndicator(value: backup.progress),
              const SizedBox(height: 8),
              Text('${(backup.progress * 100).toInt()}% Complete'),
              const SizedBox(height: 8),
              Text(_getStatusText(backup.status)),
            ] else ...[
              Row(
                children: [
                  Icon(
                    _getStatusIcon(backup.status),
                    color: _getStatusColor(backup.status),
                  ),
                  const SizedBox(width: 8),
                  Text(_getStatusText(backup.status)),
                ],
              ),
              if (backup.lastBackupDate != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Last backup: ${_formatDate(backup.lastBackupDate!)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ],

            if (backup.errorMessage != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        backup.errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 16),
                      onPressed: backup.clearError,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsCard(BackupProvider backup) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        backup.isOperationInProgress
                            ? null
                            : () => _createBackup(backup),
                    icon: const Icon(FontAwesomeIcons.plus),
                    label: const Text('Create Backup'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed:
                        backup.backupHistory.isEmpty
                            ? null
                            : () => _showRestoreDialog(backup),
                    icon: const Icon(FontAwesomeIcons.download),
                    label: const Text('Restore'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupSettingsCard(BackupProvider backup) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Backup Settings',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Schedule Settings
            ListTile(
              title: const Text('Backup Schedule'),
              subtitle: Text(_getScheduleText(backup.scheduleType)),
              trailing: DropdownButton<BackupType>(
                value: backup.scheduleType,
                onChanged: (value) {
                  if (value != null) {
                    backup.setBackupSchedule(value);
                  }
                },
                items:
                    BackupType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(_getScheduleText(type)),
                      );
                    }).toList(),
              ),
            ),

            // Encryption Settings
            SwitchListTile(
              title: const Text('Enable Encryption'),
              subtitle: const Text('Protect backups with password'),
              value: backup.isEncryptionEnabled,
              onChanged: backup.setEncryptionEnabled,
            ),

            // Cloud Backup Settings
            SwitchListTile(
              title: const Text('Cloud Backup'),
              subtitle: const Text('Save backups to Google Drive'),
              value: backup.isCloudBackupEnabled,
              onChanged: backup.setCloudBackupEnabled,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackupHistoryCard(BackupProvider backup) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Backup History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${backup.backupHistory.length} backups',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (backup.backupHistory.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        FontAwesomeIcons.database,
                        size: 48,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text('No backups yet'),
                      Text('Create your first backup to get started'),
                    ],
                  ),
                ),
              )
            else
              ...backup.backupHistory.take(5).map((backupInfo) {
                return ListTile(
                  leading: Icon(
                    backupInfo.isCloudBackup
                        ? FontAwesomeIcons.cloud
                        : FontAwesomeIcons.hardDrive,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(backupInfo.name),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(_formatDate(backupInfo.createdAt)),
                      Text(backup.formatFileSize(backupInfo.size)),
                    ],
                  ),
                  trailing: PopupMenuButton(
                    itemBuilder:
                        (context) => [
                          PopupMenuItem(
                            value: 'restore',
                            child: const Row(
                              children: [
                                Icon(FontAwesomeIcons.download),
                                SizedBox(width: 8),
                                Text('Restore'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'delete',
                            child: const Row(
                              children: [
                                Icon(FontAwesomeIcons.trash, color: Colors.red),
                                SizedBox(width: 8),
                                Text(
                                  'Delete',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        ],
                    onSelected: (value) {
                      if (value == 'restore') {
                        _restoreBackup(backup, backupInfo);
                      } else if (value == 'delete') {
                        _deleteBackup(backup, backupInfo);
                      }
                    },
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  String _getStatusText(BackupStatus status) {
    switch (status) {
      case BackupStatus.idle:
        return 'Ready';
      case BackupStatus.creating:
        return 'Creating backup...';
      case BackupStatus.uploading:
        return 'Uploading to cloud...';
      case BackupStatus.downloading:
        return 'Downloading backup...';
      case BackupStatus.restoring:
        return 'Restoring data...';
      case BackupStatus.completed:
        return 'Completed';
      case BackupStatus.failed:
        return 'Failed';
    }
  }

  IconData _getStatusIcon(BackupStatus status) {
    switch (status) {
      case BackupStatus.idle:
        return FontAwesomeIcons.clock;
      case BackupStatus.completed:
        return FontAwesomeIcons.check;
      case BackupStatus.failed:
        return FontAwesomeIcons.xmark;
      default:
        return FontAwesomeIcons.spinner;
    }
  }

  Color _getStatusColor(BackupStatus status) {
    switch (status) {
      case BackupStatus.completed:
        return Colors.green;
      case BackupStatus.failed:
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  String _getScheduleText(BackupType type) {
    switch (type) {
      case BackupType.manual:
        return 'Manual';
      case BackupType.daily:
        return 'Daily';
      case BackupType.weekly:
        return 'Weekly';
      case BackupType.monthly:
        return 'Monthly';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _createBackup(BackupProvider backup) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Create Backup'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _backupNameController,
                  decoration: const InputDecoration(
                    labelText: 'Backup Name (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                if (backup.isEncryptionEnabled) ...[
                  const SizedBox(height: 16),
                  TextField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: 'Encryption Password',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  backup.createBackup(
                    customName:
                        _backupNameController.text.isEmpty
                            ? null
                            : _backupNameController.text,
                    encryptionPassword:
                        _passwordController.text.isEmpty
                            ? null
                            : _passwordController.text,
                  );
                  _backupNameController.clear();
                  _passwordController.clear();
                },
                child: const Text('Create'),
              ),
            ],
          ),
    );
  }

  void _showRestoreDialog(BackupProvider backup) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Backup to Restore'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: backup.backupHistory.length,
                itemBuilder: (context, index) {
                  final backupInfo = backup.backupHistory[index];
                  return ListTile(
                    title: Text(backupInfo.name),
                    subtitle: Text(_formatDate(backupInfo.createdAt)),
                    onTap: () {
                      Navigator.pop(context);
                      _restoreBackup(backup, backupInfo);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  void _restoreBackup(BackupProvider backup, BackupInfo backupInfo) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Restore Backup'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Are you sure you want to restore "${backupInfo.name}"?'),
                const SizedBox(height: 8),
                const Text(
                  'This will replace all current data.',
                  style: TextStyle(color: Colors.red),
                ),
                if (backupInfo.isEncrypted) ...[
                  const SizedBox(height: 16),
                  TextField(
                    controller: _passwordController,
                    decoration: const InputDecoration(
                      labelText: 'Decryption Password',
                      border: OutlineInputBorder(),
                    ),
                    obscureText: true,
                  ),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  backup.restoreBackup(
                    backupInfo,
                    decryptionPassword:
                        _passwordController.text.isEmpty
                            ? null
                            : _passwordController.text,
                  );
                  _passwordController.clear();
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Restore'),
              ),
            ],
          ),
    );
  }

  void _deleteBackup(BackupProvider backup, BackupInfo backupInfo) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Backup'),
            content: Text(
              'Are you sure you want to delete "${backupInfo.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  backup.deleteBackup(backupInfo);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }
}
