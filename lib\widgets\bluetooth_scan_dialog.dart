import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/bluetooth_provider.dart';

class BluetoothScanDialog extends StatefulWidget {
  final bool isRTL;

  const BluetoothScanDialog({super.key, required this.isRTL});

  @override
  State<BluetoothScanDialog> createState() => _BluetoothScanDialogState();
}

class _BluetoothScanDialogState extends State<BluetoothScanDialog> {
  @override
  void initState() {
    super.initState();
    // Start scanning when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BluetoothProvider>().startScan();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: isTablet ? 500 : double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  FaIcon(
                    FontAwesomeIcons.bluetooth,
                    color: Colors.white,
                    size: isTablet ? 24 : 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      widget.isRTL
                          ? 'البحث عن طابعات البلوتوث'
                          : 'Bluetooth Printer Scan',
                      style: GoogleFonts.cairo(
                        fontSize: isTablet ? 18 : 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Consumer<BluetoothProvider>(
                builder: (context, bluetoothProvider, _) {
                  return Column(
                    children: [
                      // Status Section
                      Container(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          children: [
                            if (bluetoothProvider.isScanning) ...[
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    theme.colorScheme.primary,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                widget.isRTL ? 'جاري البحث...' : 'Scanning...',
                                style: GoogleFonts.cairo(
                                  fontSize: isTablet ? 16 : 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ] else ...[
                              FaIcon(
                                FontAwesomeIcons.magnifyingGlass,
                                color: theme.colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                widget.isRTL
                                    ? 'تم العثور على ${bluetoothProvider.availableDevices.length} جهاز'
                                    : 'Found ${bluetoothProvider.availableDevices.length} devices',
                                style: GoogleFonts.cairo(
                                  fontSize: isTablet ? 16 : 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // Error Message
                      if (bluetoothProvider.errorMessage != null)
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.withValues(alpha: 0.3),
                            ),
                          ),
                          child: Row(
                            children: [
                              const Icon(
                                Icons.error,
                                color: Colors.red,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  bluetoothProvider.errorMessage!,
                                  style: GoogleFonts.cairo(
                                    fontSize: isTablet ? 14 : 12,
                                    color: Colors.red,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // Device List
                      Expanded(
                        child:
                            bluetoothProvider.availableDevices.isEmpty
                                ? _buildEmptyState(
                                  context,
                                  bluetoothProvider,
                                  isTablet,
                                )
                                : ListView.builder(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                  ),
                                  itemCount:
                                      bluetoothProvider.availableDevices.length,
                                  itemBuilder: (context, index) {
                                    final device =
                                        bluetoothProvider
                                            .availableDevices[index];
                                    return _buildDeviceTile(
                                      context,
                                      device,
                                      bluetoothProvider,
                                      isTablet,
                                    );
                                  },
                                ),
                      ),
                    ],
                  );
                },
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(16),
              child: Consumer<BluetoothProvider>(
                builder: (context, bluetoothProvider, _) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed:
                              bluetoothProvider.isScanning
                                  ? () => bluetoothProvider.stopScan()
                                  : () => bluetoothProvider.startScan(),
                          icon: FaIcon(
                            bluetoothProvider.isScanning
                                ? FontAwesomeIcons.stop
                                : FontAwesomeIcons.magnifyingGlass,
                            size: 16,
                          ),
                          label: Text(
                            bluetoothProvider.isScanning
                                ? (widget.isRTL ? 'إيقاف' : 'Stop')
                                : (widget.isRTL ? 'إعادة البحث' : 'Rescan'),
                            style: GoogleFonts.cairo(
                              fontSize: isTablet ? 14 : 12,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text(
                            widget.isRTL ? 'إغلاق' : 'Close',
                            style: GoogleFonts.cairo(
                              fontSize: isTablet ? 14 : 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(
    BuildContext context,
    BluetoothProvider bluetoothProvider,
    bool isTablet,
  ) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              bluetoothProvider.isScanning
                  ? FontAwesomeIcons.spinner
                  : FontAwesomeIcons.bluetooth,
              size: isTablet ? 64 : 48,
              color: theme.colorScheme.primary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              bluetoothProvider.isScanning
                  ? (widget.isRTL
                      ? 'جاري البحث عن الأجهزة...'
                      : 'Searching for devices...')
                  : (widget.isRTL
                      ? 'لم يتم العثور على أجهزة'
                      : 'No devices found'),
              style: GoogleFonts.cairo(
                fontSize: isTablet ? 16 : 14,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (!bluetoothProvider.isScanning) ...[
              const SizedBox(height: 8),
              Text(
                widget.isRTL
                    ? 'تأكد من تشغيل البلوتوث وأن الطابعة في وضع الاقتران'
                    : 'Make sure Bluetooth is on and printer is in pairing mode',
                style: GoogleFonts.cairo(
                  fontSize: isTablet ? 14 : 12,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceTile(
    BuildContext context,
    BluetoothDevice device,
    BluetoothProvider bluetoothProvider,
    bool isTablet,
  ) {
    final theme = Theme.of(context);
    final isSelected =
        bluetoothProvider.selectedPrinter?.address == device.address;
    final isConnecting =
        bluetoothProvider.connectionState ==
        BluetoothConnectionState.connecting;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: FaIcon(
          FontAwesomeIcons.print,
          color: isSelected ? theme.colorScheme.primary : Colors.grey,
          size: isTablet ? 22 : 20,
        ),
        title: Text(
          device.name,
          style: GoogleFonts.cairo(
            fontSize: isTablet ? 16 : 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          device.address,
          style: GoogleFonts.cairo(
            fontSize: isTablet ? 12 : 10,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        trailing:
            isConnecting && isSelected
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : isSelected
                ? Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.primary,
                  size: isTablet ? 24 : 20,
                )
                : Icon(
                  Icons.radio_button_unchecked,
                  color: Colors.grey,
                  size: isTablet ? 24 : 20,
                ),
        onTap:
            isConnecting
                ? null
                : () async {
                  await bluetoothProvider.connectToPrinter(device);
                  if (context.mounted) {
                    Navigator.of(context).pop();
                  }
                },
      ),
    );
  }
}
