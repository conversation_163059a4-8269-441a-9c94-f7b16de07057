import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:convert';

class StoreInfo {
  final String name;
  final String address;
  final String phone;
  final String email;
  final String website;
  final String taxId;
  final String description;
  final String logoPath;

  StoreInfo({
    required this.name,
    required this.address,
    required this.phone,
    required this.email,
    required this.website,
    required this.taxId,
    required this.description,
    required this.logoPath,
  });

  Map<String, dynamic> toJson() => {
    'name': name,
    'address': address,
    'phone': phone,
    'email': email,
    'website': website,
    'taxId': taxId,
    'description': description,
    'logoPath': logoPath,
  };

  factory StoreInfo.fromJson(Map<String, dynamic> json) => StoreInfo(
    name: json['name'] ?? '',
    address: json['address'] ?? '',
    phone: json['phone'] ?? '',
    email: json['email'] ?? '',
    website: json['website'] ?? '',
    taxId: json['taxId'] ?? '',
    description: json['description'] ?? '',
    logoPath: json['logoPath'] ?? '',
  );

  StoreInfo copyWith({
    String? name,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String? description,
    String? logoPath,
  }) {
    return StoreInfo(
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      taxId: taxId ?? this.taxId,
      description: description ?? this.description,
      logoPath: logoPath ?? this.logoPath,
    );
  }
}

class ReceiptSettings {
  final bool showLogo;
  final bool showStoreInfo;
  final bool showTaxInfo;
  final bool showFooterMessage;
  final String footerMessage;
  final double taxRate;
  final bool enableTax;
  final String receiptHeader;
  final String receiptFooter;
  final int paperWidth; // in mm
  final String fontFamily;
  final double fontSize;

  ReceiptSettings({
    required this.showLogo,
    required this.showStoreInfo,
    required this.showTaxInfo,
    required this.showFooterMessage,
    required this.footerMessage,
    required this.taxRate,
    required this.enableTax,
    required this.receiptHeader,
    required this.receiptFooter,
    required this.paperWidth,
    required this.fontFamily,
    required this.fontSize,
  });

  Map<String, dynamic> toJson() => {
    'showLogo': showLogo,
    'showStoreInfo': showStoreInfo,
    'showTaxInfo': showTaxInfo,
    'showFooterMessage': showFooterMessage,
    'footerMessage': footerMessage,
    'taxRate': taxRate,
    'enableTax': enableTax,
    'receiptHeader': receiptHeader,
    'receiptFooter': receiptFooter,
    'paperWidth': paperWidth,
    'fontFamily': fontFamily,
    'fontSize': fontSize,
  };

  factory ReceiptSettings.fromJson(Map<String, dynamic> json) => ReceiptSettings(
    showLogo: json['showLogo'] ?? true,
    showStoreInfo: json['showStoreInfo'] ?? true,
    showTaxInfo: json['showTaxInfo'] ?? true,
    showFooterMessage: json['showFooterMessage'] ?? true,
    footerMessage: json['footerMessage'] ?? 'Thank you for your business!',
    taxRate: json['taxRate']?.toDouble() ?? 0.0,
    enableTax: json['enableTax'] ?? false,
    receiptHeader: json['receiptHeader'] ?? '',
    receiptFooter: json['receiptFooter'] ?? '',
    paperWidth: json['paperWidth'] ?? 80,
    fontFamily: json['fontFamily'] ?? 'Arial',
    fontSize: json['fontSize']?.toDouble() ?? 12.0,
  );

  ReceiptSettings copyWith({
    bool? showLogo,
    bool? showStoreInfo,
    bool? showTaxInfo,
    bool? showFooterMessage,
    String? footerMessage,
    double? taxRate,
    bool? enableTax,
    String? receiptHeader,
    String? receiptFooter,
    int? paperWidth,
    String? fontFamily,
    double? fontSize,
  }) {
    return ReceiptSettings(
      showLogo: showLogo ?? this.showLogo,
      showStoreInfo: showStoreInfo ?? this.showStoreInfo,
      showTaxInfo: showTaxInfo ?? this.showTaxInfo,
      showFooterMessage: showFooterMessage ?? this.showFooterMessage,
      footerMessage: footerMessage ?? this.footerMessage,
      taxRate: taxRate ?? this.taxRate,
      enableTax: enableTax ?? this.enableTax,
      receiptHeader: receiptHeader ?? this.receiptHeader,
      receiptFooter: receiptFooter ?? this.receiptFooter,
      paperWidth: paperWidth ?? this.paperWidth,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
    );
  }
}

class StoreSettingsProvider extends ChangeNotifier {
  static const String _storeInfoKey = 'store_info';
  static const String _receiptSettingsKey = 'receipt_settings';

  StoreInfo _storeInfo = StoreInfo(
    name: 'My Store',
    address: '',
    phone: '',
    email: '',
    website: '',
    taxId: '',
    description: '',
    logoPath: '',
  );

  ReceiptSettings _receiptSettings = ReceiptSettings(
    showLogo: true,
    showStoreInfo: true,
    showTaxInfo: true,
    showFooterMessage: true,
    footerMessage: 'Thank you for your business!',
    taxRate: 0.0,
    enableTax: false,
    receiptHeader: '',
    receiptFooter: '',
    paperWidth: 80,
    fontFamily: 'Arial',
    fontSize: 12.0,
  );

  bool _isLoading = false;
  final ImagePicker _imagePicker = ImagePicker();

  // Getters
  StoreInfo get storeInfo => _storeInfo;
  ReceiptSettings get receiptSettings => _receiptSettings;
  bool get isLoading => _isLoading;
  bool get hasLogo => _storeInfo.logoPath.isNotEmpty;

  StoreSettingsProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      _isLoading = true;
      notifyListeners();

      final prefs = await SharedPreferences.getInstance();
      
      // Load store info
      final storeInfoJson = prefs.getString(_storeInfoKey);
      if (storeInfoJson != null) {
        final storeInfoMap = jsonDecode(storeInfoJson) as Map<String, dynamic>;
        _storeInfo = StoreInfo.fromJson(storeInfoMap);
      }

      // Load receipt settings
      final receiptSettingsJson = prefs.getString(_receiptSettingsKey);
      if (receiptSettingsJson != null) {
        final receiptSettingsMap = jsonDecode(receiptSettingsJson) as Map<String, dynamic>;
        _receiptSettings = ReceiptSettings.fromJson(receiptSettingsMap);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading store settings: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _saveStoreInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storeInfoJson = jsonEncode(_storeInfo.toJson());
      await prefs.setString(_storeInfoKey, storeInfoJson);
    } catch (e) {
      debugPrint('Error saving store info: $e');
    }
  }

  Future<void> _saveReceiptSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final receiptSettingsJson = jsonEncode(_receiptSettings.toJson());
      await prefs.setString(_receiptSettingsKey, receiptSettingsJson);
    } catch (e) {
      debugPrint('Error saving receipt settings: $e');
    }
  }

  // Store Info Methods
  Future<void> updateStoreInfo({
    String? name,
    String? address,
    String? phone,
    String? email,
    String? website,
    String? taxId,
    String? description,
  }) async {
    _storeInfo = _storeInfo.copyWith(
      name: name,
      address: address,
      phone: phone,
      email: email,
      website: website,
      taxId: taxId,
      description: description,
    );
    await _saveStoreInfo();
    notifyListeners();
  }

  Future<bool> updateStoreLogo({ImageSource source = ImageSource.gallery}) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image == null) return false;

      // Get app documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final logoDir = Directory('${appDir.path}/store_assets');
      if (!await logoDir.exists()) {
        await logoDir.create(recursive: true);
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final extension = image.path.split('.').last;
      final newPath = '${logoDir.path}/logo_$timestamp.$extension';

      // Copy image to app directory
      await File(image.path).copy(newPath);

      // Delete old logo if exists
      if (_storeInfo.logoPath.isNotEmpty) {
        final oldFile = File(_storeInfo.logoPath);
        if (await oldFile.exists()) {
          await oldFile.delete();
        }
      }

      // Update store info
      _storeInfo = _storeInfo.copyWith(logoPath: newPath);
      await _saveStoreInfo();
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('Error updating store logo: $e');
      return false;
    }
  }

  Future<void> removeStoreLogo() async {
    try {
      // Delete logo file if exists
      if (_storeInfo.logoPath.isNotEmpty) {
        final logoFile = File(_storeInfo.logoPath);
        if (await logoFile.exists()) {
          await logoFile.delete();
        }
      }

      // Update store info
      _storeInfo = _storeInfo.copyWith(logoPath: '');
      await _saveStoreInfo();
      notifyListeners();
    } catch (e) {
      debugPrint('Error removing store logo: $e');
    }
  }

  // Receipt Settings Methods
  Future<void> updateReceiptSettings({
    bool? showLogo,
    bool? showStoreInfo,
    bool? showTaxInfo,
    bool? showFooterMessage,
    String? footerMessage,
    double? taxRate,
    bool? enableTax,
    String? receiptHeader,
    String? receiptFooter,
    int? paperWidth,
    String? fontFamily,
    double? fontSize,
  }) async {
    _receiptSettings = _receiptSettings.copyWith(
      showLogo: showLogo,
      showStoreInfo: showStoreInfo,
      showTaxInfo: showTaxInfo,
      showFooterMessage: showFooterMessage,
      footerMessage: footerMessage,
      taxRate: taxRate,
      enableTax: enableTax,
      receiptHeader: receiptHeader,
      receiptFooter: receiptFooter,
      paperWidth: paperWidth,
      fontFamily: fontFamily,
      fontSize: fontSize,
    );
    await _saveReceiptSettings();
    notifyListeners();
  }

  // Validation methods
  bool isStoreInfoComplete() {
    return _storeInfo.name.isNotEmpty &&
           _storeInfo.address.isNotEmpty &&
           _storeInfo.phone.isNotEmpty;
  }

  List<String> getMissingStoreInfo() {
    final missing = <String>[];
    if (_storeInfo.name.isEmpty) missing.add('Store Name');
    if (_storeInfo.address.isEmpty) missing.add('Address');
    if (_storeInfo.phone.isEmpty) missing.add('Phone');
    return missing;
  }

  // Export/Import methods
  Map<String, dynamic> exportSettings() {
    return {
      'storeInfo': _storeInfo.toJson(),
      'receiptSettings': _receiptSettings.toJson(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  Future<bool> importSettings(Map<String, dynamic> settings) async {
    try {
      if (settings.containsKey('storeInfo')) {
        _storeInfo = StoreInfo.fromJson(settings['storeInfo']);
        await _saveStoreInfo();
      }

      if (settings.containsKey('receiptSettings')) {
        _receiptSettings = ReceiptSettings.fromJson(settings['receiptSettings']);
        await _saveReceiptSettings();
      }

      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Error importing settings: $e');
      return false;
    }
  }

  // Reset methods
  Future<void> resetStoreInfo() async {
    _storeInfo = StoreInfo(
      name: 'My Store',
      address: '',
      phone: '',
      email: '',
      website: '',
      taxId: '',
      description: '',
      logoPath: '',
    );
    await _saveStoreInfo();
    notifyListeners();
  }

  Future<void> resetReceiptSettings() async {
    _receiptSettings = ReceiptSettings(
      showLogo: true,
      showStoreInfo: true,
      showTaxInfo: true,
      showFooterMessage: true,
      footerMessage: 'Thank you for your business!',
      taxRate: 0.0,
      enableTax: false,
      receiptHeader: '',
      receiptFooter: '',
      paperWidth: 80,
      fontFamily: 'Arial',
      fontSize: 12.0,
    );
    await _saveReceiptSettings();
    notifyListeners();
  }
}
