# High-Quality Icons for POS Landing Page

This directory contains high-quality, colorful icons for the POS system landing page.

## Icon Requirements

### Technical Specifications:
- **Format**: PNG or SVG
- **Size**: Minimum 64x64 pixels (recommended 128x128 for crisp display)
- **Style**: Flat design, consistent visual weight
- **Colors**: Vibrant, professional colors that match the POS theme

### Required Icons:

#### Offline Features Section:
1. **backup-icon.png** - Local & Cloud Backup
   - Suggested: Cloud with arrow, database with sync symbol
   - Colors: Green/blue gradient

2. **barcode-icon.png** - Barcode Generation & Printing
   - Suggested: Barcode with printer or scanner
   - Colors: Blue/purple gradient

3. **thermal-printer-icon.png** - Thermal Receipt Printing
   - Suggested: Thermal printer with receipt
   - Colors: Purple/pink gradient

4. **cross-platform-icon.png** - Cross-Platform Sync
   - Suggested: Multiple devices (phone, tablet, desktop) with sync arrows
   - Colors: Orange/yellow gradient

5. **secure-storage-icon.png** - Secure Local Storage
   - Suggested: Database with lock/shield
   - Colors: Cyan/blue gradient

6. **real-time-icon.png** - Real-Time Processing
   - Suggested: Lightning bolt with gears or speedometer
   - Colors: Pink/red gradient

#### Stats Section:
7. **offline-icon.png** - Works Offline
   - Suggested: WiFi off symbol with checkmark
   - Colors: Green

8. **database-icon.png** - Local Storage
   - Suggested: Database cylinder
   - Colors: Blue

9. **cloud-backup-icon.png** - Cloud Backup
   - Suggested: Cloud with backup arrows
   - Colors: Purple

10. **security-icon.png** - Data Security
    - Suggested: Shield with lock
    - Colors: Orange

## Icon Sources

### Recommended Sources:
1. **Flaticon.com** - High-quality flat icons
2. **Icons8.com** - Professional icon sets
3. **Feather Icons** - Clean, minimal icons
4. **Heroicons** - Beautiful hand-crafted SVG icons

### Download Instructions:
1. Visit flaticon.com or similar icon website
2. Search for relevant keywords (e.g., "backup", "barcode", "printer")
3. Choose icons with consistent style and colors
4. Download in PNG format (128x128 or higher)
5. Rename according to the list above
6. Place in this directory

## Usage in Components

Icons are used in the Hero section:

```tsx
// Example usage
<img 
  src="/icons/backup-icon.png" 
  alt="Backup Icon" 
  className="w-8 h-8" 
/>
```

## Color Scheme

Match the green theme of the POS application:
- **Primary Green**: #4CAF50
- **Secondary Colors**: Blues, purples, oranges for variety
- **Style**: Modern, flat design with subtle gradients

## Attribution

If using icons from Flaticon or similar services, ensure proper attribution as required by the license.
