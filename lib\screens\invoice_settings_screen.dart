import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/invoice_settings_provider.dart';
import 'package:pos_app/models/invoice_settings.dart';

class InvoiceSettingsScreen extends StatefulWidget {
  const InvoiceSettingsScreen({super.key});

  @override
  _InvoiceSettingsScreenState createState() => _InvoiceSettingsScreenState();
}

class _InvoiceSettingsScreenState extends State<InvoiceSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Text controllers
  late TextEditingController _storeNameController;
  late TextEditingController _storeAddressController;
  late TextEditingController _phoneController;
  late TextEditingController _currencySymbolController;
  
  // RTL direction
  bool _rtlDirection = true;
  
  // Get RTL status for current widget tree
  bool get isRtl => _rtlDirection;
  
  @override
  void initState() {
    super.initState();
    
    // Initialize controllers with current settings
    final settings = Provider.of<InvoiceSettingsProvider>(context, listen: false).settings;
    
    _storeNameController = TextEditingController(text: settings.storeName);
    _storeAddressController = TextEditingController(text: settings.storeAddress);
    _phoneController = TextEditingController(text: settings.phoneNumber);
    _currencySymbolController = TextEditingController(text: settings.currencySymbol);
    _rtlDirection = settings.rtlDirection;
  }
  
  @override
  void dispose() {
    // Clean up controllers
    _storeNameController.dispose();
    _storeAddressController.dispose();
    _phoneController.dispose();
    _currencySymbolController.dispose();
    super.dispose();
  }
  
  // Save settings
  void _saveSettings() {
    if (_formKey.currentState!.validate()) {
      final provider = Provider.of<InvoiceSettingsProvider>(context, listen: false);
      
      // Create updated settings
      final updatedSettings = InvoiceSettings(
        storeName: _storeNameController.text,
        storeAddress: _storeAddressController.text,
        phoneNumber: _phoneController.text,
        currencySymbol: _currencySymbolController.text,
        rtlDirection: _rtlDirection,
      );
      
      // Update provider
      provider.updateInvoiceSettings(updatedSettings);
      
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حفظ الإعدادات بنجاح')),
      );
    }
  }
  
  // Build section header
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  // Build text field
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    IconData? icon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: icon != null ? Icon(icon) : null,
          border: const OutlineInputBorder(),
        ),
        keyboardType: keyboardType,
        validator: validator,
      ),
    );
  }
  
  // Build switch tile
  Widget _buildSwitchTile({
    required String title,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: SwitchListTile(
        title: Text(title),
        value: value,
        onChanged: onChanged,
        contentPadding: EdgeInsets.zero,
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الفاتورة'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Directionality(
          textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildSectionHeader(context, 'معلومات المتجر'),
                _buildTextField(
                  controller: _storeNameController,
                  label: 'اسم المتجر',
                  icon: Icons.store,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال اسم المتجر';
                    }
                    return null;
                  },
                ),
                _buildTextField(
                  controller: _storeAddressController,
                  label: 'عنوان المتجر',
                  icon: Icons.location_on,
                ),
                _buildTextField(
                  controller: _phoneController,
                  label: 'رقم الهاتف',
                  icon: Icons.phone,
                  keyboardType: TextInputType.phone,
                ),
                _buildTextField(
                  controller: _currencySymbolController,
                  label: 'رمز العملة',
                  icon: Icons.attach_money,
                ),
                _buildSectionHeader(context, 'إعدادات التنسيق'),
                _buildSwitchTile(
                  title: 'دعم النص من اليمين إلى اليسار (RTL)',
                  value: _rtlDirection,
                  onChanged: (value) {
                    setState(() {
                      _rtlDirection = value;
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
