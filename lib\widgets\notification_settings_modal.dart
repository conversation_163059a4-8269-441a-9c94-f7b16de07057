import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pos_app/providers/notification_provider.dart';

class NotificationSettingsModal extends StatelessWidget {
  final NotificationProvider provider;
  final bool isRTL;

  const NotificationSettingsModal({
    super.key,
    required this.provider,
    required this.isRTL,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                FaIcon(
                  FontAwesomeIcons.gear,
                  color: const Color(0xFF4CAF50),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    isRTL ? 'إعدادات الإشعارات' : 'Notification Settings',
                    style: GoogleFonts.cairo(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const FaIcon(FontAwesomeIcons.xmark),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Settings List
          Expanded(
            child: ListView(
              padding: const EdgeInsets.all(20),
              children: [
                _buildSettingSection(
                  isRTL ? 'إشعارات المخزون' : 'Stock Notifications',
                  FontAwesomeIcons.boxes,
                  [
                    _buildSwitchTile(
                      isRTL ? 'تنبيهات المخزون القليل' : 'Low Stock Alerts',
                      isRTL ? 'تلقي إشعارات عند انخفاض المخزون' : 'Receive alerts when stock is low',
                      provider.lowStockEnabled,
                      (value) => provider.setLowStockEnabled(value),
                    ),
                    _buildThresholdTile(
                      isRTL ? 'حد المخزون القليل' : 'Low Stock Threshold',
                      provider.lowStockThreshold,
                      (value) => provider.setLowStockThreshold(value),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildSettingSection(
                  isRTL ? 'إشعارات المبيعات' : 'Sales Notifications',
                  FontAwesomeIcons.cashRegister,
                  [
                    _buildSwitchTile(
                      isRTL ? 'إشعارات البيع' : 'Sale Notifications',
                      isRTL ? 'تلقي إشعارات عند إتمام البيع' : 'Receive notifications when sales are completed',
                      provider.saleNotificationsEnabled,
                      (value) => provider.setSaleNotificationsEnabled(value),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildSettingSection(
                  isRTL ? 'إشعارات النظام' : 'System Notifications',
                  FontAwesomeIcons.gear,
                  [
                    _buildSwitchTile(
                      isRTL ? 'تنبيهات النظام' : 'System Alerts',
                      isRTL ? 'تلقي تنبيهات النظام والتذكيرات' : 'Receive system alerts and reminders',
                      provider.systemAlertsEnabled,
                      (value) => provider.setSystemAlertsEnabled(value),
                    ),
                    _buildSwitchTile(
                      isRTL ? 'إشعارات النسخ الاحتياطي' : 'Backup Notifications',
                      isRTL ? 'تلقي إشعارات النسخ الاحتياطي' : 'Receive backup notifications',
                      provider.backupNotificationsEnabled,
                      (value) => provider.setBackupNotificationsEnabled(value),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                _buildSettingSection(
                  isRTL ? 'إعدادات الصوت' : 'Sound Settings',
                  FontAwesomeIcons.volumeHigh,
                  [
                    _buildSwitchTile(
                      isRTL ? 'تفعيل الصوت' : 'Enable Sound',
                      isRTL ? 'تشغيل صوت عند وصول الإشعارات' : 'Play sound when notifications arrive',
                      provider.soundEnabled,
                      (value) => provider.setSoundEnabled(value),
                    ),
                    _buildSwitchTile(
                      isRTL ? 'تفعيل الاهتزاز' : 'Enable Vibration',
                      isRTL ? 'اهتزاز الجهاز عند وصول الإشعارات' : 'Vibrate device when notifications arrive',
                      provider.vibrationEnabled,
                      (value) => provider.setVibrationEnabled(value),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            FaIcon(icon, color: const Color(0xFF4CAF50), size: 20),
            const SizedBox(width: 12),
            Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: SwitchListTile(
        title: Text(
          title,
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          subtitle,
          style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFF4CAF50),
      ),
    );
  }

  Widget _buildThresholdTile(
    String title,
    int value,
    ValueChanged<int> onChanged,
  ) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        title: Text(
          title,
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(
          isRTL ? 'القيمة الحالية: $value' : 'Current value: $value',
          style: GoogleFonts.cairo(fontSize: 12, color: Colors.grey.shade600),
        ),
        trailing: SizedBox(
          width: 100,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: value > 1 ? () => onChanged(value - 1) : null,
                icon: const FaIcon(FontAwesomeIcons.minus, size: 16),
              ),
              Text(
                value.toString(),
                style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
              ),
              IconButton(
                onPressed: value < 100 ? () => onChanged(value + 1) : null,
                icon: const FaIcon(FontAwesomeIcons.plus, size: 16),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
