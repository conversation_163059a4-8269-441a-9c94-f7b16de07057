import 'package:flutter/foundation.dart';
import 'package:pos_app/db/database_helper.dart';
import 'package:pos_app/models/supplier.dart';

class SupplierProvider with ChangeNotifier {
  List<Supplier> _suppliers = [];
  final DatabaseHelper dbHelper = DatabaseHelper.instance;
  bool _isLoaded = false;

  List<Supplier> get suppliers => _suppliers;

  Future<void> loadSuppliers() async {
    if (_isLoaded) return;
    
    if (kIsWeb) {
      // Use sample data for web
      _suppliers = [
        Supplier(
          id: 1,
          name: 'Algerian Foods Ltd',
          phone: '021123456',
          email: '<EMAIL>',
          address: 'Zone Industrielle, Algiers',
          balance: 12500.0,
          notes: 'Main supplier for local products',
        ),
        Supplier(
          id: 2,
          name: 'Tech Imports LLC',
          phone: '021987654',
          email: '<EMAIL>',
          address: 'Industrial Park, Oran',
          balance: 8750.0,
          notes: 'Electronics and appliances supplier',
        ),
        Supplier(
          id: 3,
          name: 'Global Distribution Co.',
          phone: '021456789',
          email: '<EMAIL>',
          address: 'Commercial Hub, Constantine',
          balance: 15200.0,
          notes: 'International imported goods',
        ),
      ];
    } else {
      try {
        final db = await dbHelper.database;
        final result = await db.query('suppliers');
        _suppliers = result.map((json) => Supplier.fromMap(json)).toList();
      } catch (e) {
        print('Error loading suppliers: $e');
      }
    }
    
    _isLoaded = true;
    notifyListeners();
  }

  Future<Supplier?> getSupplierById(int id) async {
    if (kIsWeb) {
      return _suppliers.firstWhere((supplier) => supplier.id == id);
    } else {
      try {
        final db = await dbHelper.database;
        final maps = await db.query(
          'suppliers',
          where: 'id = ?',
          whereArgs: [id],
        );
        
        if (maps.isNotEmpty) {
          return Supplier.fromMap(maps.first);
        }
      } catch (e) {
        print('Error getting supplier by ID: $e');
      }
    }
    return null;
  }

  Future<int> addSupplier(Supplier supplier) async {
    if (kIsWeb) {
      // Generate a new ID for web
      final newId = _suppliers.isEmpty ? 1 : (_suppliers.last.id ?? 0) + 1;
      final newSupplier = Supplier(
        id: newId,
        name: supplier.name,
        phone: supplier.phone,
        email: supplier.email,
        address: supplier.address,
        balance: supplier.balance,
        notes: supplier.notes,
      );
      
      _suppliers.add(newSupplier);
      notifyListeners();
      return newId;
    } else {
      try {
        final db = await dbHelper.database;
        final id = await db.insert('suppliers', supplier.toMap());
        final newSupplier = supplier.copyWith(id: id);
        _suppliers.add(newSupplier);
        notifyListeners();
        return id;
      } catch (e) {
        print('Error adding supplier: $e');
        return -1;
      }
    }
  }

  Future<bool> updateSupplier(Supplier supplier) async {
    if (kIsWeb) {
      final index = _suppliers.indexWhere((s) => s.id == supplier.id);
      if (index != -1) {
        _suppliers[index] = supplier;
        notifyListeners();
        return true;
      }
      return false;
    } else {
      try {
        final db = await dbHelper.database;
        final result = await db.update(
          'suppliers',
          supplier.toMap(),
          where: 'id = ?',
          whereArgs: [supplier.id],
        );
        
        if (result > 0) {
          final index = _suppliers.indexWhere((s) => s.id == supplier.id);
          if (index != -1) {
            _suppliers[index] = supplier;
            notifyListeners();
          }
          return true;
        }
        return false;
      } catch (e) {
        print('Error updating supplier: $e');
        return false;
      }
    }
  }

  Future<bool> deleteSupplier(int id) async {
    if (kIsWeb) {
      _suppliers.removeWhere((supplier) => supplier.id == id);
      notifyListeners();
      return true;
    } else {
      try {
        final db = await dbHelper.database;
        final result = await db.delete(
          'suppliers',
          where: 'id = ?',
          whereArgs: [id],
        );
        
        if (result > 0) {
          _suppliers.removeWhere((supplier) => supplier.id == id);
          notifyListeners();
          return true;
        }
        return false;
      } catch (e) {
        print('Error deleting supplier: $e');
        return false;
      }
    }
  }
}
