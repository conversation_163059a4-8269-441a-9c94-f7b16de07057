import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/utils/rtl_helper.dart';

class LanguageSettingsScreen extends StatefulWidget {
  const LanguageSettingsScreen({super.key});

  @override
  _LanguageSettingsScreenState createState() => _LanguageSettingsScreenState();
}

class _LanguageSettingsScreenState extends State<LanguageSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localeProvider.isRTL ? 'إعدادات اللغة' : 'Language Settings',
          style:
              localeProvider.isRTL
                  ? GoogleFonts.cairo(fontWeight: FontWeight.w600)
                  : GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        leading: Icon<PERSON><PERSON>on(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Directionality(
        textDirection: RTLHelper.getTextDirection(context),
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // شرح حول إعدادات اللغة
            Card(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          FontAwesomeIcons.circleInfo,
                          color: theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          localeProvider.isRTL
                              ? 'حول إعدادات اللغة'
                              : 'About Language Settings',
                          style: (localeProvider.isRTL
                                  ? GoogleFonts.cairo()
                                  : GoogleFonts.poppins())
                              .copyWith(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      localeProvider.isRTL
                          ? 'يمكنك تغيير لغة التطبيق واتجاه النص من هنا. سيتم تطبيق التغييرات على جميع أجزاء التطبيق.'
                          : 'You can change the application language and text direction here. Changes will be applied to all parts of the application.',
                      style:
                          (localeProvider.isRTL
                                  ? GoogleFonts.cairo()
                                  : GoogleFonts.poppins())
                              .copyWith(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // اختيار اللغة
            _buildSectionHeader(
              context,
              localeProvider.isRTL ? 'اختر اللغة' : 'Select Language',
            ),

            // خيار اللغة العربية
            _buildLanguageOption(
              context: context,
              title: 'العربية',
              subtitle:
                  localeProvider.isRTL ? 'اللغة العربية' : 'Arabic Language',
              locale: const Locale('ar', 'DZ'),
              isSelected: localeProvider.locale.languageCode == 'ar',
            ),

            // خيار اللغة الإنجليزية
            _buildLanguageOption(
              context: context,
              title: 'English',
              subtitle:
                  localeProvider.isRTL
                      ? 'اللغة الإنجليزية'
                      : 'English Language',
              locale: const Locale('en', 'US'),
              isSelected: localeProvider.locale.languageCode == 'en',
            ),

            const SizedBox(height: 24),

            // تبديل اتجاه النص
            _buildSectionHeader(
              context,
              localeProvider.isRTL ? 'اتجاه النص' : 'Text Direction',
            ),

            Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: SwitchListTile(
                title: Text(
                  localeProvider.isRTL
                      ? 'تمكين اتجاه RTL (من اليمين إلى اليسار)'
                      : 'Enable RTL (Right to Left)',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontFamily: localeProvider.isRTL ? 'Cairo' : 'Poppins',
                  ),
                ),
                subtitle: Text(
                  localeProvider.isRTL
                      ? 'يناسب اللغة العربية'
                      : 'Suitable for Arabic language',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontFamily: localeProvider.isRTL ? 'Cairo' : 'Poppins',
                  ),
                ),
                value: localeProvider.isRTL,
                onChanged: (value) {
                  if (value) {
                    localeProvider.setLocale(const Locale('ar', 'DZ'));
                  } else {
                    localeProvider.setLocale(const Locale('en', 'US'));
                  }
                },
                secondary: Icon(
                  localeProvider.isRTL
                      ? FontAwesomeIcons.alignRight
                      : FontAwesomeIcons.alignLeft,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // زر تبديل سريع للغة
            ElevatedButton.icon(
              onPressed: () {
                localeProvider.toggleDirection();
              },
              icon: const Icon(FontAwesomeIcons.rightLeft),
              label: Text(
                localeProvider.isRTL
                    ? 'تبديل إلى الإنجليزية'
                    : 'Switch to Arabic',
                style: TextStyle(
                  fontFamily: localeProvider.isRTL ? 'Cairo' : 'Poppins',
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.bold,
          fontFamily: localeProvider.isRTL ? 'Cairo' : 'Poppins',
        ),
      ),
    );
  }

  Widget _buildLanguageOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required Locale locale,
    required bool isSelected,
  }) {
    final theme = Theme.of(context);
    final localeProvider = Provider.of<LocaleProvider>(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: RadioListTile<String>(
        title: Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontFamily: title == 'العربية' ? 'Cairo' : 'Poppins',
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: theme.textTheme.bodySmall?.copyWith(
            fontFamily: localeProvider.isRTL ? 'Cairo' : 'Poppins',
          ),
        ),
        value: locale.languageCode,
        groupValue: localeProvider.locale.languageCode,
        onChanged: (value) {
          if (value != null) {
            if (value == 'ar') {
              localeProvider.setLocale(const Locale('ar', 'DZ'));
            } else {
              localeProvider.setLocale(const Locale('en', 'US'));
            }
          }
        },
        activeColor: theme.colorScheme.primary,
        selected: isSelected,
        secondary: CircleAvatar(
          backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
          child: Text(
            locale.languageCode.toUpperCase(),
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
              fontFamily: locale.languageCode == 'ar' ? 'Cairo' : 'Poppins',
            ),
          ),
        ),
      ),
    );
  }
}
