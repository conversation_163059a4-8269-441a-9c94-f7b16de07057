// نموذج مبسط لإعدادات الفاتورة (بدون اعتماد على قاعدة البيانات)
class InvoiceSettings {
  final String storeName;
  final String storeAddress;
  final String phoneNumber;
  final String currencySymbol;
  final bool rtlDirection;

  InvoiceSettings({
    this.storeName = 'المتجر',
    this.storeAddress = 'العنوان',
    this.phoneNumber = '0123456789',
    this.currencySymbol = 'دج',
    this.rtlDirection = true,
  });

  // نسخة مع تعديلات
  InvoiceSettings copyWith({
    String? storeName,
    String? storeAddress,
    String? phoneNumber,
    String? currencySymbol,
    bool? rtlDirection,
  }) {
    return InvoiceSettings(
      storeName: storeName ?? this.storeName,
      storeAddress: storeAddress ?? this.storeAddress,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      rtlDirection: rtlDirection ?? this.rtlDirection,
    );
  }

  // تحويل من خريطة (Map) الى كائن
  factory InvoiceSettings.fromMap(Map<String, dynamic> map) {
    return InvoiceSettings(
      storeName: map['storeName'] ?? 'المتجر',
      storeAddress: map['storeAddress'] ?? 'العنوان',
      phoneNumber: map['phoneNumber'] ?? '0123456789',
      currencySymbol: map['currencySymbol'] ?? 'دج',
      rtlDirection: map['rtlDirection'] ?? true,
    );
  }

  // تحويل من كائن الى خريطة (Map)
  Map<String, dynamic> toMap() {
    return {
      'storeName': storeName,
      'storeAddress': storeAddress,
      'phoneNumber': phoneNumber,
      'currencySymbol': currencySymbol,
      'rtlDirection': rtlDirection,
    };
  }
}
