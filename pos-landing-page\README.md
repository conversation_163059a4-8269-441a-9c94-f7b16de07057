# POS Pro Landing Page

A professional, modern landing page for the Flutter POS application built with cutting-edge web technologies.

## 🚀 Tech Stack

- **Vite** - Lightning fast build tool
- **TypeScript** - Type-safe development
- **React 18** - Modern React with hooks
- **shadcn/ui** - Beautiful, accessible UI components
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Smooth animations and transitions
- **Lucide React** - Beautiful, customizable icons

## ✨ Features

- **Responsive Design** - Perfect on all devices (mobile, tablet, desktop)
- **Modern UI/UX** - Clean, professional design with smooth animations
- **Performance Optimized** - Fast loading with optimized assets
- **Accessibility** - WCAG compliant with proper contrast and navigation
- **SEO Ready** - Optimized meta tags and semantic HTML
- **Interactive Elements** - Smooth scrolling, hover effects, and animations

## 🎨 Design Highlights

- **Green Color Scheme** (#4CAF50) matching the POS app theme
- **Phone Mockups** - Professional showcase of app features
- **Gradient Backgrounds** - Modern visual appeal
- **Animated Counters** - Engaging statistics display
- **Feature Showcase** - Alternating layout with detailed descriptions

## 📱 Showcased POS Features

### Core Features
- ✅ Advanced Cart Management with auto-save
- ✅ Partial Payment System with debt tracking
- ✅ Comprehensive Settings interface
- ✅ Multi-language Support (Arabic RTL, English, French, Spanish, German)
- ✅ Multi-currency System (DZD, USD, EUR, GBP, CAD)
- ✅ Bluetooth Thermal Printer Integration

### Business Features
- ✅ Real-time Dashboard and Analytics
- ✅ Inventory Management with low stock alerts
- ✅ Customer and Supplier Management
- ✅ Invoice Management with payment tracking
- ✅ Advanced Reporting and Analytics
- ✅ Dark/Light Theme Support

## 🛠️ Installation & Setup

1. **Clone the repository**
   ```bash
   cd pos-landing-page
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173`

## 📸 Screenshot Setup

To get the best visual presentation, capture real screenshots from your POS app:

1. **Run the screenshot capture tool**
   ```bash
   npm run screenshots
   ```

2. **Capture real screenshots**
   - Open your POS app at http://localhost:8080
   - Use browser dev tools (F12) and set mobile view (280x600px)
   - Navigate to different screens and capture screenshots
   - Save them in `public/screenshots/` with these exact names:
     - `dashboard.png` - Main dashboard
     - `cart.png` - Shopping cart interface
     - `payment.png` - Partial payment screen
     - `settings.png` - Settings interface
     - `inventory.png` - Inventory management
     - `reports.png` - Reports and analytics
     - `language.png` - Language selection

3. **Automatic fallback**
   If screenshots aren't available, the landing page will show placeholder images

## 📦 Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 🎯 Live Demo Integration

The landing page includes direct links to the live POS demo running at:
- **Demo URL**: http://localhost:8080
- **Live Demo Buttons**: Throughout the page for easy access

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## 🎨 Color Palette

- **Primary Green**: #4CAF50
- **Primary Variants**: #43A047, #388E3C, #2E7D32
- **Light Variants**: #E8F5E8, #C8E6C9, #A5D6A7
- **Accent Colors**: Various gradients for feature highlights

## 📁 Project Structure

```
pos-landing-page/
├── src/
│   ├── components/
│   │   ├── ui/           # shadcn/ui components
│   │   ├── Header.tsx    # Navigation header
│   │   ├── Hero.tsx      # Hero section
│   │   ├── Features.tsx  # Features grid
│   │   ├── FeatureShowcase.tsx  # Detailed feature showcase
│   │   ├── Technology.tsx       # Tech stack & stats
│   │   ├── Contact.tsx   # Contact section
│   │   └── Footer.tsx    # Footer
│   ├── lib/
│   │   └── utils.ts      # Utility functions
│   ├── App.tsx           # Main app component
│   ├── main.tsx          # Entry point
│   └── index.css         # Global styles
├── public/               # Static assets
└── package.json          # Dependencies
```

## 🔧 Customization

### Colors
Update the color scheme in `tailwind.config.js`:
```javascript
colors: {
  primary: {
    DEFAULT: "#4CAF50",
    // ... other variants
  }
}
```

### Content
Modify the content in respective component files:
- Hero content: `src/components/Hero.tsx`
- Features: `src/components/Features.tsx`
- Contact info: `src/components/Contact.tsx`

### Animations
Customize animations in `tailwind.config.js` and component files using Framer Motion.

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For questions or support, contact:
- Email: <EMAIL>
- Phone: +****************

---

Built with ❤️ using modern web technologies to showcase the powerful Flutter POS system.
