import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:async';

class SessionProvider extends ChangeNotifier {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _userEmailKey = 'user_email';
  static const String _rememberMeKey = 'remember_me';
  static const String _lastLoginKey = 'last_login';
  static const String _sessionTokenKey = 'session_token';
  static const String _encryptedCredentialsKey = 'encrypted_credentials';
  
  // Session timeout in minutes
  static const int _sessionTimeoutMinutes = 30;
  
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  bool _isLoggedIn = false;
  bool _isLoading = true;
  bool _rememberMe = false;
  String? _userId;
  String? _userName;
  String? _userEmail;
  String? _sessionToken;
  DateTime? _lastActivity;
  Timer? _sessionTimer;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  bool get rememberMe => _rememberMe;
  String? get userId => _userId;
  String? get userName => _userName;
  String? get userEmail => _userEmail;
  String? get sessionToken => _sessionToken;
  DateTime? get lastActivity => _lastActivity;

  SessionProvider() {
    _initializeSession();
  }

  Future<void> _initializeSession() async {
    try {
      await _loadSessionFromStorage();
      await _validateSession();
      _startSessionTimer();
    } catch (e) {
      debugPrint('Error initializing session: $e');
      await _clearSession();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadSessionFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      _rememberMe = prefs.getBool(_rememberMeKey) ?? false;
      _userId = prefs.getString(_userIdKey);
      _userName = prefs.getString(_userNameKey);
      _userEmail = prefs.getString(_userEmailKey);
      
      final lastLoginStr = prefs.getString(_lastLoginKey);
      if (lastLoginStr != null) {
        _lastActivity = DateTime.parse(lastLoginStr);
      }

      // Load session token from secure storage
      _sessionToken = await _secureStorage.read(key: _sessionTokenKey);
      
    } catch (e) {
      debugPrint('Error loading session from storage: $e');
      rethrow;
    }
  }

  Future<void> _validateSession() async {
    if (!_isLoggedIn || _lastActivity == null) {
      return;
    }

    final now = DateTime.now();
    final timeDifference = now.difference(_lastActivity!);
    
    // Check if session has expired
    if (timeDifference.inMinutes > _sessionTimeoutMinutes) {
      debugPrint('Session expired');
      await _clearSession();
      return;
    }

    // Update last activity
    await _updateLastActivity();
  }

  Future<void> _updateLastActivity() async {
    try {
      _lastActivity = DateTime.now();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastLoginKey, _lastActivity!.toIso8601String());
    } catch (e) {
      debugPrint('Error updating last activity: $e');
    }
  }

  void _startSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkSessionTimeout();
    });
  }

  void _checkSessionTimeout() {
    if (!_isLoggedIn || _lastActivity == null) {
      return;
    }

    final now = DateTime.now();
    final timeDifference = now.difference(_lastActivity!);
    
    if (timeDifference.inMinutes > _sessionTimeoutMinutes) {
      _handleSessionTimeout();
    }
  }

  void _handleSessionTimeout() {
    debugPrint('Session timed out');
    logout();
    // You could show a dialog here to inform the user
  }

  Future<bool> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // For demo purposes, accept any email/password combination
      // In a real app, you would validate against your backend
      if (email.isNotEmpty && password.isNotEmpty) {
        _isLoggedIn = true;
        _rememberMe = rememberMe;
        _userId = _generateUserId(email);
        _userName = _extractNameFromEmail(email);
        _userEmail = email;
        _sessionToken = _generateSessionToken();
        _lastActivity = DateTime.now();

        await _saveSessionToStorage();
        
        if (rememberMe) {
          await _saveCredentials(email, password);
        }

        _startSessionTimer();
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('Login error: $e');
      return false;
    }
  }

  Future<void> _saveSessionToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setBool(_isLoggedInKey, _isLoggedIn);
      await prefs.setBool(_rememberMeKey, _rememberMe);
      await prefs.setString(_userIdKey, _userId ?? '');
      await prefs.setString(_userNameKey, _userName ?? '');
      await prefs.setString(_userEmailKey, _userEmail ?? '');
      await prefs.setString(_lastLoginKey, _lastActivity?.toIso8601String() ?? '');

      // Save session token to secure storage
      if (_sessionToken != null) {
        await _secureStorage.write(key: _sessionTokenKey, value: _sessionToken!);
      }
    } catch (e) {
      debugPrint('Error saving session to storage: $e');
      rethrow;
    }
  }

  Future<void> _saveCredentials(String email, String password) async {
    try {
      // Encrypt credentials before storing
      final credentials = {'email': email, 'password': password};
      final credentialsJson = jsonEncode(credentials);
      final encryptedCredentials = _encryptString(credentialsJson);
      
      await _secureStorage.write(
        key: _encryptedCredentialsKey,
        value: encryptedCredentials,
      );
    } catch (e) {
      debugPrint('Error saving credentials: $e');
    }
  }

  Future<Map<String, String>?> getSavedCredentials() async {
    try {
      final encryptedCredentials = await _secureStorage.read(
        key: _encryptedCredentialsKey,
      );
      
      if (encryptedCredentials != null) {
        final decryptedJson = _decryptString(encryptedCredentials);
        final credentials = jsonDecode(decryptedJson) as Map<String, dynamic>;
        return {
          'email': credentials['email'] as String,
          'password': credentials['password'] as String,
        };
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting saved credentials: $e');
      return null;
    }
  }

  Future<void> logout() async {
    try {
      await _clearSession();
      _sessionTimer?.cancel();
      notifyListeners();
    } catch (e) {
      debugPrint('Logout error: $e');
    }
  }

  Future<void> _clearSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Clear regular preferences
      await prefs.remove(_isLoggedInKey);
      await prefs.remove(_userIdKey);
      await prefs.remove(_userNameKey);
      await prefs.remove(_userEmailKey);
      await prefs.remove(_lastLoginKey);
      
      // Clear secure storage
      await _secureStorage.delete(key: _sessionTokenKey);
      
      // Don't clear remember me and credentials if user wants to be remembered
      if (!_rememberMe) {
        await prefs.remove(_rememberMeKey);
        await _secureStorage.delete(key: _encryptedCredentialsKey);
      }

      // Reset state
      _isLoggedIn = false;
      _userId = null;
      _userName = null;
      _userEmail = null;
      _sessionToken = null;
      _lastActivity = null;
      
    } catch (e) {
      debugPrint('Error clearing session: $e');
    }
  }

  Future<void> refreshSession() async {
    if (_isLoggedIn) {
      await _updateLastActivity();
      _sessionToken = _generateSessionToken();
      await _saveSessionToStorage();
      notifyListeners();
    }
  }

  void updateActivity() {
    if (_isLoggedIn) {
      _updateLastActivity();
    }
  }

  String _generateUserId(String email) {
    final bytes = utf8.encode(email);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 16);
  }

  String _extractNameFromEmail(String email) {
    final parts = email.split('@');
    if (parts.isNotEmpty) {
      return parts[0].replaceAll('.', ' ').replaceAll('_', ' ');
    }
    return 'User';
  }

  String _generateSessionToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final bytes = utf8.encode('$_userEmail$timestamp');
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _encryptString(String text) {
    // Simple base64 encoding for demo purposes
    // In a real app, use proper encryption
    final bytes = utf8.encode(text);
    return base64.encode(bytes);
  }

  String _decryptString(String encryptedText) {
    // Simple base64 decoding for demo purposes
    // In a real app, use proper decryption
    final bytes = base64.decode(encryptedText);
    return utf8.decode(bytes);
  }

  @override
  void dispose() {
    _sessionTimer?.cancel();
    super.dispose();
  }
}
