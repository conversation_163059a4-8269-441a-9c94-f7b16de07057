<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Screenshot Placeholder Generator</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f3f4f6;
        }
        .phone-container {
            width: 280px;
            height: 600px;
            margin: 20px auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 500;
        }
        .dynamic-island {
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 28px;
            background: #000;
            border-radius: 14px;
            z-index: 10;
        }
        .content {
            height: calc(100% - 44px);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .logo {
            width: 32px;
            height: 32px;
            background: #4CAF50;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }
        .menu-btn {
            width: 24px;
            height: 24px;
            background: #e5e7eb;
            border-radius: 50%;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: #4CAF50;
        }
        .list-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .item-icon {
            width: 32px;
            height: 32px;
            background: #e8f5e8;
            border-radius: 6px;
            margin-right: 12px;
        }
        .item-content {
            flex: 1;
        }
        .item-title {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
        }
        .item-subtitle {
            font-size: 12px;
            color: #6b7280;
        }
        .item-action {
            width: 24px;
            height: 20px;
            background: #4CAF50;
            border-radius: 4px;
        }
        .bottom-action {
            margin-top: auto;
            background: #4CAF50;
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
        }
        .controls {
            text-align: center;
            margin: 20px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            margin: 5px;
            cursor: pointer;
            font-weight: 500;
        }
        button:hover {
            background: #45a049;
        }
        .screen-type {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h2>POS App Screenshot Generator</h2>
        <p>Generate placeholder screenshots for the landing page</p>
        <button onclick="generateDashboard()">Dashboard</button>
        <button onclick="generateCart()">Cart</button>
        <button onclick="generatePayment()">Payment</button>
        <button onclick="generateSettings()">Settings</button>
        <button onclick="downloadScreenshot()">Download Current</button>
    </div>

    <div class="phone-container" id="phone">
        <div class="dynamic-island"></div>
        <div class="status-bar">
            <span>9:41</span>
            <span>📶 📶 📶 🔋</span>
        </div>
        <div class="content" id="content">
            <!-- Content will be generated here -->
        </div>
        <div class="screen-type" id="screenType">Dashboard</div>
    </div>

    <script>
        function generateDashboard() {
            document.getElementById('screenType').textContent = 'Dashboard';
            document.getElementById('content').innerHTML = `
                <div class="header">
                    <div class="logo">P</div>
                    <div class="title">POS Pro</div>
                    <div class="menu-btn"></div>
                </div>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-label">Today Sales</div>
                        <div class="stat-value">$2,450</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Orders</div>
                        <div class="stat-value">127</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Customers</div>
                        <div class="stat-value">89</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">Products</div>
                        <div class="stat-value">456</div>
                    </div>
                </div>
                <div style="flex: 1;">
                    <div class="list-item">
                        <div class="item-icon"></div>
                        <div class="item-content">
                            <div class="item-title">Recent Transaction</div>
                            <div class="item-subtitle">Invoice #1234 - $45.99</div>
                        </div>
                        <div class="item-action"></div>
                    </div>
                    <div class="list-item">
                        <div class="item-icon"></div>
                        <div class="item-content">
                            <div class="item-title">Low Stock Alert</div>
                            <div class="item-subtitle">5 items need restocking</div>
                        </div>
                        <div class="item-action"></div>
                    </div>
                    <div class="list-item">
                        <div class="item-icon"></div>
                        <div class="item-content">
                            <div class="item-title">New Customer</div>
                            <div class="item-subtitle">John Doe registered</div>
                        </div>
                        <div class="item-action"></div>
                    </div>
                </div>
                <div class="bottom-action">Quick Sale</div>
            `;
        }

        function generateCart() {
            document.getElementById('screenType').textContent = 'Cart';
            document.getElementById('content').innerHTML = `
                <div class="header">
                    <div class="logo">🛒</div>
                    <div class="title">Shopping Cart</div>
                    <div style="background: #4CAF50; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">3 items</div>
                </div>
                <div style="flex: 1;">
                    <div class="list-item">
                        <div class="item-icon"></div>
                        <div class="item-content">
                            <div class="item-title">Coffee Beans</div>
                            <div class="item-subtitle">$12.99 × 2</div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button style="width: 24px; height: 24px; padding: 0; margin: 0; background: #e5e7eb; color: #374151;">-</button>
                            <span style="font-weight: 600;">2</span>
                            <button style="width: 24px; height: 24px; padding: 0; margin: 0;">+</button>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="item-icon"></div>
                        <div class="item-content">
                            <div class="item-title">Milk</div>
                            <div class="item-subtitle">$3.49 × 1</div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button style="width: 24px; height: 24px; padding: 0; margin: 0; background: #e5e7eb; color: #374151;">-</button>
                            <span style="font-weight: 600;">1</span>
                            <button style="width: 24px; height: 24px; padding: 0; margin: 0;">+</button>
                        </div>
                    </div>
                    <div class="list-item">
                        <div class="item-icon"></div>
                        <div class="item-content">
                            <div class="item-title">Sugar</div>
                            <div class="item-subtitle">$2.99 × 1</div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button style="width: 24px; height: 24px; padding: 0; margin: 0; background: #e5e7eb; color: #374151;">-</button>
                            <span style="font-weight: 600;">1</span>
                            <button style="width: 24px; height: 24px; padding: 0; margin: 0;">+</button>
                        </div>
                    </div>
                </div>
                <div style="background: #f9fafb; padding: 16px; border-radius: 12px; margin-bottom: 16px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Subtotal:</span>
                        <span>$29.47</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>Tax:</span>
                        <span>$2.36</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: 700; font-size: 18px; color: #4CAF50;">
                        <span>Total:</span>
                        <span>$31.83</span>
                    </div>
                </div>
                <div class="bottom-action">Proceed to Checkout</div>
            `;
        }

        function generatePayment() {
            document.getElementById('screenType').textContent = 'Payment';
            document.getElementById('content').innerHTML = `
                <div class="header">
                    <div class="logo">💳</div>
                    <div class="title">Payment Options</div>
                    <div class="menu-btn"></div>
                </div>
                <div style="background: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                        <span style="color: #6b7280;">Total Amount:</span>
                        <span style="font-weight: 600;">$150.00</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 12px;">
                        <span style="color: #6b7280;">Amount Paid:</span>
                        <span style="font-weight: 600; color: #4CAF50;">$100.00</span>
                    </div>
                    <div style="border-top: 1px solid #e5e7eb; padding-top: 12px;">
                        <div style="display: flex; justify-content: space-between;">
                            <span style="color: #f59e0b; font-weight: 600;">Remaining:</span>
                            <span style="color: #f59e0b; font-weight: 600;">$50.00</span>
                        </div>
                    </div>
                </div>
                <div style="flex: 1; display: flex; flex-direction: column; gap: 12px;">
                    <button style="width: 100%; background: #4CAF50; color: white; border: none; padding: 16px; border-radius: 12px; font-weight: 600; margin: 0;">
                        Process Partial Payment
                    </button>
                    <button style="width: 100%; background: white; color: #4CAF50; border: 2px solid #4CAF50; padding: 16px; border-radius: 12px; font-weight: 600; margin: 0;">
                        Full Payment
                    </button>
                    <button style="width: 100%; background: #f3f4f6; color: #6b7280; border: none; padding: 16px; border-radius: 12px; font-weight: 600; margin: 0;">
                        Save for Later
                    </button>
                </div>
            `;
        }

        function generateSettings() {
            document.getElementById('screenType').textContent = 'Settings';
            document.getElementById('content').innerHTML = `
                <div class="header">
                    <div class="logo">⚙️</div>
                    <div class="title">Settings</div>
                    <div class="menu-btn"></div>
                </div>
                <div style="flex: 1;">
                    <div class="list-item">
                        <div style="width: 32px; height: 32px; background: #dbeafe; border-radius: 6px; margin-right: 12px; display: flex; align-items: center; justify-content: center; font-size: 16px;">🌐</div>
                        <div class="item-content">
                            <div class="item-title">Language</div>
                            <div class="item-subtitle">English</div>
                        </div>
                        <div style="color: #9ca3af;">›</div>
                    </div>
                    <div class="list-item">
                        <div style="width: 32px; height: 32px; background: #dcfce7; border-radius: 6px; margin-right: 12px; display: flex; align-items: center; justify-content: center; font-size: 16px;">💰</div>
                        <div class="item-content">
                            <div class="item-title">Currency</div>
                            <div class="item-subtitle">USD ($)</div>
                        </div>
                        <div style="color: #9ca3af;">›</div>
                    </div>
                    <div class="list-item">
                        <div style="width: 32px; height: 32px; background: #fef3c7; border-radius: 6px; margin-right: 12px; display: flex; align-items: center; justify-content: center; font-size: 16px;">🖨️</div>
                        <div class="item-content">
                            <div class="item-title">Printer</div>
                            <div class="item-subtitle">Connected</div>
                        </div>
                        <div style="color: #9ca3af;">›</div>
                    </div>
                    <div class="list-item">
                        <div style="width: 32px; height: 32px; background: #e0e7ff; border-radius: 6px; margin-right: 12px; display: flex; align-items: center; justify-content: center; font-size: 16px;">🏪</div>
                        <div class="item-content">
                            <div class="item-title">Store Info</div>
                            <div class="item-subtitle">Business Setup</div>
                        </div>
                        <div style="color: #9ca3af;">›</div>
                    </div>
                    <div class="list-item">
                        <div style="width: 32px; height: 32px; background: #fce7f3; border-radius: 6px; margin-right: 12px; display: flex; align-items: center; justify-content: center; font-size: 16px;">🔒</div>
                        <div class="item-content">
                            <div class="item-title">Security</div>
                            <div class="item-subtitle">Enabled</div>
                        </div>
                        <div style="color: #9ca3af;">›</div>
                    </div>
                </div>
            `;
        }

        function downloadScreenshot() {
            const phone = document.getElementById('phone');
            const screenType = document.getElementById('screenType').textContent.toLowerCase();
            
            // This is a simplified version - in practice you'd use html2canvas or similar
            alert(`Screenshot would be saved as: ${screenType}.png\n\nTo actually capture:\n1. Right-click on the phone mockup\n2. Select "Save image as..."\n3. Save to public/screenshots/ folder`);
        }

        // Initialize with dashboard
        generateDashboard();
    </script>
</body>
</html>
