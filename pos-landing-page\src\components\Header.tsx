import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Menu, X, ShoppingCart, Star, Settings, Phone, Zap } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { scrollToSection } from '@/lib/utils'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const navItems = [
    { label: 'Features', href: 'features', icon: Star },
    { label: 'Capabilities', href: 'capabilities', icon: Zap },
    { label: 'Technology', href: 'technology', icon: Settings },
    { label: 'Contact', href: 'contact', icon: Phone },
  ]

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? 'bg-white/98 backdrop-blur-lg shadow-xl border-b border-green-100'
          : 'bg-white/10 backdrop-blur-sm'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="flex items-center space-x-4"
          >
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <ShoppingCart className="w-7 h-7 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center">
                <span className="text-xs text-white font-bold">✓</span>
              </div>
            </div>
            <div>
              <h1 className={`text-2xl font-bold transition-colors ${
                isScrolled ? 'text-gray-900' : 'text-white'
              }`}>POS Pro</h1>
              <p className={`text-sm font-medium transition-colors ${
                isScrolled ? 'text-green-600' : 'text-green-100'
              } hidden sm:block`}>Professional Point of Sale</p>
            </div>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            {navItems.map((item, index) => (
              <motion.button
                key={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 + index * 0.1 }}
                onClick={() => scrollToSection(item.href)}
                className={`flex items-center px-4 py-3 rounded-xl font-semibold transition-all duration-300 relative group ${
                  isScrolled
                    ? 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                    : 'text-white hover:text-green-100 hover:bg-white/10'
                }`}
              >
                <item.icon className="w-4 h-4 mr-2" />
                {item.label}
                <span className={`absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 transition-all duration-300 group-hover:w-3/4 rounded-full ${
                  isScrolled ? 'bg-green-500' : 'bg-white'
                }`} />
              </motion.button>
            ))}
          </nav>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="hidden lg:flex items-center space-x-3"
          >
            <Button
              variant="outline"
              onClick={() => window.open('http://localhost:8080', '_blank')}
              className={`border-2 transition-all duration-300 ${
                isScrolled
                  ? 'border-green-500 text-green-600 hover:bg-green-50 hover:border-green-600'
                  : 'border-white text-white hover:bg-white/10 hover:border-green-200'
              }`}
            >
              🚀 Live Demo
            </Button>
            <Button
              onClick={() => scrollToSection('contact')}
              className="bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              ✨ Get Started
            </Button>
          </motion.div>

          {/* Mobile Menu Button */}
          <button
            className={`lg:hidden p-3 rounded-xl transition-all duration-300 ${
              isScrolled
                ? 'text-gray-700 hover:text-green-600 hover:bg-green-50'
                : 'text-white hover:text-green-100 hover:bg-white/10'
            }`}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="lg:hidden border-t border-green-100 bg-white/98 backdrop-blur-lg shadow-xl"
          >
            <div className="py-6 space-y-2">
              {navItems.map((item) => (
                <button
                  key={item.href}
                  onClick={() => {
                    scrollToSection(item.href)
                    setIsMenuOpen(false)
                  }}
                  className="flex items-center w-full text-left px-6 py-4 text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-200 rounded-xl mx-2 font-semibold"
                >
                  <item.icon className="w-5 h-5 mr-3 text-green-500" />
                  {item.label}
                </button>
              ))}
              <div className="px-6 pt-6 space-y-4">
                <Button
                  variant="outline"
                  className="w-full border-2 border-green-500 text-green-600 hover:bg-green-50 hover:border-green-600 py-4 text-lg font-bold rounded-xl"
                  onClick={() => {
                    window.open('http://localhost:8080', '_blank')
                    setIsMenuOpen(false)
                  }}
                >
                  🚀 Live Demo
                </Button>
                <Button
                  className="w-full bg-green-500 hover:bg-green-600 text-white shadow-lg py-4 text-lg font-bold rounded-xl"
                  onClick={() => {
                    scrollToSection('contact')
                    setIsMenuOpen(false)
                  }}
                >
                  ✨ Get Started
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.header>
  )
}

export default Header
