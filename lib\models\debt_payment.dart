class DebtPayment {
  final int? id;
  final int customerId;
  final double amount;
  final DateTime paymentDate;
  final String paymentMethod;
  final String? notes;
  final List<int> invoiceIds; // IDs of invoices this payment applies to
  final DateTime createdAt;

  DebtPayment({
    this.id,
    required this.customerId,
    required this.amount,
    required this.paymentDate,
    required this.paymentMethod,
    this.notes,
    required this.invoiceIds,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'customerId': customerId,
    'amount': amount,
    'paymentDate': paymentDate.toIso8601String(),
    'paymentMethod': paymentMethod,
    'notes': notes,
    'invoiceIds': invoiceIds.join(','),
    'createdAt': createdAt.toIso8601String(),
  };

  factory DebtPayment.fromJson(Map<String, dynamic> json) => DebtPayment(
    id: json['id'],
    customerId: json['customerId'],
    amount: json['amount'].toDouble(),
    paymentDate: DateTime.parse(json['paymentDate']),
    paymentMethod: json['paymentMethod'],
    notes: json['notes'],
    invoiceIds: json['invoiceIds'] != null 
        ? json['invoiceIds'].split(',').map<int>((id) => int.parse(id)).toList()
        : [],
    createdAt: DateTime.parse(json['createdAt']),
  );

  DebtPayment copyWith({
    int? id,
    int? customerId,
    double? amount,
    DateTime? paymentDate,
    String? paymentMethod,
    String? notes,
    List<int>? invoiceIds,
    DateTime? createdAt,
  }) {
    return DebtPayment(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      amount: amount ?? this.amount,
      paymentDate: paymentDate ?? this.paymentDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      invoiceIds: invoiceIds ?? this.invoiceIds,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
