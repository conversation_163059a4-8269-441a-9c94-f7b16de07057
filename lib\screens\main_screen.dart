import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/screens/pos_screen.dart';
import 'package:pos_app/screens/products_screen_enhanced.dart';
import 'package:pos_app/screens/customers_screen.dart';
import 'package:pos_app/screens/suppliers_screen.dart';
import 'package:pos_app/screens/enhanced_categories_screen.dart';
import 'package:pos_app/screens/expenses_screen.dart';
import 'package:pos_app/screens/invoices_screen.dart';
import 'package:pos_app/screens/advanced_reports_screen.dart';
import 'package:pos_app/screens/settings_screen_responsive.dart';
import 'package:pos_app/screens/dashboard_screen.dart';
import 'package:pos_app/screens/profile_screen.dart';
import 'package:pos_app/screens/inventory_screen.dart';
import 'package:pos_app/screens/barcode_management_screen.dart';
import 'package:pos_app/screens/enhanced_backup_screen.dart';
import 'package:pos_app/widgets/side_menu.dart';

class BottomNavItem {
  final IconData icon;
  final String arLabel;
  final String enLabel;
  final int index;

  BottomNavItem({
    required this.icon,
    required this.arLabel,
    required this.enLabel,
    required this.index,
  });

  String getLabel(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? arLabel : enLabel;
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  _MainScreenState createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  static final List<Widget> _widgetOptions = [
    const DashboardScreen(), // 0
    const POSScreen(), // 1
    const ProductsScreenEnhanced(), // 2
    const CustomersScreen(), // 3
    const EnhancedCategoriesScreen(), // 4
    const InvoicesScreen(), // 5
    const SuppliersScreen(), // 6
    const AdvancedReportsScreen(), // 7
    const ExpensesScreen(), // 8
    const InventoryScreen(), // 9
    const ProfileScreen(), // 10
    const ResponsiveSettingsScreen(), // 11
    const BarcodeManagementScreen(), // 12
    const EnhancedBackupScreen(), // 13
  ];

  static final Map<String, List<String>> _screenTitles = {
    'ar': [
      'لوحة التحكم', // 0
      'نقطة البيع', // 1
      'المنتجات', // 2
      'العملاء', // 3
      'الفئات', // 4
      'الفواتير', // 5
      'الموردين', // 6
      'التقارير', // 7
      'المصروفات', // 8
      'المخزون', // 9
      'الملف الشخصي', // 10
      'الإعدادات', // 11
      'إدارة الباركود', // 12
      'النسخ الاحتياطي', // 13
    ],
    'en': [
      'Dashboard', // 0
      'Point of Sale', // 1
      'Products', // 2
      'Customers', // 3
      'Categories', // 4
      'Invoices', // 5
      'Suppliers', // 6
      'Reports', // 7
      'Expenses', // 8
      'Inventory', // 9
      'Profile', // 10
      'Settings', // 11
      'Barcode Management', // 12
      'Enhanced Backup', // 13
    ],
  };

  String _getScreenTitle(int index) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? _screenTitles['ar']![index] : _screenTitles['en']![index];
  }

  int _getBottomNavIndex() {
    for (int i = 0; i < _bottomNavItems.length; i++) {
      if (_bottomNavItems[i].index == _selectedIndex) {
        return i;
      }
    }
    return 0; // Default to first item if not found
  }

  // void _onItemTapped(int index) {
  //   setState(() {
  //     _selectedIndex = index;
  //     _scaffoldKey.currentState?.closeDrawer();
  //   });
  // } // Removed unused method

  // 4 أهم خيارات للتنقل السفلي
  final List<BottomNavItem> _bottomNavItems = <BottomNavItem>[
    BottomNavItem(
      icon: FontAwesomeIcons.gaugeHigh,
      arLabel: 'لوحة التحكم',
      enLabel: 'Dashboard',
      index: 0,
    ),
    BottomNavItem(
      icon: FontAwesomeIcons.cashRegister,
      arLabel: 'نقطة البيع',
      enLabel: 'POS',
      index: 1,
    ),
    BottomNavItem(
      icon: FontAwesomeIcons.boxesStacked,
      arLabel: 'المخزون',
      enLabel: 'Inventory',
      index: 9,
    ),
    BottomNavItem(
      icon: FontAwesomeIcons.userGear,
      arLabel: 'الملف الشخصي',
      enLabel: 'Profile',
      index: 10,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    // final theme = Theme.of(context);
    // final colorScheme = theme.colorScheme; // Removed unused variables

    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: Consumer<LocaleProvider>(
          builder:
              (context, localeProvider, _) => Text(
                _getScreenTitle(_selectedIndex),
                style:
                    localeProvider.isRTL
                        ? GoogleFonts.cairo(fontWeight: FontWeight.w600)
                        : GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () {
            _scaffoldKey.currentState?.openDrawer();
          },
        ),
        actions: _buildActions(),
      ),
      drawer: SideMenu(
        selectedIndex: _selectedIndex,
        onItemSelected: (index) {
          setState(() {
            _selectedIndex = index;
            _scaffoldKey.currentState?.closeDrawer();
          });
        },
      ),
      body: IndexedStack(index: _selectedIndex, children: _widgetOptions),
      bottomNavigationBar: NavigationBar(
        selectedIndex: _getBottomNavIndex(),
        onDestinationSelected: (index) {
          setState(() {
            _selectedIndex = _bottomNavItems[index].index;
          });
        },
        destinations:
            _bottomNavItems
                .map(
                  (item) => NavigationDestination(
                    icon: FaIcon(item.icon, size: 20),
                    label: item.getLabel(context),
                  ),
                )
                .toList(),
      ),
    );
  }

  List<Widget> _buildActions() {
    switch (_selectedIndex) {
      case 0: // POS Screen
        return [
          IconButton(
            icon: const FaIcon(FontAwesomeIcons.barcode, size: 20),
            tooltip: 'Scan Barcode',
            onPressed: () {
              // Implement barcode scanning
            },
          ),
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: 'Transaction History',
            onPressed: () {
              // Navigate to transaction history
            },
          ),
        ];
      case 1: // Products Screen
        return [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter Products',
            onPressed: () {
              // Show filter options
            },
          ),
        ];
      case 2: // Customers Screen
      case 3: // Suppliers Screen
        return [
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter',
            onPressed: () {
              // Show filter options
            },
          ),
        ];
      default:
        return [];
    }
  }
}
