import 'package:flutter/material.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:provider/provider.dart';

class LocalizationHelper {
  // عناوين التطبيق الرئيسية
  static String getAppTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'نظام نقاط البيع' : 'POS System';
  }

  // عناوين الشاشات
  static String getDashboardTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'لوحة التحكم' : 'Dashboard';
  }

  static String getPOSTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'نقطة البيع' : 'Point of Sale';
  }

  static String getReportsTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'التقارير' : 'Reports';
  }

  static String getProfileTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'الملف الشخصي' : 'Profile';
  }

  static String getProductsTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'المنتجات' : 'Products';
  }

  static String getCategoriesTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'الفئات' : 'Categories';
  }

  static String getSettingsTitle(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'الإعدادات' : 'Settings';
  }

  // عمليات POS
  static String getAllCategories(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'كل الفئات' : 'All Categories';
  }

  static String getCheckout(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'الدفع' : 'Checkout';
  }

  static String getCart(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'سلة المشتريات' : 'Cart';
  }

  static String getQuantity(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'الكمية' : 'Quantity';
  }

  static String getTotal(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'المجموع' : 'Total';
  }

  static String getPayment(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'الدفع' : 'Payment';
  }

  // أزرار عامة
  static String getSave(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'حفظ' : 'Save';
  }

  static String getCancel(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'إلغاء' : 'Cancel';
  }

  static String getDelete(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'حذف' : 'Delete';
  }

  static String getEdit(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'تعديل' : 'Edit';
  }

  static String getAdd(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? 'إضافة' : 'Add';
  }

  // تنسيق الأرقام والعملات
  static String formatCurrency(BuildContext context, double amount) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL
        ? '${amount.toStringAsFixed(2)} د.ج'
        : 'DZD ${amount.toStringAsFixed(2)}';
  }
}
