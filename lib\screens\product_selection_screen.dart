import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:ui' as ui;
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/models/product.dart';

class ProductSelectionScreen extends StatefulWidget {
  const ProductSelectionScreen({super.key});

  @override
  State<ProductSelectionScreen> createState() => _ProductSelectionScreenState();
}

class _ProductSelectionScreenState extends State<ProductSelectionScreen> {
  final _searchController = TextEditingController();
  bool _isSearching = false;
  bool _isGridView = true;
  String _selectedCategory = 'كل الفئات';

  @override
  void initState() {
    super.initState();
    // Load categories and products
    Future.microtask(() {
      Provider.of<CategoryProvider>(context, listen: false).loadCategories();
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildCategorySelector(BuildContext context) {
    final categoryProvider = Provider.of<CategoryProvider>(context);
    final localeProvider = Provider.of<LocaleProvider>(context);
    final categories = categoryProvider.categories;

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length + 1, // +1 for "All Categories"
        itemBuilder: (context, index) {
          if (index == 0) {
            // "All Categories" option
            final allCategoriesText = localeProvider.isRTL ? 'كل الفئات' : 'All Categories';
            return Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: ChoiceChip(
                label: Text(allCategoriesText),
                selected: _selectedCategory == 'كل الفئات',
                onSelected: (bool selected) {
                  setState(() {
                    _selectedCategory = selected ? 'كل الفئات' : _selectedCategory;
                  });
                },
              ),
            );
          }
          
          final category = categories[index - 1];
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: ChoiceChip(
              label: Text(category.name),
              selected: _selectedCategory == category.name,
              onSelected: (bool selected) {
                setState(() {
                  _selectedCategory = selected ? category.name : 'كل الفئات';
                });
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductCard(Product product, LocaleProvider localeProvider) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Provider.of<CartProvider>(context, listen: false).addToCart(product);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                localeProvider.isRTL 
                    ? 'تم إضافة ${product.name} إلى السلة'
                    : '${product.name} added to cart',
              ),
              duration: const Duration(seconds: 1),
              behavior: SnackBarBehavior.floating,
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image Placeholder
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: product.imageUrl != null && product.imageUrl!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            product.imageUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.shopping_bag_outlined,
                                size: 40,
                                color: Theme.of(context).primaryColor,
                              );
                            },
                          ),
                        )
                      : Icon(
                          Icons.shopping_bag_outlined,
                          size: 40,
                          color: Theme.of(context).primaryColor,
                        ),
                ),
              ),
              const SizedBox(height: 8),
              
              // Product Name
              Expanded(
                flex: 1,
                child: Text(
                  product.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textDirection: localeProvider.isRTL 
                      ? ui.TextDirection.rtl 
                      : ui.TextDirection.ltr,
                ),
              ),
              
              // Product Price
              Text(
                NumberFormat.currency(
                  symbol: localeProvider.isRTL ? 'د.ج ' : 'DZD ',
                  decimalDigits: 2,
                ).format(product.price),
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textDirection: localeProvider.isRTL 
                    ? ui.TextDirection.rtl 
                    : ui.TextDirection.ltr,
              ),
              
              // Stock Info
              if (product.stock <= 10)
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: product.stock == 0 ? Colors.red : Colors.orange,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    product.stock == 0 
                        ? (localeProvider.isRTL ? 'نفد المخزون' : 'Out of Stock')
                        : (localeProvider.isRTL ? 'مخزون قليل' : 'Low Stock'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductsView() {
    return Consumer3<ProductProvider, LocaleProvider, CartProvider>(
      builder: (context, productProvider, localeProvider, cartProvider, _) {
        final products = productProvider.getFilteredProducts(_selectedCategory);
        
        if (products.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory_2_outlined,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  localeProvider.isRTL ? 'لا توجد منتجات' : 'No products found',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          );
        }

        return _isGridView
            ? GridView.builder(
                padding: const EdgeInsets.all(16),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  return _buildProductCard(products[index], localeProvider);
                },
              )
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: Colors.grey.shade100,
                        child: Icon(
                          Icons.shopping_bag_outlined,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      title: Text(
                        product.name,
                        textDirection: localeProvider.isRTL 
                            ? ui.TextDirection.rtl 
                            : ui.TextDirection.ltr,
                      ),
                      subtitle: Text(
                        NumberFormat.currency(
                          symbol: localeProvider.isRTL ? 'د.ج ' : 'DZD ',
                          decimalDigits: 2,
                        ).format(product.price),
                        textDirection: localeProvider.isRTL 
                            ? ui.TextDirection.rtl 
                            : ui.TextDirection.ltr,
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.add_shopping_cart),
                        onPressed: () {
                          cartProvider.addToCart(product);
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                localeProvider.isRTL 
                                    ? 'تم إضافة ${product.name} إلى السلة'
                                    : '${product.name} added to cart',
                              ),
                              duration: const Duration(seconds: 1),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        },
                      ),
                      onTap: () {
                        cartProvider.addToCart(product);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              localeProvider.isRTL 
                                  ? 'تم إضافة ${product.name} إلى السلة'
                                  : '${product.name} added to cart',
                            ),
                            duration: const Duration(seconds: 1),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  );
                },
              );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleProvider>(
      builder: (context, localeProvider, _) {
        return Scaffold(
          appBar: AppBar(
            title: !_isSearching
                ? Text(
                    localeProvider.isRTL ? 'اختيار المنتجات' : 'Select Products',
                  )
                : TextField(
                    controller: _searchController,
                    textDirection: localeProvider.isRTL
                        ? ui.TextDirection.rtl
                        : ui.TextDirection.ltr,
                    decoration: InputDecoration(
                      hintText: localeProvider.isRTL
                          ? 'بحث عن منتج...'
                          : 'Search products...',
                      border: InputBorder.none,
                      hintStyle: const TextStyle(color: Colors.white70),
                    ),
                    style: const TextStyle(color: Colors.white),
                    onChanged: (value) {
                      Provider.of<ProductProvider>(context, listen: false)
                          .searchQuery = value;
                    },
                  ),
            actions: [
              IconButton(
                icon: Icon(_isSearching ? Icons.close : Icons.search),
                onPressed: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      Provider.of<ProductProvider>(context, listen: false)
                          .searchQuery = '';
                    }
                  });
                },
              ),
              IconButton(
                icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
                onPressed: () {
                  setState(() {
                    _isGridView = !_isGridView;
                  });
                },
              ),
            ],
          ),
          body: Column(
            children: [
              _buildCategorySelector(context),
              const Divider(height: 1),
              Expanded(child: _buildProductsView()),
            ],
          ),
          bottomNavigationBar: Consumer<CartProvider>(
            builder: (context, cartProvider, _) {
              if (cartProvider.itemCount == 0) return const SizedBox();
              
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            localeProvider.isRTL 
                                ? '${cartProvider.itemCount} عنصر في السلة'
                                : '${cartProvider.itemCount} items in cart',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            NumberFormat.currency(
                              symbol: localeProvider.isRTL ? 'د.ج ' : 'DZD ',
                              decimalDigits: 2,
                            ).format(cartProvider.totalAmount),
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(
                        localeProvider.isRTL ? 'العودة للسلة' : 'Back to Cart',
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
