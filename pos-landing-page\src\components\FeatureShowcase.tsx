import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import {
  ShoppingCart,
  CreditCard,
  Settings,
  Globe,
  Check,
  ArrowRight
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import PhoneMockup from '@/components/ui/phone-mockup'

interface FeatureShowcaseProps {
  title: string
  description: string
  features: string[]
  icon: React.ElementType
  imagePosition: 'left' | 'right'
  gradient: string
  screenshot: string
  screenshotAlt: string
}

const FeatureShowcaseItem: React.FC<FeatureShowcaseProps> = ({
  title,
  description,
  features,
  icon: Icon,
  imagePosition,
  gradient,
  screenshot,
  screenshotAlt,
}) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  })

  const contentVariants = {
    hidden: { opacity: 0, x: imagePosition === 'left' ? 50 : -50 },
    visible: { opacity: 1, x: 0 },
  }

  const imageVariants = {
    hidden: { opacity: 0, x: imagePosition === 'left' ? -50 : 50 },
    visible: { opacity: 1, x: 0 },
  }

  return (
    <div ref={ref} className="py-20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`grid lg:grid-cols-2 gap-12 lg:gap-20 items-center ${
          imagePosition === 'right' ? 'lg:grid-flow-col-dense' : ''
        }`}>
          {/* Content */}
          <motion.div
            variants={contentVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            transition={{ duration: 0.8 }}
            className={`${imagePosition === 'right' ? 'lg:col-start-1' : ''}`}
          >
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${gradient} mb-6`}>
              <Icon className="w-8 h-8 text-white" />
            </div>
            
            <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              {title}
            </h3>
            
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              {description}
            </p>
            
            <ul className="space-y-4 mb-8">
              {features.map((feature, index) => (
                <motion.li
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={inView ? { opacity: 1, x: 0 } : {}}
                  transition={{ delay: 0.2 + index * 0.1 }}
                  className="flex items-start"
                >
                  <div className="flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                    <Check className="w-4 h-4 text-primary-600" />
                  </div>
                  <span className="text-gray-700 leading-relaxed">{feature}</span>
                </motion.li>
              ))}
            </ul>
            
            <Button
              size="lg"
              onClick={() => window.open('http://localhost:8080', '_blank')}
              className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700"
            >
              Try This Feature
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </motion.div>

          {/* Phone Mockup */}
          <motion.div
            variants={imageVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            transition={{ duration: 0.8, delay: 0.2 }}
            className={`flex justify-center ${imagePosition === 'right' ? 'lg:col-start-2' : ''}`}
          >
            <PhoneMockup
              screenshot={screenshot}
              alt={screenshotAlt}
              variant="tilted"
              className="scale-90 lg:scale-100"
            />
          </motion.div>
        </div>
      </div>
    </div>
  )
}

const FeatureShowcase: React.FC = () => {
  const showcaseFeatures = [
    {
      title: 'Smart Cart Management',
      description: 'Experience the most advanced cart system with automatic saving, intelligent quantity controls, and seamless product management. Never lose your work again.',
      features: [
        'Auto-save functionality with persistent storage',
        'Smart quantity controls with validation',
        'Real-time price calculations and updates',
        'Beautiful empty cart state with guidance',
        'Smooth animations and transitions'
      ],
      icon: ShoppingCart,
      imagePosition: 'right' as const,
      gradient: 'from-blue-500 to-blue-600',
      screenshot: '/screenshots/cart.png',
      screenshotAlt: 'POS Pro Smart Cart - Advanced cart management with auto-save and quantity controls',
    },
    {
      title: 'Partial Payment System',
      description: 'Revolutionary payment processing that supports partial payments with automatic debt tracking. Perfect for businesses that need flexible payment options.',
      features: [
        'Partial payment support with debt tracking',
        'Automatic customer balance management',
        'Real-time payment status updates',
        'Comprehensive payment history',
        'Integration with customer profiles'
      ],
      icon: CreditCard,
      imagePosition: 'left' as const,
      gradient: 'from-green-500 to-green-600',
      screenshot: '/screenshots/payment.png',
      screenshotAlt: 'POS Pro Partial Payment - Revolutionary payment system with debt tracking',
    },
    {
      title: 'Professional Settings',
      description: 'Comprehensive settings interface that gives you complete control over your POS system. Configure everything from printer settings to business preferences.',
      features: [
        'Intuitive settings organization and navigation',
        'Advanced printer configuration and testing',
        'Multi-language and currency preferences',
        'Business information and branding setup',
        'System backup and security options'
      ],
      icon: Settings,
      imagePosition: 'right' as const,
      gradient: 'from-purple-500 to-purple-600',
      screenshot: '/screenshots/settings.png',
      screenshotAlt: 'POS Pro Settings - Comprehensive configuration interface with printer setup',
    },
    {
      title: 'Multi-Language Support',
      description: 'Seamlessly switch between 5 languages including full RTL support for Arabic. Perfect for international businesses and diverse customer bases.',
      features: [
        'Support for Arabic (RTL), English, French, Spanish, German',
        'Instant language switching without restart',
        'Proper RTL layout and text direction',
        'Localized number and currency formatting',
        'Persistent language preferences'
      ],
      icon: Globe,
      imagePosition: 'left' as const,
      gradient: 'from-orange-500 to-orange-600',
      screenshot: '/screenshots/language.png',
      screenshotAlt: 'POS Pro Multi-Language - Support for 5 languages including Arabic RTL',
    },
  ]

  return (
    <section id="capabilities" className="bg-white">
      {showcaseFeatures.map((feature, index) => (
        <div key={feature.title} className={index % 2 === 1 ? 'bg-gray-50' : 'bg-white'}>
          <FeatureShowcaseItem {...feature} />
        </div>
      ))}
    </section>
  )
}

export default FeatureShowcase
