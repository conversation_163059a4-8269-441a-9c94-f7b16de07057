<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="printerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E91E63;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="receiptGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F5F5F5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Printer Base -->
  <rect x="24" y="48" width="80" height="48" rx="8" fill="url(#printerGradient)"/>
  <rect x="28" y="52" width="72" height="40" rx="4" fill="#7B1FA2"/>
  
  <!-- Printer Top -->
  <rect x="32" y="32" width="64" height="24" rx="4" fill="#E1BEE7"/>
  <rect x="36" y="36" width="56" height="16" rx="2" fill="#CE93D8"/>
  
  <!-- Receipt Paper -->
  <rect x="44" y="16" width="40" height="40" rx="2" fill="url(#receiptGradient)" stroke="#E0E0E0" stroke-width="1"/>
  
  <!-- Receipt Content Lines -->
  <line x1="48" y1="24" x2="76" y2="24" stroke="#666" stroke-width="1"/>
  <line x1="48" y1="28" x2="80" y2="28" stroke="#666" stroke-width="1"/>
  <line x1="48" y1="32" x2="72" y2="32" stroke="#666" stroke-width="1"/>
  <line x1="48" y1="36" x2="78" y2="36" stroke="#666" stroke-width="1"/>
  <line x1="48" y1="40" x2="70" y2="40" stroke="#666" stroke-width="1"/>
  <line x1="48" y1="44" x2="76" y2="44" stroke="#666" stroke-width="1"/>
  
  <!-- Printer Controls -->
  <circle cx="88" cy="64" r="4" fill="#F8BBD9"/>
  <circle cx="88" cy="76" r="4" fill="#F8BBD9"/>
  <rect x="40" y="72" width="32" height="8" rx="4" fill="#AD1457"/>
  
  <!-- LED Indicator -->
  <circle cx="96" cy="56" r="2" fill="#4CAF50">
    <animate attributeName="opacity" values="1;0.3;1" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Paper Feed -->
  <path d="M64 56 L64 48" stroke="#9C27B0" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Arrow Marker -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#9C27B0"/>
    </marker>
  </defs>
  
  <!-- Bluetooth Symbol -->
  <path d="M16 80 L20 76 L16 72 L20 68 L16 64" stroke="#2196F3" stroke-width="2" fill="none"/>
  <path d="M16 72 L12 68" stroke="#2196F3" stroke-width="2"/>
  <path d="M16 72 L12 76" stroke="#2196F3" stroke-width="2"/>
</svg>
