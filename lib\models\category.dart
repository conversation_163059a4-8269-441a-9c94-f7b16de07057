class Category {
  final int? id;
  final String name;
  final String? description;
  final String? iconName;
  final int? productCount;

  Category({
    this.id,
    required this.name,
    this.description,
    this.iconName,
    this.productCount,
  });

  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      iconName: map['iconName'],
      productCount: map['productCount'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'iconName': iconName,
      // productCount is computed and not stored
    };
  }

  Category copyWith({
    int? id,
    String? name,
    String? description,
    String? iconName,
    int? productCount,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      productCount: productCount ?? this.productCount,
    );
  }
}
