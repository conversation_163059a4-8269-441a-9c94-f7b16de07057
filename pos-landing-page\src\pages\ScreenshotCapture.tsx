import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Camera, 
  Download, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle,
  Copy,
  Smartphone
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import ScreenshotManager from '@/components/ScreenshotManager'

const ScreenshotCapture: React.FC = () => {
  const [copiedText, setCopiedText] = useState<string | null>(null)

  const screenshots = [
    {
      name: 'Dashboard',
      filename: 'dashboard.png',
      description: 'Main dashboard with sales analytics and quick stats',
      url: 'http://localhost:8080',
      path: '#/dashboard',
      priority: 'high'
    },
    {
      name: 'Cart Management',
      filename: 'cart.png', 
      description: 'Smart cart with auto-save and quantity controls',
      url: 'http://localhost:8080',
      path: '#/pos',
      priority: 'high'
    },
    {
      name: 'Partial Payment',
      filename: 'payment.png',
      description: 'Revolutionary partial payment system',
      url: 'http://localhost:8080',
      path: '#/pos/checkout',
      priority: 'high'
    },
    {
      name: 'Settings',
      filename: 'settings.png',
      description: 'Professional settings interface',
      url: 'http://localhost:8080',
      path: '#/settings',
      priority: 'high'
    },
    {
      name: 'Inventory',
      filename: 'inventory.png',
      description: 'Inventory management with stock alerts',
      url: 'http://localhost:8080',
      path: '#/inventory',
      priority: 'medium'
    },
    {
      name: 'Reports',
      filename: 'reports.png',
      description: 'Advanced reporting and analytics',
      url: 'http://localhost:8080',
      path: '#/reports',
      priority: 'medium'
    },
    {
      name: 'Language Selection',
      filename: 'language.png',
      description: 'Multi-language support interface',
      url: 'http://localhost:8080',
      path: '#/settings/language',
      priority: 'low'
    }
  ]

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopiedText(text)
    setTimeout(() => setCopiedText(null), 2000)
  }

  const openPOSApp = (path?: string) => {
    const url = path ? `http://localhost:8080${path}` : 'http://localhost:8080'
    window.open(url, '_blank')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Screenshot Capture Tool</h1>
              <p className="text-gray-600 mt-1">Capture high-quality screenshots from the POS app for the landing page</p>
            </div>
            <Button
              onClick={() => openPOSApp()}
              className="bg-green-600 hover:bg-green-700"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Open POS App
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Instructions */}
        <Card className="mb-8 border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900 flex items-center">
              <Smartphone className="w-5 h-5 mr-2" />
              Quick Start Instructions
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-800">
            <ol className="list-decimal list-inside space-y-2">
              <li>Open the POS app in a new tab using the button above</li>
              <li>Press <kbd className="bg-blue-100 px-2 py-1 rounded">F12</kbd> to open Developer Tools</li>
              <li>Click the device toolbar icon (📱) or press <kbd className="bg-blue-100 px-2 py-1 rounded">Ctrl+Shift+M</kbd></li>
              <li>Set device to "iPhone 12 Pro" or custom dimensions: 280x600</li>
              <li>Navigate to each screen using the links below</li>
              <li>Right-click and select "Capture screenshot" or use browser extensions</li>
              <li>Save screenshots to <code className="bg-blue-100 px-1 rounded">public/screenshots/</code> folder</li>
            </ol>
          </CardContent>
        </Card>

        {/* Screenshot Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {screenshots.map((screenshot, index) => (
            <motion.div
              key={screenshot.filename}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{screenshot.name}</CardTitle>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      screenshot.priority === 'high' ? 'bg-red-100 text-red-700' :
                      screenshot.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {screenshot.priority}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{screenshot.description}</p>
                </CardHeader>
                <CardContent>
                  {/* Preview Area */}
                  <div className="bg-gray-100 rounded-lg p-4 mb-4 aspect-[9/16] flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <Camera className="w-8 h-8 mx-auto mb-2" />
                      <p className="text-sm">Screenshot Preview</p>
                      <p className="text-xs">{screenshot.filename}</p>
                    </div>
                  </div>

                  {/* File Info */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Filename:</span>
                      <div className="flex items-center">
                        <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                          {screenshot.filename}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyToClipboard(screenshot.filename)}
                          className="ml-1 h-6 w-6 p-0"
                        >
                          {copiedText === screenshot.filename ? (
                            <CheckCircle className="w-3 h-3 text-green-600" />
                          ) : (
                            <Copy className="w-3 h-3" />
                          )}
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Dimensions:</span>
                      <span className="font-mono text-xs">280x600px</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="space-y-2">
                    <Button
                      onClick={() => openPOSApp(screenshot.path)}
                      className="w-full"
                      variant="outline"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Open Screen
                    </Button>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(`${screenshot.url}${screenshot.path}`)}
                      >
                        {copiedText === `${screenshot.url}${screenshot.path}` ? (
                          <CheckCircle className="w-4 h-4 mr-1 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4 mr-1" />
                        )}
                        URL
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        disabled
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Save
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Advanced Tools */}
        <Card>
          <CardHeader>
            <CardTitle>Advanced Screenshot Management</CardTitle>
          </CardHeader>
          <CardContent>
            <ScreenshotManager />
          </CardContent>
        </Card>

        {/* Tips */}
        <Card className="mt-8 border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-900 flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              Pro Tips for Best Results
            </CardTitle>
          </CardHeader>
          <CardContent className="text-green-800">
            <ul className="list-disc list-inside space-y-1">
              <li>Use consistent lighting and theme across all screenshots</li>
              <li>Capture screens with realistic but clean sample data</li>
              <li>Ensure text is readable and UI elements are clearly visible</li>
              <li>Use the same device/viewport size for consistency</li>
              <li>Save files with exact names shown above for automatic integration</li>
              <li>Test the landing page after adding screenshots to verify they load correctly</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ScreenshotCapture
