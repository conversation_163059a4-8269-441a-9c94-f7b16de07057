import React from 'react'

interface FeatureIconProps {
  iconName: string
  alt: string
  className?: string
  fallbackEmoji?: string
}

const FeatureIcon: React.FC<FeatureIconProps> = ({
  iconName,
  alt,
  className = "w-8 h-8",
  fallbackEmoji = "🔧"
}) => {
  const [imageError, setImageError] = React.useState(false)
  const [svgError, setSvgError] = React.useState(false)
  const svgPath = `/icons/${iconName}.svg`
  const pngPath = `/icons/${iconName}.png`

  const handleSvgError = () => {
    setSvgError(true)
  }

  const handlePngError = () => {
    setImageError(true)
  }

  if (imageError && svgError) {
    return (
      <span className={`${className} flex items-center justify-center text-2xl`}>
        {fallbackEmoji}
      </span>
    )
  }

  if (svgError) {
    return (
      <img
        src={pngPath}
        alt={alt}
        className={className}
        onError={handlePngError}
        loading="lazy"
      />
    )
  }

  return (
    <img
      src={svgPath}
      alt={alt}
      className={className}
      onError={handleSvgError}
      loading="lazy"
    />
  )
}

export default FeatureIcon
