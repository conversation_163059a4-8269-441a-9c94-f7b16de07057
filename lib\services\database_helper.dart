import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    if (kIsWeb) {
      // For web, use a simple in-memory database
      return await openDatabase(
        inMemoryDatabasePath,
        version: 1,
        onCreate: _createTables,
      );
    } else {
      // For mobile platforms
      final documentsDirectory = await getApplicationDocumentsDirectory();
      final path = join(documentsDirectory.path, 'pos_database.db');
      return await openDatabase(
        path,
        version: 1,
        onCreate: _createTables,
      );
    }
  }

  Future<void> _createTables(Database db, int version) async {
    // Products table
    await db.execute('''
      CREATE TABLE products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        name_ar TEXT,
        name_en TEXT,
        name_fr TEXT,
        barcode TEXT UNIQUE,
        price REAL NOT NULL,
        cost REAL,
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 5,
        category_id INTEGER,
        description TEXT,
        image_path TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id)
      )
    ''');

    // Categories table
    await db.execute('''
      CREATE TABLE categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        name_ar TEXT,
        name_en TEXT,
        name_fr TEXT,
        description TEXT,
        color TEXT,
        icon TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Customers table
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        balance REAL DEFAULT 0.0,
        credit_limit REAL DEFAULT 0.0,
        is_active INTEGER DEFAULT 1,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Transactions table
    await db.execute('''
      CREATE TABLE transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transaction_number TEXT UNIQUE NOT NULL,
        customer_id INTEGER,
        total_amount REAL NOT NULL,
        paid_amount REAL DEFAULT 0.0,
        change_amount REAL DEFAULT 0.0,
        discount_amount REAL DEFAULT 0.0,
        tax_amount REAL DEFAULT 0.0,
        payment_method TEXT DEFAULT 'cash',
        status TEXT DEFAULT 'completed',
        notes TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id)
      )
    ''');

    // Transaction items table
    await db.execute('''
      CREATE TABLE transaction_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transaction_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price REAL NOT NULL,
        total_price REAL NOT NULL,
        discount_amount REAL DEFAULT 0.0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transaction_id) REFERENCES transactions (id),
        FOREIGN KEY (product_id) REFERENCES products (id)
      )
    ''');

    // Settings table
    await db.execute('''
      CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        category TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Store info table
    await db.execute('''
      CREATE TABLE store_info (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        store_name TEXT,
        store_name_ar TEXT,
        store_name_en TEXT,
        store_name_fr TEXT,
        address TEXT,
        phone TEXT,
        email TEXT,
        tax_id TEXT,
        website TEXT,
        logo_path TEXT,
        currency TEXT DEFAULT 'DZD',
        tax_rate REAL DEFAULT 0.0,
        business_hours TEXT,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Backup history table
    await db.execute('''
      CREATE TABLE backup_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        backup_name TEXT NOT NULL,
        backup_path TEXT,
        backup_size INTEGER,
        backup_type TEXT DEFAULT 'manual',
        status TEXT DEFAULT 'completed',
        created_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Insert default store info
    await db.insert('store_info', {
      'store_name': 'متجري',
      'store_name_ar': 'متجري',
      'store_name_en': 'My Store',
      'store_name_fr': 'Mon Magasin',
      'currency': 'DZD',
      'tax_rate': 19.0,
    });

    // Insert default settings
    final defaultSettings = [
      {'key': 'language', 'value': 'ar', 'category': 'general'},
      {'key': 'currency', 'value': 'DZD', 'category': 'general'},
      {'key': 'theme_mode', 'value': 'light', 'category': 'appearance'},
      {'key': 'backup_schedule', 'value': 'manual', 'category': 'backup'},
      {'key': 'low_stock_threshold', 'value': '5', 'category': 'inventory'},
      {'key': 'notifications_enabled', 'value': 'true', 'category': 'notifications'},
    ];

    for (final setting in defaultSettings) {
      await db.insert('settings', setting);
    }

    // Insert sample categories
    final sampleCategories = [
      {
        'name': 'مشروبات',
        'name_ar': 'مشروبات',
        'name_en': 'Beverages',
        'name_fr': 'Boissons',
        'color': '#2196F3',
        'icon': 'local_drink'
      },
      {
        'name': 'وجبات خفيفة',
        'name_ar': 'وجبات خفيفة',
        'name_en': 'Snacks',
        'name_fr': 'Collations',
        'color': '#FF9800',
        'icon': 'fastfood'
      },
    ];

    for (final category in sampleCategories) {
      await db.insert('categories', category);
    }
  }

  // Get all tables for backup
  Future<List<String>> getAllTables() async {
    final db = await database;
    final result = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
    );
    return result.map((row) => row['name'] as String).toList();
  }

  // Export database to JSON
  Future<Map<String, dynamic>> exportToJson() async {
    final db = await database;
    final tables = await getAllTables();
    final Map<String, dynamic> backup = {
      'version': 1,
      'timestamp': DateTime.now().toIso8601String(),
      'tables': {},
    };

    for (final table in tables) {
      final data = await db.query(table);
      backup['tables'][table] = data;
    }

    return backup;
  }

  // Import database from JSON
  Future<bool> importFromJson(Map<String, dynamic> backup) async {
    try {
      final db = await database;
      
      // Start transaction
      await db.transaction((txn) async {
        // Clear existing data
        final tables = await getAllTables();
        for (final table in tables) {
          await txn.delete(table);
        }

        // Import data
        final tablesData = backup['tables'] as Map<String, dynamic>;
        for (final tableName in tablesData.keys) {
          final tableData = tablesData[tableName] as List<dynamic>;
          for (final row in tableData) {
            await txn.insert(tableName, Map<String, dynamic>.from(row));
          }
        }
      });

      return true;
    } catch (e) {
      debugPrint('Error importing database: $e');
      return false;
    }
  }

  // Get database file path
  Future<String?> getDatabasePath() async {
    if (kIsWeb) return null;
    
    final documentsDirectory = await getApplicationDocumentsDirectory();
    return join(documentsDirectory.path, 'pos_database.db');
  }

  // Close database
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
