import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/models/transaction.dart' as model;
import 'package:pos_app/widgets/receipt_printer.dart';

class ReceiptScreen extends StatelessWidget {
  final model.Transaction transaction;

  const ReceiptScreen({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat.currency(locale: 'en_US', symbol: '\$');
    final dateFormatter = DateFormat('dd/MM/yyyy hh:mm a');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Receipt'),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text(
                    'RECEIPT',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
                  ),
                  const SizedBox(height: 8),
                  Text('Transaction ID: ${transaction.transactionId}'),
                  Text('Date: ${dateFormatter.format(transaction.date)}'),
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: transaction.items.length,
                    itemBuilder: (context, index) {
                      final item = transaction.items[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4.0),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 4,
                              child: Text(item.product.name),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text('${item.quantity}x'),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                formatter.format(item.price),
                                textAlign: TextAlign.right,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(
                                formatter.format(item.total),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 8),
                  const Divider(),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Total:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      Text(formatter.format(transaction.total),
                          style: const TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('Payment Method: ${transaction.paymentMethod}'),
                      Text('Amount Paid: ${formatter.format(transaction.amountPaid)}'),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Change:'),
                      Text(formatter.format(transaction.change)),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text('Thank you for your purchase!',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                  icon: const Icon(Icons.home),
                  label: const Text('Back to POS'),
                ),
                ElevatedButton.icon(
                  onPressed: () async {
                    await ReceiptPrinter.printReceipt(transaction);
                  },
                  icon: const Icon(Icons.print),
                  label: const Text('Print Receipt'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
