# POS App Screenshots

This folder contains screenshots from the live POS application for use in the landing page.

## Required Screenshots

1. **dashboard.png** - Main dashboard with sales analytics
2. **cart.png** - Smart cart management interface
3. **payment.png** - Partial payment system
4. **settings.png** - Professional settings interface
5. **inventory.png** - Inventory management screen
6. **reports.png** - Reports and analytics
7. **language.png** - Multi-language selection

## Screenshot Specifications

- **Format**: PNG with transparent background where possible
- **Dimensions**: 280x600 pixels (mobile portrait)
- **Quality**: High resolution for crisp display
- **Content**: Real app screens from http://localhost:8080

## How to Capture Screenshots

### Method 1: Browser Developer Tools
1. Open http://localhost:8080 in Chrome
2. Press F12 to open Developer Tools
3. Click the device toolbar icon (📱) or press Ctrl+Shift+M
4. Set device to "iPhone 12 Pro" or custom 280x600
5. Navigate to the desired screen in the POS app
6. Right-click and select "Capture screenshot"

### Method 2: Browser Extensions
1. Install a screenshot extension like "Full Page Screen Capture"
2. Navigate to the desired screen
3. Use the extension to capture the visible area
4. Crop to 280x600 pixels

### Method 3: Screenshot Tools
1. Use tools like Snagit, Lightshot, or built-in screenshot tools
2. Navigate to the desired screen
3. Capture the mobile view area
4. Resize to 280x600 pixels

## Screens to Capture

### Dashboard (dashboard.png)
- Main dashboard with sales statistics
- Today's sales, orders, customers
- Quick action buttons
- Recent transactions

### Cart Management (cart.png)
- Shopping cart with products
- Quantity controls (+/- buttons)
- Total amount calculation
- Checkout button

### Partial Payment (payment.png)
- Payment options screen
- Partial payment interface
- Amount fields (total, paid, remaining)
- Customer selection

### Settings (settings.png)
- Settings main screen or specific setting
- Language/currency options
- Printer configuration
- Business setup options

### Inventory (inventory.png)
- Product list or inventory management
- Stock levels
- Add/edit product interface
- Category management

### Reports (reports.png)
- Analytics dashboard
- Charts and graphs
- Sales reports
- Profit/loss statements

### Language Selection (language.png)
- Language selection screen
- List of supported languages
- Arabic, English, French, Spanish, German
- RTL/LTR indicators

## File Naming Convention

Use lowercase names with hyphens for consistency:
- `dashboard.png`
- `cart.png`
- `payment.png`
- `settings.png`
- `inventory.png`
- `reports.png`
- `language.png`

## Fallback Images

If real screenshots are not available, the landing page will show placeholder images with the feature names. To get the best presentation, capture real screenshots from the running POS application.

## Testing

After adding screenshots:
1. Refresh the landing page
2. Check that images load correctly
3. Verify they display properly in phone mockups
4. Test on different screen sizes

## Tips for Best Results

1. **Clean Interface**: Capture screens with realistic but clean data
2. **Good Lighting**: Ensure the app interface is clearly visible
3. **Consistent Style**: Use the same theme/mode for all screenshots
4. **Mobile View**: Always capture in mobile/responsive view
5. **High Quality**: Use high DPI settings for crisp images
