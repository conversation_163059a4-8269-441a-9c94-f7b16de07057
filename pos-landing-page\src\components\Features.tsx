import React from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { 
  ShoppingCart, 
  CreditCard, 
  Settings, 
  Globe, 
  DollarSign, 
  Printer,
  BarChart3,
  Package,
  Users,
  FileText,
  Moon,
  Smartphone
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const Features: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  const features = [
    {
      icon: ShoppingCart,
      title: 'Advanced Cart Management',
      description: 'Smart cart with auto-save functionality, quantity controls, and persistent storage across sessions.',
      color: 'from-blue-500 to-blue-600',
    },
    {
      icon: CreditCard,
      title: 'Partial Payment System',
      description: 'Revolutionary partial payment support with automatic debt tracking and customer balance management.',
      color: 'from-green-500 to-green-600',
    },
    {
      icon: Settings,
      title: 'Comprehensive Settings',
      description: 'Professional settings interface with printer configuration, business setup, and system preferences.',
      color: 'from-purple-500 to-purple-600',
    },
    {
      icon: Globe,
      title: 'Multi-Language Support',
      description: 'Full support for Arabic (RTL), English, French, Spanish, and German with seamless switching.',
      color: 'from-orange-500 to-orange-600',
    },
    {
      icon: DollarSign,
      title: 'Multi-Currency System',
      description: 'Support for DZD, USD, EUR, GBP, CAD with real-time currency formatting and conversion.',
      color: 'from-emerald-500 to-emerald-600',
    },
    {
      icon: Printer,
      title: 'Thermal Printer Integration',
      description: 'Bluetooth thermal printer support for 58mm and 80mm receipts with advanced configuration.',
      color: 'from-red-500 to-red-600',
    },
    {
      icon: BarChart3,
      title: 'Real-Time Analytics',
      description: 'Modern dashboard with live sales data, inventory tracking, and comprehensive reporting.',
      color: 'from-indigo-500 to-indigo-600',
    },
    {
      icon: Package,
      title: 'Inventory Management',
      description: 'Complete inventory control with low stock alerts, barcode scanning, and category management.',
      color: 'from-teal-500 to-teal-600',
    },
    {
      icon: Users,
      title: 'Customer & Supplier Management',
      description: 'Comprehensive contact management with debt tracking, purchase history, and detailed profiles.',
      color: 'from-pink-500 to-pink-600',
    },
    {
      icon: FileText,
      title: 'Invoice Management',
      description: 'Professional invoicing with multiple payment statuses, PDF generation, and WhatsApp sharing.',
      color: 'from-yellow-500 to-yellow-600',
    },
    {
      icon: Moon,
      title: 'Dark/Light Theme',
      description: 'Beautiful dark and light themes with automatic switching and user preference persistence.',
      color: 'from-gray-500 to-gray-600',
    },
    {
      icon: Smartphone,
      title: 'Responsive Design',
      description: 'Perfect experience across all devices - mobile phones, tablets, and desktop computers.',
      color: 'from-cyan-500 to-cyan-600',
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
            Powerful Features for
            <span className="bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent"> Modern Business</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our comprehensive POS system includes everything you need to run your business efficiently, 
            from advanced payment processing to detailed analytics and reporting.
          </p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          {features.map((feature) => (
            <motion.div
              key={feature.title}
              variants={itemVariants}
              className="group"
            >
              <Card className="h-full feature-card border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Business?</h3>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Join thousands of businesses already using our POS system to streamline operations and boost sales.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => window.open('http://localhost:8080', '_blank')}
                className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
              >
                Try Live Demo
              </button>
              <button className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                Contact Sales
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Features
