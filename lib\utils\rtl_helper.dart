import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

class RTLHelper {
  /// تحديد اتجاه النص بناءً على اللغة الحالية
  static TextDirection getTextDirection(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? TextDirection.rtl : TextDirection.ltr;
  }

  /// تحديد تنسيق اتجاه الصفحة بناءً على اللغة الحالية
  static TextDirection getPageDirection(BuildContext context) {
    return getTextDirection(context);
  }

  /// تطبيق الـ padding بشكل متناسق مع اتجاه النص
  static EdgeInsetsDirectional getDirectionalPadding({
    required BuildContext context,
    double start = 0.0,
    double end = 0.0,
    double top = 0.0,
    double bottom = 0.0,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  /// الحصول على الهامش المتناسب مع اتجاه RTL
  static EdgeInsetsDirectional getRTLAwarePadding(BuildContext context, EdgeInsets padding) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL
        ? EdgeInsetsDirectional.fromSTEB(
            padding.right, padding.top, padding.left, padding.bottom)
        : EdgeInsetsDirectional.fromSTEB(
            padding.left, padding.top, padding.right, padding.bottom);
  }

  /// محاذاة النص بناءً على اتجاه اللغة
  static TextAlign getTextAlignment(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? TextAlign.right : TextAlign.left;
  }

  /// تطبيق التنسيق المناسب للقوائم المنسدلة
  static TextDirection getDropdownDirection(BuildContext context) {
    return getTextDirection(context);
  }

  /// عكس القيم في حالة RTL
  static double getRTLAwareValue(BuildContext context, double ltrValue, double rtlValue) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? rtlValue : ltrValue;
  }

  /// لمعرفة ما إذا كان النص يجب أن يكون بالعربية
  static bool isArabic(BuildContext context) {
    return Provider.of<LocaleProvider>(context, listen: false).isRTL;
  }

  /// للحصول على اتجاه للأطفال حسب اللغة
  static MainAxisAlignment getMainAxisAlignment(BuildContext context, {bool reverse = false}) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    if (reverse) {
      return isRTL ? MainAxisAlignment.start : MainAxisAlignment.end;
    }
    return isRTL ? MainAxisAlignment.end : MainAxisAlignment.start;
  }

  /// الحصول على محاذاة مناسبة لعناصر الشاشة
  static CrossAxisAlignment getCrossAxisAlignment(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    return isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start;
  }
}
