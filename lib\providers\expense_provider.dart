import 'package:flutter/foundation.dart';
import 'package:pos_app/models/expense.dart';
import 'package:pos_app/db/database_helper.dart';

class ExpenseProvider with ChangeNotifier {
  List<Expense> _expenses = [];
  bool _isLoading = false;

  ExpenseProvider() {
    // Load expenses when provider is created
    loadExpenses();
  }

  List<Expense> get expenses => [..._expenses];
  bool get isLoading => _isLoading;

  Future<void> loadExpenses() async {
    _isLoading = true;
    notifyListeners();

    try {
      if (kIsWeb) {
        // For web, use sample expenses since SQLite is not fully supported
        _expenses = [
          Expense(
            id: 1,
            title: 'إيجار المحل',
            amount: 25000,
            category: 'إيجار',
            date: DateTime.now().subtract(const Duration(days: 5)),
            description: 'إيجار شهر مايو',
          ),
          Expense(
            id: 2,
            title: 'فواتير الكهرباء',
            amount: 3500,
            category: 'مرافق',
            date: DateTime.now().subtract(const Duration(days: 3)),
            description: 'فاتورة الكهرباء لشهر مايو',
          ),
          Expense(
            id: 3,
            title: 'مصاريف الصيانة',
            amount: 1200,
            category: 'صيانة',
            date: DateTime.now().subtract(const Duration(days: 1)),
            description: 'إصلاح مكيف الهواء',
          ),
        ];
      } else {
        // For mobile, use SQLite database
        _expenses = await DatabaseHelper.instance.getAllExpenses();
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('Error loading expenses: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addExpense(Expense expense) async {
    try {
      if (kIsWeb) {
        // For web, just add to the in-memory list
        final id = _expenses.isNotEmpty ? _expenses.last.id! + 1 : 1;
        final newExpense = expense.copyWith(id: id);
        _expenses.add(newExpense);
      } else {
        // For mobile, use SQLite database
        final id = await DatabaseHelper.instance.insertExpense(expense);
        final newExpense = expense.copyWith(id: id);
        _expenses.add(newExpense);
      }
      notifyListeners();
    } catch (e) {
      print('Error adding expense: $e');
    }
  }

  Future<void> updateExpense(Expense expense) async {
    try {
      if (!kIsWeb) {
        // For mobile, use SQLite database
        await DatabaseHelper.instance.updateExpense(expense);
      }
      
      // Update in-memory list for both web and mobile
      final index = _expenses.indexWhere((e) => e.id == expense.id);
      if (index >= 0) {
        _expenses[index] = expense;
        notifyListeners();
      }
    } catch (e) {
      print('Error updating expense: $e');
    }
  }

  Future<void> deleteExpense(int id) async {
    try {
      if (!kIsWeb) {
        // For mobile, use SQLite database
        await DatabaseHelper.instance.deleteExpense(id);
      }
      
      // Update in-memory list for both web and mobile
      _expenses.removeWhere((expense) => expense.id == id);
      notifyListeners();
    } catch (e) {
      print('Error deleting expense: $e');
    }
  }

  Future<List<Expense>> getExpensesByCategory(String category) async {
    if (kIsWeb) {
      return _expenses.where((expense) => expense.category == category).toList();
    } else {
      return await DatabaseHelper.instance.getExpensesByCategory(category);
    }
  }

  Future<List<Expense>> getExpensesByDateRange(DateTime start, DateTime end) async {
    if (kIsWeb) {
      return _expenses
          .where((expense) => expense.date.isAfter(start) && expense.date.isBefore(end))
          .toList();
    } else {
      return await DatabaseHelper.instance.getExpensesByDateRange(start, end);
    }
  }

  double getTotalExpenses() {
    return _expenses.fold(0, (sum, expense) => sum + expense.amount);
  }

  double getTotalExpensesByCategory(String category) {
    return _expenses
        .where((expense) => expense.category == category)
        .fold(0, (sum, expense) => sum + expense.amount);
  }

  List<String> getExpenseCategories() {
    final categories = _expenses.map((expense) => expense.category).toSet().toList();
    return categories;
  }
}
