import 'dart:convert';

class Invoice {
  final int? id;
  final String invoiceNumber;
  final DateTime date;
  final double totalAmount;
  final double discountAmount;
  final double taxAmount;
  final double finalAmount;
  final String paymentMethod;
  final String status;
  final int? customerId;
  final String? customerName;
  final List<InvoiceItem> items;
  final String? notes;

  Invoice({
    this.id,
    required this.invoiceNumber,
    required this.date,
    required this.totalAmount,
    required this.discountAmount,
    required this.taxAmount,
    required this.finalAmount,
    required this.paymentMethod,
    required this.status,
    this.customerId,
    this.customerName,
    required this.items,
    this.notes,
  });

  Invoice copyWith({
    int? id,
    String? invoiceNumber,
    DateTime? date,
    double? totalAmount,
    double? discountAmount,
    double? taxAmount,
    double? finalAmount,
    String? paymentMethod,
    String? status,
    int? customerId,
    String? customerName,
    List<InvoiceItem>? items,
    String? notes,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      date: date ?? this.date,
      totalAmount: totalAmount ?? this.totalAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      taxAmount: taxAmount ?? this.taxAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      items: items ?? this.items,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'date': date.toIso8601String(),
      'totalAmount': totalAmount,
      'discountAmount': discountAmount,
      'taxAmount': taxAmount,
      'finalAmount': finalAmount,
      'paymentMethod': paymentMethod,
      'status': status,
      'customerId': customerId,
      'customerName': customerName,
      'items': items.map((x) => x.toMap()).toList(),
      'notes': notes,
    };
  }

  factory Invoice.fromMap(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'],
      invoiceNumber: map['invoiceNumber'],
      date: DateTime.parse(map['date']),
      totalAmount: map['totalAmount'],
      discountAmount: map['discountAmount'],
      taxAmount: map['taxAmount'],
      finalAmount: map['finalAmount'],
      paymentMethod: map['paymentMethod'],
      status: map['status'],
      customerId: map['customerId'],
      customerName: map['customerName'],
      items: List<InvoiceItem>.from(
        (jsonDecode(map['items']) as List).map((x) => InvoiceItem.fromMap(x)),
      ),
      notes: map['notes'],
    );
  }

  // Helper method for database operations
  Map<String, dynamic> toMapForDb() {
    return {
      'invoiceNumber': invoiceNumber,
      'date': date.toIso8601String(),
      'totalAmount': totalAmount,
      'discountAmount': discountAmount,
      'taxAmount': taxAmount,
      'finalAmount': finalAmount,
      'paymentMethod': paymentMethod,
      'status': status,
      'customerId': customerId,
      'customerName': customerName,
      'items': jsonEncode(items.map((x) => x.toMap()).toList()),
      'notes': notes,
    };
  }

  factory Invoice.fromDb(Map<String, dynamic> map) {
    return Invoice(
      id: map['id'],
      invoiceNumber: map['invoiceNumber'],
      date: DateTime.parse(map['date']),
      totalAmount: map['totalAmount'],
      discountAmount: map['discountAmount'],
      taxAmount: map['taxAmount'],
      finalAmount: map['finalAmount'],
      paymentMethod: map['paymentMethod'],
      status: map['status'],
      customerId: map['customerId'],
      customerName: map['customerName'],
      items: List<InvoiceItem>.from(
        (jsonDecode(map['items']) as List).map((x) => InvoiceItem.fromMap(x)),
      ),
      notes: map['notes'],
    );
  }

  // Factory method for database operations with separate items
  factory Invoice.fromMapWithItems(
    Map<String, dynamic> map,
    List<InvoiceItem> items,
  ) {
    return Invoice(
      id: map['id'] as int?,
      invoiceNumber: map['invoiceNumber'] as String,
      date: DateTime.parse(map['date'] as String),
      totalAmount: map['totalAmount'] as double,
      discountAmount: map['discountAmount'] as double,
      taxAmount: map['taxAmount'] as double,
      finalAmount: map['finalAmount'] as double,
      paymentMethod: map['paymentMethod'] as String,
      status: map['status'] as String,
      customerName: map['customerName'] as String?,
      items: items,
      notes: map['notes'] as String?,
    );
  }
}

class InvoiceItem {
  final int? id;
  final int? productId;
  final String productName;
  final double quantity;
  final double unitPrice;
  final double totalPrice;
  final String? notes;

  InvoiceItem({
    this.id,
    this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.notes,
  });

  InvoiceItem copyWith({
    int? id,
    int? productId,
    String? productName,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    String? notes,
  }) {
    return InvoiceItem(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      notes: notes ?? this.notes,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'notes': notes,
    };
  }

  factory InvoiceItem.fromMap(Map<String, dynamic> map) {
    return InvoiceItem(
      id: map['id'],
      productId: map['productId'],
      productName: map['productName'],
      quantity: map['quantity'],
      unitPrice: map['unitPrice'],
      totalPrice: map['totalPrice'],
      notes: map['notes'],
    );
  }
}
