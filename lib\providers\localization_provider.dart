import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';

enum SupportedLanguage { arabic, english, french }

class LocalizationProvider extends ChangeNotifier {
  static const String _languageKey = 'selected_language';
  
  SupportedLanguage _currentLanguage = SupportedLanguage.arabic;
  bool _isLoading = true;

  // Getters
  SupportedLanguage get currentLanguage => _currentLanguage;
  bool get isLoading => _isLoading;
  bool get isRTL => _currentLanguage == SupportedLanguage.arabic;
  String get languageCode => _getLanguageCode(_currentLanguage);
  String get countryCode => _getCountryCode(_currentLanguage);
  Locale get locale => Locale(languageCode, countryCode);

  LocalizationProvider() {
    _loadLanguage();
  }

  String _getLanguageCode(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.arabic:
        return 'ar';
      case SupportedLanguage.english:
        return 'en';
      case SupportedLanguage.french:
        return 'fr';
    }
  }

  String _getCountryCode(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.arabic:
        return 'DZ';
      case SupportedLanguage.english:
        return 'US';
      case SupportedLanguage.french:
        return 'FR';
    }
  }

  Future<void> _loadLanguage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final languageString = prefs.getString(_languageKey) ?? 'arabic';
      _currentLanguage = SupportedLanguage.values.firstWhere(
        (lang) => lang.name == languageString,
        orElse: () => SupportedLanguage.arabic,
      );
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading language: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> setLanguage(SupportedLanguage language) async {
    if (_currentLanguage == language) return;

    try {
      _currentLanguage = language;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, language.name);
      notifyListeners();
    } catch (e) {
      debugPrint('Error setting language: $e');
    }
  }

  // Localized strings
  String translate(String key) {
    final translations = _getTranslations();
    return translations[key] ?? key;
  }

  Map<String, String> _getTranslations() {
    switch (_currentLanguage) {
      case SupportedLanguage.arabic:
        return _arabicTranslations;
      case SupportedLanguage.english:
        return _englishTranslations;
      case SupportedLanguage.french:
        return _frenchTranslations;
    }
  }

  // Format currency based on language and locale
  String formatCurrency(double amount, String currencyCode) {
    final formatter = NumberFormat.currency(
      locale: '${languageCode}_$countryCode',
      symbol: _getCurrencySymbol(currencyCode),
      decimalDigits: _getCurrencyDecimalPlaces(currencyCode),
    );
    return formatter.format(amount);
  }

  // Format date based on language
  String formatDate(DateTime date) {
    final formatter = DateFormat.yMMMd(languageCode);
    return formatter.format(date);
  }

  // Format time based on language
  String formatTime(DateTime time) {
    final formatter = DateFormat.Hm(languageCode);
    return formatter.format(time);
  }

  // Format number based on language
  String formatNumber(double number) {
    final formatter = NumberFormat.decimalPattern(languageCode);
    return formatter.format(number);
  }

  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode) {
      case 'DZD':
        return 'د.ج';
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      default:
        return currencyCode;
    }
  }

  int _getCurrencyDecimalPlaces(String currencyCode) {
    switch (currencyCode) {
      case 'DZD':
        return 2;
      case 'USD':
      case 'EUR':
      case 'GBP':
        return 2;
      default:
        return 2;
    }
  }

  // Arabic translations
  static const Map<String, String> _arabicTranslations = {
    // App general
    'app_name': 'نظام نقاط البيع',
    'welcome': 'مرحباً',
    'loading': 'جاري التحميل...',
    'error': 'خطأ',
    'success': 'نجح',
    'cancel': 'إلغاء',
    'ok': 'موافق',
    'save': 'حفظ',
    'delete': 'حذف',
    'edit': 'تعديل',
    'add': 'إضافة',
    'search': 'بحث',
    'filter': 'تصفية',
    'sort': 'ترتيب',
    'refresh': 'تحديث',
    'back': 'رجوع',
    'next': 'التالي',
    'previous': 'السابق',
    'close': 'إغلاق',
    'open': 'فتح',
    'yes': 'نعم',
    'no': 'لا',
    
    // Navigation
    'dashboard': 'لوحة التحكم',
    'pos': 'نقطة البيع',
    'inventory': 'المخزون',
    'products': 'المنتجات',
    'categories': 'الفئات',
    'customers': 'العملاء',
    'suppliers': 'الموردون',
    'transactions': 'المعاملات',
    'reports': 'التقارير',
    'settings': 'الإعدادات',
    'profile': 'الملف الشخصي',
    
    // Products
    'product_name': 'اسم المنتج',
    'product_price': 'سعر المنتج',
    'product_cost': 'تكلفة المنتج',
    'product_stock': 'المخزون',
    'product_category': 'فئة المنتج',
    'product_barcode': 'الباركود',
    'product_description': 'وصف المنتج',
    'low_stock': 'مخزون منخفض',
    'out_of_stock': 'نفد المخزون',
    'in_stock': 'متوفر',
    
    // Transactions
    'total_amount': 'المبلغ الإجمالي',
    'paid_amount': 'المبلغ المدفوع',
    'change_amount': 'المبلغ المتبقي',
    'discount': 'خصم',
    'tax': 'ضريبة',
    'payment_method': 'طريقة الدفع',
    'cash': 'نقداً',
    'card': 'بطاقة',
    'credit': 'آجل',
    
    // Settings
    'general_settings': 'الإعدادات العامة',
    'language': 'اللغة',
    'currency': 'العملة',
    'theme': 'المظهر',
    'notifications': 'الإشعارات',
    'backup': 'النسخ الاحتياطي',
    'restore': 'الاستعادة',
    'dark_mode': 'الوضع المظلم',
    'light_mode': 'الوضع الفاتح',
    
    // Backup
    'create_backup': 'إنشاء نسخة احتياطية',
    'restore_backup': 'استعادة نسخة احتياطية',
    'backup_created': 'تم إنشاء النسخة الاحتياطية',
    'backup_restored': 'تم استعادة النسخة الاحتياطية',
    'backup_failed': 'فشل في النسخ الاحتياطي',
    'restore_failed': 'فشل في الاستعادة',
    
    // Store info
    'store_name': 'اسم المتجر',
    'store_address': 'عنوان المتجر',
    'store_phone': 'هاتف المتجر',
    'store_email': 'بريد المتجر الإلكتروني',
    'tax_id': 'الرقم الضريبي',
    'website': 'الموقع الإلكتروني',
    
    // Notifications
    'low_stock_alert': 'تنبيه مخزون منخفض',
    'out_of_stock_alert': 'تنبيه نفاد المخزون',
    'sale_completed': 'تمت عملية البيع',
    'backup_completed': 'اكتمل النسخ الاحتياطي',
    
    // Errors
    'network_error': 'خطأ في الشبكة',
    'database_error': 'خطأ في قاعدة البيانات',
    'validation_error': 'خطأ في التحقق',
    'permission_denied': 'تم رفض الإذن',
    'file_not_found': 'الملف غير موجود',
    'invalid_data': 'بيانات غير صحيحة',
  };

  // English translations
  static const Map<String, String> _englishTranslations = {
    // App general
    'app_name': 'POS System',
    'welcome': 'Welcome',
    'loading': 'Loading...',
    'error': 'Error',
    'success': 'Success',
    'cancel': 'Cancel',
    'ok': 'OK',
    'save': 'Save',
    'delete': 'Delete',
    'edit': 'Edit',
    'add': 'Add',
    'search': 'Search',
    'filter': 'Filter',
    'sort': 'Sort',
    'refresh': 'Refresh',
    'back': 'Back',
    'next': 'Next',
    'previous': 'Previous',
    'close': 'Close',
    'open': 'Open',
    'yes': 'Yes',
    'no': 'No',
    
    // Navigation
    'dashboard': 'Dashboard',
    'pos': 'POS',
    'inventory': 'Inventory',
    'products': 'Products',
    'categories': 'Categories',
    'customers': 'Customers',
    'suppliers': 'Suppliers',
    'transactions': 'Transactions',
    'reports': 'Reports',
    'settings': 'Settings',
    'profile': 'Profile',
    
    // Products
    'product_name': 'Product Name',
    'product_price': 'Product Price',
    'product_cost': 'Product Cost',
    'product_stock': 'Stock',
    'product_category': 'Product Category',
    'product_barcode': 'Barcode',
    'product_description': 'Product Description',
    'low_stock': 'Low Stock',
    'out_of_stock': 'Out of Stock',
    'in_stock': 'In Stock',
    
    // Transactions
    'total_amount': 'Total Amount',
    'paid_amount': 'Paid Amount',
    'change_amount': 'Change Amount',
    'discount': 'Discount',
    'tax': 'Tax',
    'payment_method': 'Payment Method',
    'cash': 'Cash',
    'card': 'Card',
    'credit': 'Credit',
    
    // Settings
    'general_settings': 'General Settings',
    'language': 'Language',
    'currency': 'Currency',
    'theme': 'Theme',
    'notifications': 'Notifications',
    'backup': 'Backup',
    'restore': 'Restore',
    'dark_mode': 'Dark Mode',
    'light_mode': 'Light Mode',
    
    // Backup
    'create_backup': 'Create Backup',
    'restore_backup': 'Restore Backup',
    'backup_created': 'Backup Created',
    'backup_restored': 'Backup Restored',
    'backup_failed': 'Backup Failed',
    'restore_failed': 'Restore Failed',
    
    // Store info
    'store_name': 'Store Name',
    'store_address': 'Store Address',
    'store_phone': 'Store Phone',
    'store_email': 'Store Email',
    'tax_id': 'Tax ID',
    'website': 'Website',
    
    // Notifications
    'low_stock_alert': 'Low Stock Alert',
    'out_of_stock_alert': 'Out of Stock Alert',
    'sale_completed': 'Sale Completed',
    'backup_completed': 'Backup Completed',
    
    // Errors
    'network_error': 'Network Error',
    'database_error': 'Database Error',
    'validation_error': 'Validation Error',
    'permission_denied': 'Permission Denied',
    'file_not_found': 'File Not Found',
    'invalid_data': 'Invalid Data',
  };

  // French translations
  static const Map<String, String> _frenchTranslations = {
    // App general
    'app_name': 'Système de Point de Vente',
    'welcome': 'Bienvenue',
    'loading': 'Chargement...',
    'error': 'Erreur',
    'success': 'Succès',
    'cancel': 'Annuler',
    'ok': 'OK',
    'save': 'Enregistrer',
    'delete': 'Supprimer',
    'edit': 'Modifier',
    'add': 'Ajouter',
    'search': 'Rechercher',
    'filter': 'Filtrer',
    'sort': 'Trier',
    'refresh': 'Actualiser',
    'back': 'Retour',
    'next': 'Suivant',
    'previous': 'Précédent',
    'close': 'Fermer',
    'open': 'Ouvrir',
    'yes': 'Oui',
    'no': 'Non',
    
    // Navigation
    'dashboard': 'Tableau de Bord',
    'pos': 'Point de Vente',
    'inventory': 'Inventaire',
    'products': 'Produits',
    'categories': 'Catégories',
    'customers': 'Clients',
    'suppliers': 'Fournisseurs',
    'transactions': 'Transactions',
    'reports': 'Rapports',
    'settings': 'Paramètres',
    'profile': 'Profil',
  };
}
