import 'package:pos_app/models/cart_item.dart';

class Transaction {
  final int? id;
  final String transactionId;
  final DateTime date;
  final List<CartItem> items;
  final double total;
  final double amountPaid;
  final double change;
  final String paymentMethod;

  Transaction({
    this.id,
    required this.transactionId,
    required this.date,
    required this.items,
    required this.total,
    required this.amountPaid,
    required this.change,
    required this.paymentMethod,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'transactionId': transactionId,
      'date': date.toIso8601String(),
      'total': total,
      'amountPaid': amountPaid,
      'change': change,
      'paymentMethod': paymentMethod,
    };
  }

  factory Transaction.fromMap(Map<String, dynamic> map, List<CartItem> items) {
    return Transaction(
      id: map['id'],
      transactionId: map['transactionId'],
      date: DateTime.parse(map['date']),
      items: items,
      total: map['total'],
      amountPaid: map['amountPaid'],
      change: map['change'],
      paymentMethod: map['paymentMethod'],
    );
  }
}
