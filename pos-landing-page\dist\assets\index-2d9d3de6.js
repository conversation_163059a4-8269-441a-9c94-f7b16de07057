(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Bm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Kd={exports:{}},_o={},Qd={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ci=Symbol.for("react.element"),Um=Symbol.for("react.portal"),$m=Symbol.for("react.fragment"),Wm=Symbol.for("react.strict_mode"),Hm=Symbol.for("react.profiler"),Gm=Symbol.for("react.provider"),Km=Symbol.for("react.context"),Qm=Symbol.for("react.forward_ref"),Ym=Symbol.for("react.suspense"),Xm=Symbol.for("react.memo"),Zm=Symbol.for("react.lazy"),Tu=Symbol.iterator;function qm(e){return e===null||typeof e!="object"?null:(e=Tu&&e[Tu]||e["@@iterator"],typeof e=="function"?e:null)}var Yd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Xd=Object.assign,Zd={};function lr(e,t,n){this.props=e,this.context=t,this.refs=Zd,this.updater=n||Yd}lr.prototype.isReactComponent={};lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function qd(){}qd.prototype=lr.prototype;function ll(e,t,n){this.props=e,this.context=t,this.refs=Zd,this.updater=n||Yd}var ul=ll.prototype=new qd;ul.constructor=ll;Xd(ul,lr.prototype);ul.isPureReactComponent=!0;var bu=Array.isArray,Jd=Object.prototype.hasOwnProperty,cl={current:null},ef={key:!0,ref:!0,__self:!0,__source:!0};function tf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Jd.call(t,r)&&!ef.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:ci,type:e,key:o,ref:s,props:i,_owner:cl.current}}function Jm(e,t){return{$$typeof:ci,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function dl(e){return typeof e=="object"&&e!==null&&e.$$typeof===ci}function e0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Nu=/\/+/g;function ds(e,t){return typeof e=="object"&&e!==null&&e.key!=null?e0(""+e.key):t.toString(36)}function $i(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ci:case Um:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+ds(s,0):r,bu(i)?(n="",e!=null&&(n=e.replace(Nu,"$&/")+"/"),$i(i,t,n,"",function(u){return u})):i!=null&&(dl(i)&&(i=Jm(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Nu,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",bu(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+ds(o,a);s+=$i(o,t,n,l,i)}else if(l=qm(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+ds(o,a++),s+=$i(o,t,n,l,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function wi(e,t,n){if(e==null)return e;var r=[],i=0;return $i(e,r,"","",function(o){return t.call(n,o,i++)}),r}function t0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},Wi={transition:null},n0={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:Wi,ReactCurrentOwner:cl};function nf(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:wi,forEach:function(e,t,n){wi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return wi(e,function(){t++}),t},toArray:function(e){return wi(e,function(t){return t})||[]},only:function(e){if(!dl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};I.Component=lr;I.Fragment=$m;I.Profiler=Hm;I.PureComponent=ll;I.StrictMode=Wm;I.Suspense=Ym;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=n0;I.act=nf;I.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Xd({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=cl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Jd.call(t,l)&&!ef.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ci,type:e.type,key:i,ref:o,props:r,_owner:s}};I.createContext=function(e){return e={$$typeof:Km,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Gm,_context:e},e.Consumer=e};I.createElement=tf;I.createFactory=function(e){var t=tf.bind(null,e);return t.type=e,t};I.createRef=function(){return{current:null}};I.forwardRef=function(e){return{$$typeof:Qm,render:e}};I.isValidElement=dl;I.lazy=function(e){return{$$typeof:Zm,_payload:{_status:-1,_result:e},_init:t0}};I.memo=function(e,t){return{$$typeof:Xm,type:e,compare:t===void 0?null:t}};I.startTransition=function(e){var t=Wi.transition;Wi.transition={};try{e()}finally{Wi.transition=t}};I.unstable_act=nf;I.useCallback=function(e,t){return Te.current.useCallback(e,t)};I.useContext=function(e){return Te.current.useContext(e)};I.useDebugValue=function(){};I.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};I.useEffect=function(e,t){return Te.current.useEffect(e,t)};I.useId=function(){return Te.current.useId()};I.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};I.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};I.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};I.useMemo=function(e,t){return Te.current.useMemo(e,t)};I.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};I.useRef=function(e){return Te.current.useRef(e)};I.useState=function(e){return Te.current.useState(e)};I.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};I.useTransition=function(){return Te.current.useTransition()};I.version="18.3.1";Qd.exports=I;var b=Qd.exports;const Br=Bm(b);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r0=b,i0=Symbol.for("react.element"),o0=Symbol.for("react.fragment"),s0=Object.prototype.hasOwnProperty,a0=r0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l0={key:!0,ref:!0,__self:!0,__source:!0};function rf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)s0.call(t,r)&&!l0.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:i0,type:e,key:o,ref:s,props:i,_owner:a0.current}}_o.Fragment=o0;_o.jsx=rf;_o.jsxs=rf;Kd.exports=_o;var f=Kd.exports,Ys={},of={exports:{}},Fe={},sf={exports:{}},af={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,M){var z=N.length;N.push(M);e:for(;0<z;){var A=z-1>>>1,U=N[A];if(0<i(U,M))N[A]=M,N[z]=U,z=A;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var M=N[0],z=N.pop();if(z!==M){N[0]=z;e:for(var A=0,U=N.length,oe=U>>>1;A<oe;){var st=2*(A+1)-1,bn=N[st],Re=st+1,nn=N[Re];if(0>i(bn,z))Re<U&&0>i(nn,bn)?(N[A]=nn,N[Re]=z,A=Re):(N[A]=bn,N[st]=z,A=st);else if(Re<U&&0>i(nn,z))N[A]=nn,N[Re]=z,A=Re;else break e}}return M}function i(N,M){var z=N.sortIndex-M.sortIndex;return z!==0?z:N.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],u=[],c=1,d=null,p=3,g=!1,v=!1,x=!1,k=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(N){for(var M=n(u);M!==null;){if(M.callback===null)r(u);else if(M.startTime<=N)r(u),M.sortIndex=M.expirationTime,t(l,M);else break;M=n(u)}}function w(N){if(x=!1,m(N),!v)if(n(l)!==null)v=!0,W(S);else{var M=n(u);M!==null&&ke(w,M.startTime-N)}}function S(N,M){v=!1,x&&(x=!1,y(P),P=-1),g=!0;var z=p;try{for(m(M),d=n(l);d!==null&&(!(d.expirationTime>M)||N&&!ne());){var A=d.callback;if(typeof A=="function"){d.callback=null,p=d.priorityLevel;var U=A(d.expirationTime<=M);M=e.unstable_now(),typeof U=="function"?d.callback=U:d===n(l)&&r(l),m(M)}else r(l);d=n(l)}if(d!==null)var oe=!0;else{var st=n(u);st!==null&&ke(w,st.startTime-M),oe=!1}return oe}finally{d=null,p=z,g=!1}}var T=!1,j=null,P=-1,R=5,V=-1;function ne(){return!(e.unstable_now()-V<R)}function _(){if(j!==null){var N=e.unstable_now();V=N;var M=!0;try{M=j(!0,N)}finally{M?de():(T=!1,j=null)}}else T=!1}var de;if(typeof h=="function")de=function(){h(_)};else if(typeof MessageChannel<"u"){var Z=new MessageChannel,Xe=Z.port2;Z.port1.onmessage=_,de=function(){Xe.postMessage(null)}}else de=function(){k(_,0)};function W(N){j=N,T||(T=!0,de())}function ke(N,M){P=k(function(){N(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,W(S))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(p){case 1:case 2:case 3:var M=3;break;default:M=p}var z=p;p=M;try{return N()}finally{p=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,M){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var z=p;p=N;try{return M()}finally{p=z}},e.unstable_scheduleCallback=function(N,M,z){var A=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?A+z:A):z=A,N){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=z+U,N={id:c++,callback:M,priorityLevel:N,startTime:z,expirationTime:U,sortIndex:-1},z>A?(N.sortIndex=z,t(u,N),n(l)===null&&N===n(u)&&(x?(y(P),P=-1):x=!0,ke(w,z-A))):(N.sortIndex=U,t(l,N),v||g||(v=!0,W(S))),N},e.unstable_shouldYield=ne,e.unstable_wrapCallback=function(N){var M=p;return function(){var z=p;p=M;try{return N.apply(this,arguments)}finally{p=z}}}})(af);sf.exports=af;var u0=sf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var c0=b,Oe=u0;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var lf=new Set,Ur={};function kn(e,t){Jn(e,t),Jn(e+"Capture",t)}function Jn(e,t){for(Ur[e]=t,e=0;e<t.length;e++)lf.add(t[e])}var St=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Xs=Object.prototype.hasOwnProperty,d0=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Eu={},Mu={};function f0(e){return Xs.call(Mu,e)?!0:Xs.call(Eu,e)?!1:d0.test(e)?Mu[e]=!0:(Eu[e]=!0,!1)}function p0(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function h0(e,t,n,r){if(t===null||typeof t>"u"||p0(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function be(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ge[e]=new be(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ge[t]=new be(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ge[e]=new be(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ge[e]=new be(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ge[e]=new be(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ge[e]=new be(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ge[e]=new be(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ge[e]=new be(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ge[e]=new be(e,5,!1,e.toLowerCase(),null,!1,!1)});var fl=/[\-:]([a-z])/g;function pl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(fl,pl);ge[t]=new be(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(fl,pl);ge[t]=new be(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(fl,pl);ge[t]=new be(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ge[e]=new be(e,1,!1,e.toLowerCase(),null,!1,!1)});ge.xlinkHref=new be("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ge[e]=new be(e,1,!1,e.toLowerCase(),null,!0,!0)});function hl(e,t,n,r){var i=ge.hasOwnProperty(t)?ge[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(h0(t,n,i,r)&&(n=null),r||i===null?f0(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Tt=c0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Si=Symbol.for("react.element"),Mn=Symbol.for("react.portal"),An=Symbol.for("react.fragment"),ml=Symbol.for("react.strict_mode"),Zs=Symbol.for("react.profiler"),uf=Symbol.for("react.provider"),cf=Symbol.for("react.context"),gl=Symbol.for("react.forward_ref"),qs=Symbol.for("react.suspense"),Js=Symbol.for("react.suspense_list"),yl=Symbol.for("react.memo"),Mt=Symbol.for("react.lazy"),df=Symbol.for("react.offscreen"),Au=Symbol.iterator;function dr(e){return e===null||typeof e!="object"?null:(e=Au&&e[Au]||e["@@iterator"],typeof e=="function"?e:null)}var te=Object.assign,fs;function Cr(e){if(fs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);fs=t&&t[1]||""}return`
`+fs+e}var ps=!1;function hs(e,t){if(!e||ps)return"";ps=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var l=`
`+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{ps=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Cr(e):""}function m0(e){switch(e.tag){case 5:return Cr(e.type);case 16:return Cr("Lazy");case 13:return Cr("Suspense");case 19:return Cr("SuspenseList");case 0:case 2:case 15:return e=hs(e.type,!1),e;case 11:return e=hs(e.type.render,!1),e;case 1:return e=hs(e.type,!0),e;default:return""}}function ea(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case An:return"Fragment";case Mn:return"Portal";case Zs:return"Profiler";case ml:return"StrictMode";case qs:return"Suspense";case Js:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case cf:return(e.displayName||"Context")+".Consumer";case uf:return(e._context.displayName||"Context")+".Provider";case gl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case yl:return t=e.displayName||null,t!==null?t:ea(e.type)||"Memo";case Mt:t=e._payload,e=e._init;try{return ea(e(t))}catch{}}return null}function g0(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ea(t);case 8:return t===ml?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Kt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ff(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function y0(e){var t=ff(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ki(e){e._valueTracker||(e._valueTracker=y0(e))}function pf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ff(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ro(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ta(e,t){var n=t.checked;return te({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Vu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Kt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function hf(e,t){t=t.checked,t!=null&&hl(e,"checked",t,!1)}function na(e,t){hf(e,t);var n=Kt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ra(e,t.type,n):t.hasOwnProperty("defaultValue")&&ra(e,t.type,Kt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Lu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ra(e,t,n){(t!=="number"||ro(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Pr=Array.isArray;function Kn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Kt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function ia(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return te({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(Pr(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Kt(n)}}function mf(e,t){var n=Kt(t.value),r=Kt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Du(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function oa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ci,yf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ci=Ci||document.createElement("div"),Ci.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ci.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function $r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Nr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},v0=["Webkit","ms","Moz","O"];Object.keys(Nr).forEach(function(e){v0.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Nr[t]=Nr[e]})});function vf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Nr.hasOwnProperty(e)&&Nr[e]?(""+t).trim():t+"px"}function xf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=vf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var x0=te({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function sa(e,t){if(t){if(x0[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function aa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var la=null;function vl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ua=null,Qn=null,Yn=null;function zu(e){if(e=pi(e)){if(typeof ua!="function")throw Error(C(280));var t=e.stateNode;t&&(t=Uo(t),ua(e.stateNode,e.type,t))}}function wf(e){Qn?Yn?Yn.push(e):Yn=[e]:Qn=e}function Sf(){if(Qn){var e=Qn,t=Yn;if(Yn=Qn=null,zu(e),t)for(e=0;e<t.length;e++)zu(t[e])}}function kf(e,t){return e(t)}function Cf(){}var ms=!1;function Pf(e,t,n){if(ms)return e(t,n);ms=!0;try{return kf(e,t,n)}finally{ms=!1,(Qn!==null||Yn!==null)&&(Cf(),Sf())}}function Wr(e,t){var n=e.stateNode;if(n===null)return null;var r=Uo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var ca=!1;if(St)try{var fr={};Object.defineProperty(fr,"passive",{get:function(){ca=!0}}),window.addEventListener("test",fr,fr),window.removeEventListener("test",fr,fr)}catch{ca=!1}function w0(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Er=!1,io=null,oo=!1,da=null,S0={onError:function(e){Er=!0,io=e}};function k0(e,t,n,r,i,o,s,a,l){Er=!1,io=null,w0.apply(S0,arguments)}function C0(e,t,n,r,i,o,s,a,l){if(k0.apply(this,arguments),Er){if(Er){var u=io;Er=!1,io=null}else throw Error(C(198));oo||(oo=!0,da=u)}}function Cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function jf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function _u(e){if(Cn(e)!==e)throw Error(C(188))}function P0(e){var t=e.alternate;if(!t){if(t=Cn(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return _u(i),e;if(o===r)return _u(i),t;o=o.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function Tf(e){return e=P0(e),e!==null?bf(e):null}function bf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=bf(e);if(t!==null)return t;e=e.sibling}return null}var Nf=Oe.unstable_scheduleCallback,Ou=Oe.unstable_cancelCallback,j0=Oe.unstable_shouldYield,T0=Oe.unstable_requestPaint,se=Oe.unstable_now,b0=Oe.unstable_getCurrentPriorityLevel,xl=Oe.unstable_ImmediatePriority,Ef=Oe.unstable_UserBlockingPriority,so=Oe.unstable_NormalPriority,N0=Oe.unstable_LowPriority,Mf=Oe.unstable_IdlePriority,Oo=null,ct=null;function E0(e){if(ct&&typeof ct.onCommitFiberRoot=="function")try{ct.onCommitFiberRoot(Oo,e,void 0,(e.current.flags&128)===128)}catch{}}var rt=Math.clz32?Math.clz32:V0,M0=Math.log,A0=Math.LN2;function V0(e){return e>>>=0,e===0?32:31-(M0(e)/A0|0)|0}var Pi=64,ji=4194304;function jr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ao(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=jr(a):(o&=s,o!==0&&(r=jr(o)))}else s=n&~i,s!==0?r=jr(s):o!==0&&(r=jr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-rt(t),i=1<<n,r|=e[n],t&=~i;return r}function L0(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function R0(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-rt(o),a=1<<s,l=i[s];l===-1?(!(a&n)||a&r)&&(i[s]=L0(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function fa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Af(){var e=Pi;return Pi<<=1,!(Pi&4194240)&&(Pi=64),e}function gs(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function di(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-rt(t),e[t]=n}function D0(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-rt(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function wl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-rt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var $=0;function Vf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Lf,Sl,Rf,Df,zf,pa=!1,Ti=[],_t=null,Ot=null,It=null,Hr=new Map,Gr=new Map,Lt=[],z0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Iu(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Ot=null;break;case"mouseover":case"mouseout":It=null;break;case"pointerover":case"pointerout":Hr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Gr.delete(t.pointerId)}}function pr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=pi(t),t!==null&&Sl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function _0(e,t,n,r,i){switch(t){case"focusin":return _t=pr(_t,e,t,n,r,i),!0;case"dragenter":return Ot=pr(Ot,e,t,n,r,i),!0;case"mouseover":return It=pr(It,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Hr.set(o,pr(Hr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Gr.set(o,pr(Gr.get(o)||null,e,t,n,r,i)),!0}return!1}function _f(e){var t=cn(e.target);if(t!==null){var n=Cn(t);if(n!==null){if(t=n.tag,t===13){if(t=jf(n),t!==null){e.blockedOn=t,zf(e.priority,function(){Rf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Hi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ha(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);la=r,n.target.dispatchEvent(r),la=null}else return t=pi(n),t!==null&&Sl(t),e.blockedOn=n,!1;t.shift()}return!0}function Fu(e,t,n){Hi(e)&&n.delete(t)}function O0(){pa=!1,_t!==null&&Hi(_t)&&(_t=null),Ot!==null&&Hi(Ot)&&(Ot=null),It!==null&&Hi(It)&&(It=null),Hr.forEach(Fu),Gr.forEach(Fu)}function hr(e,t){e.blockedOn===t&&(e.blockedOn=null,pa||(pa=!0,Oe.unstable_scheduleCallback(Oe.unstable_NormalPriority,O0)))}function Kr(e){function t(i){return hr(i,e)}if(0<Ti.length){hr(Ti[0],e);for(var n=1;n<Ti.length;n++){var r=Ti[n];r.blockedOn===e&&(r.blockedOn=null)}}for(_t!==null&&hr(_t,e),Ot!==null&&hr(Ot,e),It!==null&&hr(It,e),Hr.forEach(t),Gr.forEach(t),n=0;n<Lt.length;n++)r=Lt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&(n=Lt[0],n.blockedOn===null);)_f(n),n.blockedOn===null&&Lt.shift()}var Xn=Tt.ReactCurrentBatchConfig,lo=!0;function I0(e,t,n,r){var i=$,o=Xn.transition;Xn.transition=null;try{$=1,kl(e,t,n,r)}finally{$=i,Xn.transition=o}}function F0(e,t,n,r){var i=$,o=Xn.transition;Xn.transition=null;try{$=4,kl(e,t,n,r)}finally{$=i,Xn.transition=o}}function kl(e,t,n,r){if(lo){var i=ha(e,t,n,r);if(i===null)Ts(e,t,r,uo,n),Iu(e,r);else if(_0(i,e,t,n,r))r.stopPropagation();else if(Iu(e,r),t&4&&-1<z0.indexOf(e)){for(;i!==null;){var o=pi(i);if(o!==null&&Lf(o),o=ha(e,t,n,r),o===null&&Ts(e,t,r,uo,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else Ts(e,t,r,null,n)}}var uo=null;function ha(e,t,n,r){if(uo=null,e=vl(r),e=cn(e),e!==null)if(t=Cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=jf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return uo=e,null}function Of(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(b0()){case xl:return 1;case Ef:return 4;case so:case N0:return 16;case Mf:return 536870912;default:return 16}default:return 16}}var Dt=null,Cl=null,Gi=null;function If(){if(Gi)return Gi;var e,t=Cl,n=t.length,r,i="value"in Dt?Dt.value:Dt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Gi=i.slice(e,1<r?1-r:void 0)}function Ki(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bi(){return!0}function Bu(){return!1}function Be(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?bi:Bu,this.isPropagationStopped=Bu,this}return te(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=bi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=bi)},persist:function(){},isPersistent:bi}),t}var ur={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pl=Be(ur),fi=te({},ur,{view:0,detail:0}),B0=Be(fi),ys,vs,mr,Io=te({},fi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==mr&&(mr&&e.type==="mousemove"?(ys=e.screenX-mr.screenX,vs=e.screenY-mr.screenY):vs=ys=0,mr=e),ys)},movementY:function(e){return"movementY"in e?e.movementY:vs}}),Uu=Be(Io),U0=te({},Io,{dataTransfer:0}),$0=Be(U0),W0=te({},fi,{relatedTarget:0}),xs=Be(W0),H0=te({},ur,{animationName:0,elapsedTime:0,pseudoElement:0}),G0=Be(H0),K0=te({},ur,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Q0=Be(K0),Y0=te({},ur,{data:0}),$u=Be(Y0),X0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Z0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},q0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function J0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=q0[e])?!!t[e]:!1}function jl(){return J0}var eg=te({},fi,{key:function(e){if(e.key){var t=X0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ki(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Z0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jl,charCode:function(e){return e.type==="keypress"?Ki(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ki(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),tg=Be(eg),ng=te({},Io,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wu=Be(ng),rg=te({},fi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jl}),ig=Be(rg),og=te({},ur,{propertyName:0,elapsedTime:0,pseudoElement:0}),sg=Be(og),ag=te({},Io,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),lg=Be(ag),ug=[9,13,27,32],Tl=St&&"CompositionEvent"in window,Mr=null;St&&"documentMode"in document&&(Mr=document.documentMode);var cg=St&&"TextEvent"in window&&!Mr,Ff=St&&(!Tl||Mr&&8<Mr&&11>=Mr),Hu=String.fromCharCode(32),Gu=!1;function Bf(e,t){switch(e){case"keyup":return ug.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Uf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Vn=!1;function dg(e,t){switch(e){case"compositionend":return Uf(t);case"keypress":return t.which!==32?null:(Gu=!0,Hu);case"textInput":return e=t.data,e===Hu&&Gu?null:e;default:return null}}function fg(e,t){if(Vn)return e==="compositionend"||!Tl&&Bf(e,t)?(e=If(),Gi=Cl=Dt=null,Vn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ff&&t.locale!=="ko"?null:t.data;default:return null}}var pg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ku(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pg[e.type]:t==="textarea"}function $f(e,t,n,r){wf(r),t=co(t,"onChange"),0<t.length&&(n=new Pl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ar=null,Qr=null;function hg(e){ep(e,0)}function Fo(e){var t=Dn(e);if(pf(t))return e}function mg(e,t){if(e==="change")return t}var Wf=!1;if(St){var ws;if(St){var Ss="oninput"in document;if(!Ss){var Qu=document.createElement("div");Qu.setAttribute("oninput","return;"),Ss=typeof Qu.oninput=="function"}ws=Ss}else ws=!1;Wf=ws&&(!document.documentMode||9<document.documentMode)}function Yu(){Ar&&(Ar.detachEvent("onpropertychange",Hf),Qr=Ar=null)}function Hf(e){if(e.propertyName==="value"&&Fo(Qr)){var t=[];$f(t,Qr,e,vl(e)),Pf(hg,t)}}function gg(e,t,n){e==="focusin"?(Yu(),Ar=t,Qr=n,Ar.attachEvent("onpropertychange",Hf)):e==="focusout"&&Yu()}function yg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Fo(Qr)}function vg(e,t){if(e==="click")return Fo(t)}function xg(e,t){if(e==="input"||e==="change")return Fo(t)}function wg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ot=typeof Object.is=="function"?Object.is:wg;function Yr(e,t){if(ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Xs.call(t,i)||!ot(e[i],t[i]))return!1}return!0}function Xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Zu(e,t){var n=Xu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Xu(n)}}function Gf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Gf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Kf(){for(var e=window,t=ro();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ro(e.document)}return t}function bl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Sg(e){var t=Kf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Gf(n.ownerDocument.documentElement,n)){if(r!==null&&bl(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Zu(n,o);var s=Zu(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var kg=St&&"documentMode"in document&&11>=document.documentMode,Ln=null,ma=null,Vr=null,ga=!1;function qu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ga||Ln==null||Ln!==ro(r)||(r=Ln,"selectionStart"in r&&bl(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Vr&&Yr(Vr,r)||(Vr=r,r=co(ma,"onSelect"),0<r.length&&(t=new Pl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ln)))}function Ni(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rn={animationend:Ni("Animation","AnimationEnd"),animationiteration:Ni("Animation","AnimationIteration"),animationstart:Ni("Animation","AnimationStart"),transitionend:Ni("Transition","TransitionEnd")},ks={},Qf={};St&&(Qf=document.createElement("div").style,"AnimationEvent"in window||(delete Rn.animationend.animation,delete Rn.animationiteration.animation,delete Rn.animationstart.animation),"TransitionEvent"in window||delete Rn.transitionend.transition);function Bo(e){if(ks[e])return ks[e];if(!Rn[e])return e;var t=Rn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Qf)return ks[e]=t[n];return e}var Yf=Bo("animationend"),Xf=Bo("animationiteration"),Zf=Bo("animationstart"),qf=Bo("transitionend"),Jf=new Map,Ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zt(e,t){Jf.set(e,t),kn(t,[e])}for(var Cs=0;Cs<Ju.length;Cs++){var Ps=Ju[Cs],Cg=Ps.toLowerCase(),Pg=Ps[0].toUpperCase()+Ps.slice(1);Zt(Cg,"on"+Pg)}Zt(Yf,"onAnimationEnd");Zt(Xf,"onAnimationIteration");Zt(Zf,"onAnimationStart");Zt("dblclick","onDoubleClick");Zt("focusin","onFocus");Zt("focusout","onBlur");Zt(qf,"onTransitionEnd");Jn("onMouseEnter",["mouseout","mouseover"]);Jn("onMouseLeave",["mouseout","mouseover"]);Jn("onPointerEnter",["pointerout","pointerover"]);Jn("onPointerLeave",["pointerout","pointerover"]);kn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));kn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));kn("onBeforeInput",["compositionend","keypress","textInput","paste"]);kn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));kn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));kn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Tr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Tr));function ec(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,C0(r,t,void 0,e),e.currentTarget=null}function ep(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break e;ec(i,a,u),o=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break e;ec(i,a,u),o=l}}}if(oo)throw e=da,oo=!1,da=null,e}function K(e,t){var n=t[Sa];n===void 0&&(n=t[Sa]=new Set);var r=e+"__bubble";n.has(r)||(tp(t,e,2,!1),n.add(r))}function js(e,t,n){var r=0;t&&(r|=4),tp(n,e,r,t)}var Ei="_reactListening"+Math.random().toString(36).slice(2);function Xr(e){if(!e[Ei]){e[Ei]=!0,lf.forEach(function(n){n!=="selectionchange"&&(jg.has(n)||js(n,!1,e),js(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ei]||(t[Ei]=!0,js("selectionchange",!1,t))}}function tp(e,t,n,r){switch(Of(t)){case 1:var i=I0;break;case 4:i=F0;break;default:i=kl}n=i.bind(null,t,n,e),i=void 0,!ca||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ts(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;s=s.return}for(;a!==null;){if(s=cn(a),s===null)return;if(l=s.tag,l===5||l===6){r=o=s;continue e}a=a.parentNode}}r=r.return}Pf(function(){var u=o,c=vl(n),d=[];e:{var p=Jf.get(e);if(p!==void 0){var g=Pl,v=e;switch(e){case"keypress":if(Ki(n)===0)break e;case"keydown":case"keyup":g=tg;break;case"focusin":v="focus",g=xs;break;case"focusout":v="blur",g=xs;break;case"beforeblur":case"afterblur":g=xs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Uu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=$0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=ig;break;case Yf:case Xf:case Zf:g=G0;break;case qf:g=sg;break;case"scroll":g=B0;break;case"wheel":g=lg;break;case"copy":case"cut":case"paste":g=Q0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Wu}var x=(t&4)!==0,k=!x&&e==="scroll",y=x?p!==null?p+"Capture":null:p;x=[];for(var h=u,m;h!==null;){m=h;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,y!==null&&(w=Wr(h,y),w!=null&&x.push(Zr(h,w,m)))),k)break;h=h.return}0<x.length&&(p=new g(p,v,null,n,c),d.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==la&&(v=n.relatedTarget||n.fromElement)&&(cn(v)||v[kt]))break e;if((g||p)&&(p=c.window===c?c:(p=c.ownerDocument)?p.defaultView||p.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?cn(v):null,v!==null&&(k=Cn(v),v!==k||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(x=Uu,w="onMouseLeave",y="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=Wu,w="onPointerLeave",y="onPointerEnter",h="pointer"),k=g==null?p:Dn(g),m=v==null?p:Dn(v),p=new x(w,h+"leave",g,n,c),p.target=k,p.relatedTarget=m,w=null,cn(c)===u&&(x=new x(y,h+"enter",v,n,c),x.target=m,x.relatedTarget=k,w=x),k=w,g&&v)t:{for(x=g,y=v,h=0,m=x;m;m=Nn(m))h++;for(m=0,w=y;w;w=Nn(w))m++;for(;0<h-m;)x=Nn(x),h--;for(;0<m-h;)y=Nn(y),m--;for(;h--;){if(x===y||y!==null&&x===y.alternate)break t;x=Nn(x),y=Nn(y)}x=null}else x=null;g!==null&&tc(d,p,g,x,!1),v!==null&&k!==null&&tc(d,k,v,x,!0)}}e:{if(p=u?Dn(u):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var S=mg;else if(Ku(p))if(Wf)S=xg;else{S=yg;var T=gg}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=vg);if(S&&(S=S(e,u))){$f(d,S,n,c);break e}T&&T(e,p,u),e==="focusout"&&(T=p._wrapperState)&&T.controlled&&p.type==="number"&&ra(p,"number",p.value)}switch(T=u?Dn(u):window,e){case"focusin":(Ku(T)||T.contentEditable==="true")&&(Ln=T,ma=u,Vr=null);break;case"focusout":Vr=ma=Ln=null;break;case"mousedown":ga=!0;break;case"contextmenu":case"mouseup":case"dragend":ga=!1,qu(d,n,c);break;case"selectionchange":if(kg)break;case"keydown":case"keyup":qu(d,n,c)}var j;if(Tl)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Vn?Bf(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Ff&&n.locale!=="ko"&&(Vn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Vn&&(j=If()):(Dt=c,Cl="value"in Dt?Dt.value:Dt.textContent,Vn=!0)),T=co(u,P),0<T.length&&(P=new $u(P,e,null,n,c),d.push({event:P,listeners:T}),j?P.data=j:(j=Uf(n),j!==null&&(P.data=j)))),(j=cg?dg(e,n):fg(e,n))&&(u=co(u,"onBeforeInput"),0<u.length&&(c=new $u("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=j))}ep(d,t)})}function Zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function co(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Wr(e,n),o!=null&&r.unshift(Zr(e,o,i)),o=Wr(e,t),o!=null&&r.push(Zr(e,o,i))),e=e.return}return r}function Nn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function tc(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Wr(n,o),l!=null&&s.unshift(Zr(n,l,a))):i||(l=Wr(n,o),l!=null&&s.push(Zr(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Tg=/\r\n?/g,bg=/\u0000|\uFFFD/g;function nc(e){return(typeof e=="string"?e:""+e).replace(Tg,`
`).replace(bg,"")}function Mi(e,t,n){if(t=nc(t),nc(e)!==t&&n)throw Error(C(425))}function fo(){}var ya=null,va=null;function xa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var wa=typeof setTimeout=="function"?setTimeout:void 0,Ng=typeof clearTimeout=="function"?clearTimeout:void 0,rc=typeof Promise=="function"?Promise:void 0,Eg=typeof queueMicrotask=="function"?queueMicrotask:typeof rc<"u"?function(e){return rc.resolve(null).then(e).catch(Mg)}:wa;function Mg(e){setTimeout(function(){throw e})}function bs(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Kr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Kr(t)}function Ft(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ic(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var cr=Math.random().toString(36).slice(2),ut="__reactFiber$"+cr,qr="__reactProps$"+cr,kt="__reactContainer$"+cr,Sa="__reactEvents$"+cr,Ag="__reactListeners$"+cr,Vg="__reactHandles$"+cr;function cn(e){var t=e[ut];if(t)return t;for(var n=e.parentNode;n;){if(t=n[kt]||n[ut]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ic(e);e!==null;){if(n=e[ut])return n;e=ic(e)}return t}e=n,n=e.parentNode}return null}function pi(e){return e=e[ut]||e[kt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Dn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function Uo(e){return e[qr]||null}var ka=[],zn=-1;function qt(e){return{current:e}}function Q(e){0>zn||(e.current=ka[zn],ka[zn]=null,zn--)}function H(e,t){zn++,ka[zn]=e.current,e.current=t}var Qt={},Se=qt(Qt),Me=qt(!1),yn=Qt;function er(e,t){var n=e.type.contextTypes;if(!n)return Qt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ae(e){return e=e.childContextTypes,e!=null}function po(){Q(Me),Q(Se)}function oc(e,t,n){if(Se.current!==Qt)throw Error(C(168));H(Se,t),H(Me,n)}function np(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(C(108,g0(e)||"Unknown",i));return te({},n,r)}function ho(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Qt,yn=Se.current,H(Se,e),H(Me,Me.current),!0}function sc(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=np(e,t,yn),r.__reactInternalMemoizedMergedChildContext=e,Q(Me),Q(Se),H(Se,e)):Q(Me),H(Me,n)}var ht=null,$o=!1,Ns=!1;function rp(e){ht===null?ht=[e]:ht.push(e)}function Lg(e){$o=!0,rp(e)}function Jt(){if(!Ns&&ht!==null){Ns=!0;var e=0,t=$;try{var n=ht;for($=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}ht=null,$o=!1}catch(i){throw ht!==null&&(ht=ht.slice(e+1)),Nf(xl,Jt),i}finally{$=t,Ns=!1}}return null}var _n=[],On=0,mo=null,go=0,We=[],He=0,vn=null,mt=1,gt="";function sn(e,t){_n[On++]=go,_n[On++]=mo,mo=e,go=t}function ip(e,t,n){We[He++]=mt,We[He++]=gt,We[He++]=vn,vn=e;var r=mt;e=gt;var i=32-rt(r)-1;r&=~(1<<i),n+=1;var o=32-rt(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,mt=1<<32-rt(t)+i|n<<i|r,gt=o+e}else mt=1<<o|n<<i|r,gt=e}function Nl(e){e.return!==null&&(sn(e,1),ip(e,1,0))}function El(e){for(;e===mo;)mo=_n[--On],_n[On]=null,go=_n[--On],_n[On]=null;for(;e===vn;)vn=We[--He],We[He]=null,gt=We[--He],We[He]=null,mt=We[--He],We[He]=null}var _e=null,ze=null,X=!1,tt=null;function op(e,t){var n=Ge(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ac(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,_e=e,ze=Ft(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,_e=e,ze=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=vn!==null?{id:mt,overflow:gt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ge(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,_e=e,ze=null,!0):!1;default:return!1}}function Ca(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Pa(e){if(X){var t=ze;if(t){var n=t;if(!ac(e,t)){if(Ca(e))throw Error(C(418));t=Ft(n.nextSibling);var r=_e;t&&ac(e,t)?op(r,n):(e.flags=e.flags&-4097|2,X=!1,_e=e)}}else{if(Ca(e))throw Error(C(418));e.flags=e.flags&-4097|2,X=!1,_e=e}}}function lc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;_e=e}function Ai(e){if(e!==_e)return!1;if(!X)return lc(e),X=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!xa(e.type,e.memoizedProps)),t&&(t=ze)){if(Ca(e))throw sp(),Error(C(418));for(;t;)op(e,t),t=Ft(t.nextSibling)}if(lc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ze=Ft(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ze=null}}else ze=_e?Ft(e.stateNode.nextSibling):null;return!0}function sp(){for(var e=ze;e;)e=Ft(e.nextSibling)}function tr(){ze=_e=null,X=!1}function Ml(e){tt===null?tt=[e]:tt.push(e)}var Rg=Tt.ReactCurrentBatchConfig;function gr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function Vi(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uc(e){var t=e._init;return t(e._payload)}function ap(e){function t(y,h){if(e){var m=y.deletions;m===null?(y.deletions=[h],y.flags|=16):m.push(h)}}function n(y,h){if(!e)return null;for(;h!==null;)t(y,h),h=h.sibling;return null}function r(y,h){for(y=new Map;h!==null;)h.key!==null?y.set(h.key,h):y.set(h.index,h),h=h.sibling;return y}function i(y,h){return y=Wt(y,h),y.index=0,y.sibling=null,y}function o(y,h,m){return y.index=m,e?(m=y.alternate,m!==null?(m=m.index,m<h?(y.flags|=2,h):m):(y.flags|=2,h)):(y.flags|=1048576,h)}function s(y){return e&&y.alternate===null&&(y.flags|=2),y}function a(y,h,m,w){return h===null||h.tag!==6?(h=Ds(m,y.mode,w),h.return=y,h):(h=i(h,m),h.return=y,h)}function l(y,h,m,w){var S=m.type;return S===An?c(y,h,m.props.children,w,m.key):h!==null&&(h.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Mt&&uc(S)===h.type)?(w=i(h,m.props),w.ref=gr(y,h,m),w.return=y,w):(w=eo(m.type,m.key,m.props,null,y.mode,w),w.ref=gr(y,h,m),w.return=y,w)}function u(y,h,m,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==m.containerInfo||h.stateNode.implementation!==m.implementation?(h=zs(m,y.mode,w),h.return=y,h):(h=i(h,m.children||[]),h.return=y,h)}function c(y,h,m,w,S){return h===null||h.tag!==7?(h=gn(m,y.mode,w,S),h.return=y,h):(h=i(h,m),h.return=y,h)}function d(y,h,m){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Ds(""+h,y.mode,m),h.return=y,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Si:return m=eo(h.type,h.key,h.props,null,y.mode,m),m.ref=gr(y,null,h),m.return=y,m;case Mn:return h=zs(h,y.mode,m),h.return=y,h;case Mt:var w=h._init;return d(y,w(h._payload),m)}if(Pr(h)||dr(h))return h=gn(h,y.mode,m,null),h.return=y,h;Vi(y,h)}return null}function p(y,h,m,w){var S=h!==null?h.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return S!==null?null:a(y,h,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case Si:return m.key===S?l(y,h,m,w):null;case Mn:return m.key===S?u(y,h,m,w):null;case Mt:return S=m._init,p(y,h,S(m._payload),w)}if(Pr(m)||dr(m))return S!==null?null:c(y,h,m,w,null);Vi(y,m)}return null}function g(y,h,m,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return y=y.get(m)||null,a(h,y,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Si:return y=y.get(w.key===null?m:w.key)||null,l(h,y,w,S);case Mn:return y=y.get(w.key===null?m:w.key)||null,u(h,y,w,S);case Mt:var T=w._init;return g(y,h,m,T(w._payload),S)}if(Pr(w)||dr(w))return y=y.get(m)||null,c(h,y,w,S,null);Vi(h,w)}return null}function v(y,h,m,w){for(var S=null,T=null,j=h,P=h=0,R=null;j!==null&&P<m.length;P++){j.index>P?(R=j,j=null):R=j.sibling;var V=p(y,j,m[P],w);if(V===null){j===null&&(j=R);break}e&&j&&V.alternate===null&&t(y,j),h=o(V,h,P),T===null?S=V:T.sibling=V,T=V,j=R}if(P===m.length)return n(y,j),X&&sn(y,P),S;if(j===null){for(;P<m.length;P++)j=d(y,m[P],w),j!==null&&(h=o(j,h,P),T===null?S=j:T.sibling=j,T=j);return X&&sn(y,P),S}for(j=r(y,j);P<m.length;P++)R=g(j,y,P,m[P],w),R!==null&&(e&&R.alternate!==null&&j.delete(R.key===null?P:R.key),h=o(R,h,P),T===null?S=R:T.sibling=R,T=R);return e&&j.forEach(function(ne){return t(y,ne)}),X&&sn(y,P),S}function x(y,h,m,w){var S=dr(m);if(typeof S!="function")throw Error(C(150));if(m=S.call(m),m==null)throw Error(C(151));for(var T=S=null,j=h,P=h=0,R=null,V=m.next();j!==null&&!V.done;P++,V=m.next()){j.index>P?(R=j,j=null):R=j.sibling;var ne=p(y,j,V.value,w);if(ne===null){j===null&&(j=R);break}e&&j&&ne.alternate===null&&t(y,j),h=o(ne,h,P),T===null?S=ne:T.sibling=ne,T=ne,j=R}if(V.done)return n(y,j),X&&sn(y,P),S;if(j===null){for(;!V.done;P++,V=m.next())V=d(y,V.value,w),V!==null&&(h=o(V,h,P),T===null?S=V:T.sibling=V,T=V);return X&&sn(y,P),S}for(j=r(y,j);!V.done;P++,V=m.next())V=g(j,y,P,V.value,w),V!==null&&(e&&V.alternate!==null&&j.delete(V.key===null?P:V.key),h=o(V,h,P),T===null?S=V:T.sibling=V,T=V);return e&&j.forEach(function(_){return t(y,_)}),X&&sn(y,P),S}function k(y,h,m,w){if(typeof m=="object"&&m!==null&&m.type===An&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case Si:e:{for(var S=m.key,T=h;T!==null;){if(T.key===S){if(S=m.type,S===An){if(T.tag===7){n(y,T.sibling),h=i(T,m.props.children),h.return=y,y=h;break e}}else if(T.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Mt&&uc(S)===T.type){n(y,T.sibling),h=i(T,m.props),h.ref=gr(y,T,m),h.return=y,y=h;break e}n(y,T);break}else t(y,T);T=T.sibling}m.type===An?(h=gn(m.props.children,y.mode,w,m.key),h.return=y,y=h):(w=eo(m.type,m.key,m.props,null,y.mode,w),w.ref=gr(y,h,m),w.return=y,y=w)}return s(y);case Mn:e:{for(T=m.key;h!==null;){if(h.key===T)if(h.tag===4&&h.stateNode.containerInfo===m.containerInfo&&h.stateNode.implementation===m.implementation){n(y,h.sibling),h=i(h,m.children||[]),h.return=y,y=h;break e}else{n(y,h);break}else t(y,h);h=h.sibling}h=zs(m,y.mode,w),h.return=y,y=h}return s(y);case Mt:return T=m._init,k(y,h,T(m._payload),w)}if(Pr(m))return v(y,h,m,w);if(dr(m))return x(y,h,m,w);Vi(y,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,h!==null&&h.tag===6?(n(y,h.sibling),h=i(h,m),h.return=y,y=h):(n(y,h),h=Ds(m,y.mode,w),h.return=y,y=h),s(y)):n(y,h)}return k}var nr=ap(!0),lp=ap(!1),yo=qt(null),vo=null,In=null,Al=null;function Vl(){Al=In=vo=null}function Ll(e){var t=yo.current;Q(yo),e._currentValue=t}function ja(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Zn(e,t){vo=e,Al=In=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function Qe(e){var t=e._currentValue;if(Al!==e)if(e={context:e,memoizedValue:t,next:null},In===null){if(vo===null)throw Error(C(308));In=e,vo.dependencies={lanes:0,firstContext:e}}else In=In.next=e;return t}var dn=null;function Rl(e){dn===null?dn=[e]:dn.push(e)}function up(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Rl(t)):(n.next=i.next,i.next=n),t.interleaved=n,Ct(e,r)}function Ct(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var At=!1;function Dl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function cp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function vt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Ct(e,n)}return i=r.interleaved,i===null?(t.next=t,Rl(r)):(t.next=i.next,i.next=t),r.interleaved=t,Ct(e,n)}function Qi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,wl(e,n)}}function cc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function xo(e,t,n,r){var i=e.updateQueue;At=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?o=u:s.next=u,s=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==s&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(o!==null){var d=i.baseState;s=0,c=u=l=null,a=o;do{var p=a.lane,g=a.eventTime;if((r&p)===p){c!==null&&(c=c.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(p=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){d=v.call(g,d,p);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,p=typeof v=="function"?v.call(g,d,p):v,p==null)break e;d=te({},d,p);break e;case 2:At=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[a]:p.push(a))}else g={eventTime:g,lane:p,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=g,l=d):c=c.next=g,s|=p;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;p=a,a=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(1);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);wn|=s,e.lanes=s,e.memoizedState=d}}function dc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var hi={},dt=qt(hi),Jr=qt(hi),ei=qt(hi);function fn(e){if(e===hi)throw Error(C(174));return e}function zl(e,t){switch(H(ei,t),H(Jr,e),H(dt,hi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:oa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=oa(t,e)}Q(dt),H(dt,t)}function rr(){Q(dt),Q(Jr),Q(ei)}function dp(e){fn(ei.current);var t=fn(dt.current),n=oa(t,e.type);t!==n&&(H(Jr,e),H(dt,n))}function _l(e){Jr.current===e&&(Q(dt),Q(Jr))}var q=qt(0);function wo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Es=[];function Ol(){for(var e=0;e<Es.length;e++)Es[e]._workInProgressVersionPrimary=null;Es.length=0}var Yi=Tt.ReactCurrentDispatcher,Ms=Tt.ReactCurrentBatchConfig,xn=0,ee=null,ue=null,fe=null,So=!1,Lr=!1,ti=0,Dg=0;function ye(){throw Error(C(321))}function Il(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ot(e[n],t[n]))return!1;return!0}function Fl(e,t,n,r,i,o){if(xn=o,ee=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yi.current=e===null||e.memoizedState===null?Ig:Fg,e=n(r,i),Lr){o=0;do{if(Lr=!1,ti=0,25<=o)throw Error(C(301));o+=1,fe=ue=null,t.updateQueue=null,Yi.current=Bg,e=n(r,i)}while(Lr)}if(Yi.current=ko,t=ue!==null&&ue.next!==null,xn=0,fe=ue=ee=null,So=!1,t)throw Error(C(300));return e}function Bl(){var e=ti!==0;return ti=0,e}function lt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return fe===null?ee.memoizedState=fe=e:fe=fe.next=e,fe}function Ye(){if(ue===null){var e=ee.alternate;e=e!==null?e.memoizedState:null}else e=ue.next;var t=fe===null?ee.memoizedState:fe.next;if(t!==null)fe=t,ue=e;else{if(e===null)throw Error(C(310));ue=e,e={memoizedState:ue.memoizedState,baseState:ue.baseState,baseQueue:ue.baseQueue,queue:ue.queue,next:null},fe===null?ee.memoizedState=fe=e:fe=fe.next=e}return fe}function ni(e,t){return typeof t=="function"?t(e):t}function As(e){var t=Ye(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=ue,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,l=null,u=o;do{var c=u.lane;if((xn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,s=r):l=l.next=d,ee.lanes|=c,wn|=c}u=u.next}while(u!==null&&u!==o);l===null?s=r:l.next=a,ot(r,t.memoizedState)||(Ee=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,ee.lanes|=o,wn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Vs(e){var t=Ye(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);ot(o,t.memoizedState)||(Ee=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function fp(){}function pp(e,t){var n=ee,r=Ye(),i=t(),o=!ot(r.memoizedState,i);if(o&&(r.memoizedState=i,Ee=!0),r=r.queue,Ul(gp.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||fe!==null&&fe.memoizedState.tag&1){if(n.flags|=2048,ri(9,mp.bind(null,n,r,i,t),void 0,null),pe===null)throw Error(C(349));xn&30||hp(n,t,i)}return i}function hp(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ee.updateQueue,t===null?(t={lastEffect:null,stores:null},ee.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function mp(e,t,n,r){t.value=n,t.getSnapshot=r,yp(t)&&vp(e)}function gp(e,t,n){return n(function(){yp(t)&&vp(e)})}function yp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ot(e,n)}catch{return!0}}function vp(e){var t=Ct(e,1);t!==null&&it(t,e,1,-1)}function fc(e){var t=lt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ni,lastRenderedState:e},t.queue=e,e=e.dispatch=Og.bind(null,ee,e),[t.memoizedState,e]}function ri(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ee.updateQueue,t===null?(t={lastEffect:null,stores:null},ee.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function xp(){return Ye().memoizedState}function Xi(e,t,n,r){var i=lt();ee.flags|=e,i.memoizedState=ri(1|t,n,void 0,r===void 0?null:r)}function Wo(e,t,n,r){var i=Ye();r=r===void 0?null:r;var o=void 0;if(ue!==null){var s=ue.memoizedState;if(o=s.destroy,r!==null&&Il(r,s.deps)){i.memoizedState=ri(t,n,o,r);return}}ee.flags|=e,i.memoizedState=ri(1|t,n,o,r)}function pc(e,t){return Xi(8390656,8,e,t)}function Ul(e,t){return Wo(2048,8,e,t)}function wp(e,t){return Wo(4,2,e,t)}function Sp(e,t){return Wo(4,4,e,t)}function kp(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Cp(e,t,n){return n=n!=null?n.concat([e]):null,Wo(4,4,kp.bind(null,t,e),n)}function $l(){}function Pp(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Il(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function jp(e,t){var n=Ye();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Il(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Tp(e,t,n){return xn&21?(ot(n,t)||(n=Af(),ee.lanes|=n,wn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function zg(e,t){var n=$;$=n!==0&&4>n?n:4,e(!0);var r=Ms.transition;Ms.transition={};try{e(!1),t()}finally{$=n,Ms.transition=r}}function bp(){return Ye().memoizedState}function _g(e,t,n){var r=$t(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Np(e))Ep(t,n);else if(n=up(e,t,n,r),n!==null){var i=je();it(n,e,r,i),Mp(n,t,r)}}function Og(e,t,n){var r=$t(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Np(e))Ep(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,ot(a,s)){var l=t.interleaved;l===null?(i.next=i,Rl(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=up(e,t,i,r),n!==null&&(i=je(),it(n,e,r,i),Mp(n,t,r))}}function Np(e){var t=e.alternate;return e===ee||t!==null&&t===ee}function Ep(e,t){Lr=So=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Mp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,wl(e,n)}}var ko={readContext:Qe,useCallback:ye,useContext:ye,useEffect:ye,useImperativeHandle:ye,useInsertionEffect:ye,useLayoutEffect:ye,useMemo:ye,useReducer:ye,useRef:ye,useState:ye,useDebugValue:ye,useDeferredValue:ye,useTransition:ye,useMutableSource:ye,useSyncExternalStore:ye,useId:ye,unstable_isNewReconciler:!1},Ig={readContext:Qe,useCallback:function(e,t){return lt().memoizedState=[e,t===void 0?null:t],e},useContext:Qe,useEffect:pc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Xi(4194308,4,kp.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Xi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Xi(4,2,e,t)},useMemo:function(e,t){var n=lt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=lt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=_g.bind(null,ee,e),[r.memoizedState,e]},useRef:function(e){var t=lt();return e={current:e},t.memoizedState=e},useState:fc,useDebugValue:$l,useDeferredValue:function(e){return lt().memoizedState=e},useTransition:function(){var e=fc(!1),t=e[0];return e=zg.bind(null,e[1]),lt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ee,i=lt();if(X){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),pe===null)throw Error(C(349));xn&30||hp(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,pc(gp.bind(null,r,o,e),[e]),r.flags|=2048,ri(9,mp.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=lt(),t=pe.identifierPrefix;if(X){var n=gt,r=mt;n=(r&~(1<<32-rt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ti++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Dg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Fg={readContext:Qe,useCallback:Pp,useContext:Qe,useEffect:Ul,useImperativeHandle:Cp,useInsertionEffect:wp,useLayoutEffect:Sp,useMemo:jp,useReducer:As,useRef:xp,useState:function(){return As(ni)},useDebugValue:$l,useDeferredValue:function(e){var t=Ye();return Tp(t,ue.memoizedState,e)},useTransition:function(){var e=As(ni)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:fp,useSyncExternalStore:pp,useId:bp,unstable_isNewReconciler:!1},Bg={readContext:Qe,useCallback:Pp,useContext:Qe,useEffect:Ul,useImperativeHandle:Cp,useInsertionEffect:wp,useLayoutEffect:Sp,useMemo:jp,useReducer:Vs,useRef:xp,useState:function(){return Vs(ni)},useDebugValue:$l,useDeferredValue:function(e){var t=Ye();return ue===null?t.memoizedState=e:Tp(t,ue.memoizedState,e)},useTransition:function(){var e=Vs(ni)[0],t=Ye().memoizedState;return[e,t]},useMutableSource:fp,useSyncExternalStore:pp,useId:bp,unstable_isNewReconciler:!1};function Je(e,t){if(e&&e.defaultProps){t=te({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ta(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:te({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ho={isMounted:function(e){return(e=e._reactInternals)?Cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=je(),i=$t(e),o=vt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Bt(e,o,i),t!==null&&(it(t,e,i,r),Qi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=je(),i=$t(e),o=vt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Bt(e,o,i),t!==null&&(it(t,e,i,r),Qi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=je(),r=$t(e),i=vt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Bt(e,i,r),t!==null&&(it(t,e,r,n),Qi(t,e,r))}};function hc(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Yr(n,r)||!Yr(i,o):!0}function Ap(e,t,n){var r=!1,i=Qt,o=t.contextType;return typeof o=="object"&&o!==null?o=Qe(o):(i=Ae(t)?yn:Se.current,r=t.contextTypes,o=(r=r!=null)?er(e,i):Qt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ho,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function mc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ho.enqueueReplaceState(t,t.state,null)}function ba(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Dl(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Qe(o):(o=Ae(t)?yn:Se.current,i.context=er(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Ta(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ho.enqueueReplaceState(i,i.state,null),xo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function ir(e,t){try{var n="",r=t;do n+=m0(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function Ls(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Na(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ug=typeof WeakMap=="function"?WeakMap:Map;function Vp(e,t,n){n=vt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Po||(Po=!0,Oa=r),Na(e,t)},n}function Lp(e,t,n){n=vt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Na(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Na(e,t),typeof r!="function"&&(Ut===null?Ut=new Set([this]):Ut.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function gc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ug;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ny.bind(null,e,t,n),t.then(e,e))}function yc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=vt(-1,1),t.tag=2,Bt(n,t,1))),n.lanes|=1),e)}var $g=Tt.ReactCurrentOwner,Ee=!1;function Pe(e,t,n,r){t.child=e===null?lp(t,null,n,r):nr(t,e.child,n,r)}function xc(e,t,n,r,i){n=n.render;var o=t.ref;return Zn(t,i),r=Fl(e,t,n,r,o,i),n=Bl(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Pt(e,t,i)):(X&&n&&Nl(t),t.flags|=1,Pe(e,t,r,i),t.child)}function wc(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Zl(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Rp(e,t,o,r,i)):(e=eo(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Yr,n(s,r)&&e.ref===t.ref)return Pt(e,t,i)}return t.flags|=1,e=Wt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Rp(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Yr(o,r)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,Pt(e,t,i)}return Ea(e,t,n,r,i)}function Dp(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},H(Bn,De),De|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,H(Bn,De),De|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,H(Bn,De),De|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,H(Bn,De),De|=r;return Pe(e,t,i,n),t.child}function zp(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ea(e,t,n,r,i){var o=Ae(n)?yn:Se.current;return o=er(t,o),Zn(t,i),n=Fl(e,t,n,r,o,i),r=Bl(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Pt(e,t,i)):(X&&r&&Nl(t),t.flags|=1,Pe(e,t,n,i),t.child)}function Sc(e,t,n,r,i){if(Ae(n)){var o=!0;ho(t)}else o=!1;if(Zn(t,i),t.stateNode===null)Zi(e,t),Ap(t,n,r),ba(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=Qe(u):(u=Ae(n)?yn:Se.current,u=er(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&mc(t,s,r,u),At=!1;var p=t.memoizedState;s.state=p,xo(t,r,s,i),l=t.memoizedState,a!==r||p!==l||Me.current||At?(typeof c=="function"&&(Ta(t,n,c,r),l=t.memoizedState),(a=At||hc(t,n,a,r,p,l,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,cp(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Je(t.type,a),s.props=u,d=t.pendingProps,p=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=Qe(l):(l=Ae(n)?yn:Se.current,l=er(t,l));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||p!==l)&&mc(t,s,r,l),At=!1,p=t.memoizedState,s.state=p,xo(t,r,s,i);var v=t.memoizedState;a!==d||p!==v||Me.current||At?(typeof g=="function"&&(Ta(t,n,g,r),v=t.memoizedState),(u=At||hc(t,n,u,r,p,v,l)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ma(e,t,n,r,o,i)}function Ma(e,t,n,r,i,o){zp(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&sc(t,n,!1),Pt(e,t,o);r=t.stateNode,$g.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=nr(t,e.child,null,o),t.child=nr(t,null,a,o)):Pe(e,t,a,o),t.memoizedState=r.state,i&&sc(t,n,!0),t.child}function _p(e){var t=e.stateNode;t.pendingContext?oc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&oc(e,t.context,!1),zl(e,t.containerInfo)}function kc(e,t,n,r,i){return tr(),Ml(i),t.flags|=256,Pe(e,t,n,r),t.child}var Aa={dehydrated:null,treeContext:null,retryLane:0};function Va(e){return{baseLanes:e,cachePool:null,transitions:null}}function Op(e,t,n){var r=t.pendingProps,i=q.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),H(q,i&1),e===null)return Pa(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Qo(s,r,0,null),e=gn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Va(n),t.memoizedState=Aa,e):Wl(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return Wg(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Wt(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=Wt(a,o):(o=gn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?Va(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=Aa,r}return o=e.child,e=o.sibling,r=Wt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Wl(e,t){return t=Qo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Li(e,t,n,r){return r!==null&&Ml(r),nr(t,e.child,null,n),e=Wl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wg(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=Ls(Error(C(422))),Li(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Qo({mode:"visible",children:r.children},i,0,null),o=gn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&nr(t,e.child,null,s),t.child.memoizedState=Va(s),t.memoizedState=Aa,o);if(!(t.mode&1))return Li(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(C(419)),r=Ls(o,r,void 0),Li(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ee||a){if(r=pe,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,Ct(e,i),it(r,e,i,-1))}return Xl(),r=Ls(Error(C(421))),Li(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=ry.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,ze=Ft(i.nextSibling),_e=t,X=!0,tt=null,e!==null&&(We[He++]=mt,We[He++]=gt,We[He++]=vn,mt=e.id,gt=e.overflow,vn=t),t=Wl(t,r.children),t.flags|=4096,t)}function Cc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ja(e.return,t,n)}function Rs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Ip(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(Pe(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Cc(e,n,t);else if(e.tag===19)Cc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(H(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&wo(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Rs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&wo(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Rs(t,!0,n,null,o);break;case"together":Rs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Pt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),wn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Wt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Wt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Hg(e,t,n){switch(t.tag){case 3:_p(t),tr();break;case 5:dp(t);break;case 1:Ae(t.type)&&ho(t);break;case 4:zl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;H(yo,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(H(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Op(e,t,n):(H(q,q.current&1),e=Pt(e,t,n),e!==null?e.sibling:null);H(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ip(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),H(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Dp(e,t,n)}return Pt(e,t,n)}var Fp,La,Bp,Up;Fp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};La=function(){};Bp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,fn(dt.current);var o=null;switch(n){case"input":i=ta(e,i),r=ta(e,r),o=[];break;case"select":i=te({},i,{value:void 0}),r=te({},r,{value:void 0}),o=[];break;case"textarea":i=ia(e,i),r=ia(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=fo)}sa(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ur.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(o||(o=[]),o.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ur.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&K("scroll",e),o||a===l||(o=[])):(o=o||[]).push(u,l))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Up=function(e,t,n,r){n!==r&&(t.flags|=4)};function yr(e,t){if(!X)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gg(e,t,n){var r=t.pendingProps;switch(El(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ve(t),null;case 1:return Ae(t.type)&&po(),ve(t),null;case 3:return r=t.stateNode,rr(),Q(Me),Q(Se),Ol(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ai(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,tt!==null&&(Ba(tt),tt=null))),La(e,t),ve(t),null;case 5:_l(t);var i=fn(ei.current);if(n=t.type,e!==null&&t.stateNode!=null)Bp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return ve(t),null}if(e=fn(dt.current),Ai(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[ut]=t,r[qr]=o,e=(t.mode&1)!==0,n){case"dialog":K("cancel",r),K("close",r);break;case"iframe":case"object":case"embed":K("load",r);break;case"video":case"audio":for(i=0;i<Tr.length;i++)K(Tr[i],r);break;case"source":K("error",r);break;case"img":case"image":case"link":K("error",r),K("load",r);break;case"details":K("toggle",r);break;case"input":Vu(r,o),K("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},K("invalid",r);break;case"textarea":Ru(r,o),K("invalid",r)}sa(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&Mi(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&Mi(r.textContent,a,e),i=["children",""+a]):Ur.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&K("scroll",r)}switch(n){case"input":ki(r),Lu(r,o,!0);break;case"textarea":ki(r),Du(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=fo)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[ut]=t,e[qr]=r,Fp(e,t,!1,!1),t.stateNode=e;e:{switch(s=aa(n,r),n){case"dialog":K("cancel",e),K("close",e),i=r;break;case"iframe":case"object":case"embed":K("load",e),i=r;break;case"video":case"audio":for(i=0;i<Tr.length;i++)K(Tr[i],e);i=r;break;case"source":K("error",e),i=r;break;case"img":case"image":case"link":K("error",e),K("load",e),i=r;break;case"details":K("toggle",e),i=r;break;case"input":Vu(e,r),i=ta(e,r),K("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=te({},r,{value:void 0}),K("invalid",e);break;case"textarea":Ru(e,r),i=ia(e,r),K("invalid",e);break;default:i=r}sa(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?xf(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&yf(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&$r(e,l):typeof l=="number"&&$r(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Ur.hasOwnProperty(o)?l!=null&&o==="onScroll"&&K("scroll",e):l!=null&&hl(e,o,l,s))}switch(n){case"input":ki(e),Lu(e,r,!1);break;case"textarea":ki(e),Du(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Kt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Kn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Kn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=fo)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ve(t),null;case 6:if(e&&t.stateNode!=null)Up(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=fn(ei.current),fn(dt.current),Ai(t)){if(r=t.stateNode,n=t.memoizedProps,r[ut]=t,(o=r.nodeValue!==n)&&(e=_e,e!==null))switch(e.tag){case 3:Mi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Mi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[ut]=t,t.stateNode=r}return ve(t),null;case 13:if(Q(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(X&&ze!==null&&t.mode&1&&!(t.flags&128))sp(),tr(),t.flags|=98560,o=!1;else if(o=Ai(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(C(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(C(317));o[ut]=t}else tr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ve(t),o=!1}else tt!==null&&(Ba(tt),tt=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?ce===0&&(ce=3):Xl())),t.updateQueue!==null&&(t.flags|=4),ve(t),null);case 4:return rr(),La(e,t),e===null&&Xr(t.stateNode.containerInfo),ve(t),null;case 10:return Ll(t.type._context),ve(t),null;case 17:return Ae(t.type)&&po(),ve(t),null;case 19:if(Q(q),o=t.memoizedState,o===null)return ve(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)yr(o,!1);else{if(ce!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=wo(e),s!==null){for(t.flags|=128,yr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return H(q,q.current&1|2),t.child}e=e.sibling}o.tail!==null&&se()>or&&(t.flags|=128,r=!0,yr(o,!1),t.lanes=4194304)}else{if(!r)if(e=wo(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),yr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!X)return ve(t),null}else 2*se()-o.renderingStartTime>or&&n!==1073741824&&(t.flags|=128,r=!0,yr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=se(),t.sibling=null,n=q.current,H(q,r?n&1|2:n&1),t):(ve(t),null);case 22:case 23:return Yl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?De&1073741824&&(ve(t),t.subtreeFlags&6&&(t.flags|=8192)):ve(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function Kg(e,t){switch(El(t),t.tag){case 1:return Ae(t.type)&&po(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return rr(),Q(Me),Q(Se),Ol(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return _l(t),null;case 13:if(Q(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));tr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Q(q),null;case 4:return rr(),null;case 10:return Ll(t.type._context),null;case 22:case 23:return Yl(),null;case 24:return null;default:return null}}var Ri=!1,we=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,E=null;function Fn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){re(e,t,r)}else n.current=null}function Ra(e,t,n){try{n()}catch(r){re(e,t,r)}}var Pc=!1;function Yg(e,t){if(ya=lo,e=Kf(),bl(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,a=-1,l=-1,u=0,c=0,d=e,p=null;t:for(;;){for(var g;d!==n||i!==0&&d.nodeType!==3||(a=s+i),d!==o||r!==0&&d.nodeType!==3||(l=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(g=d.firstChild)!==null;)p=d,d=g;for(;;){if(d===e)break t;if(p===n&&++u===i&&(a=s),p===o&&++c===r&&(l=s),(g=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=g}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(va={focusedElem:e,selectionRange:n},lo=!1,E=t;E!==null;)if(t=E,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,E=e;else for(;E!==null;){t=E;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,k=v.memoizedState,y=t.stateNode,h=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:Je(t.type,x),k);y.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){re(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,E=e;break}E=t.return}return v=Pc,Pc=!1,v}function Rr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&Ra(t,n,o)}i=i.next}while(i!==r)}}function Go(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Da(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function $p(e){var t=e.alternate;t!==null&&(e.alternate=null,$p(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[ut],delete t[qr],delete t[Sa],delete t[Ag],delete t[Vg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Wp(e){return e.tag===5||e.tag===3||e.tag===4}function jc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Wp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function za(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=fo));else if(r!==4&&(e=e.child,e!==null))for(za(e,t,n),e=e.sibling;e!==null;)za(e,t,n),e=e.sibling}function _a(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(_a(e,t,n),e=e.sibling;e!==null;)_a(e,t,n),e=e.sibling}var he=null,et=!1;function bt(e,t,n){for(n=n.child;n!==null;)Hp(e,t,n),n=n.sibling}function Hp(e,t,n){if(ct&&typeof ct.onCommitFiberUnmount=="function")try{ct.onCommitFiberUnmount(Oo,n)}catch{}switch(n.tag){case 5:we||Fn(n,t);case 6:var r=he,i=et;he=null,bt(e,t,n),he=r,et=i,he!==null&&(et?(e=he,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):he.removeChild(n.stateNode));break;case 18:he!==null&&(et?(e=he,n=n.stateNode,e.nodeType===8?bs(e.parentNode,n):e.nodeType===1&&bs(e,n),Kr(e)):bs(he,n.stateNode));break;case 4:r=he,i=et,he=n.stateNode.containerInfo,et=!0,bt(e,t,n),he=r,et=i;break;case 0:case 11:case 14:case 15:if(!we&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&Ra(n,t,s),i=i.next}while(i!==r)}bt(e,t,n);break;case 1:if(!we&&(Fn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){re(n,t,a)}bt(e,t,n);break;case 21:bt(e,t,n);break;case 22:n.mode&1?(we=(r=we)||n.memoizedState!==null,bt(e,t,n),we=r):bt(e,t,n);break;default:bt(e,t,n)}}function Tc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Qg),t.forEach(function(r){var i=iy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ze(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:he=a.stateNode,et=!1;break e;case 3:he=a.stateNode.containerInfo,et=!0;break e;case 4:he=a.stateNode.containerInfo,et=!0;break e}a=a.return}if(he===null)throw Error(C(160));Hp(o,s,i),he=null,et=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){re(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Gp(t,e),t=t.sibling}function Gp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ze(t,e),at(e),r&4){try{Rr(3,e,e.return),Go(3,e)}catch(x){re(e,e.return,x)}try{Rr(5,e,e.return)}catch(x){re(e,e.return,x)}}break;case 1:Ze(t,e),at(e),r&512&&n!==null&&Fn(n,n.return);break;case 5:if(Ze(t,e),at(e),r&512&&n!==null&&Fn(n,n.return),e.flags&32){var i=e.stateNode;try{$r(i,"")}catch(x){re(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&hf(i,o),aa(a,s);var u=aa(a,o);for(s=0;s<l.length;s+=2){var c=l[s],d=l[s+1];c==="style"?xf(i,d):c==="dangerouslySetInnerHTML"?yf(i,d):c==="children"?$r(i,d):hl(i,c,d,u)}switch(a){case"input":na(i,o);break;case"textarea":mf(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?Kn(i,!!o.multiple,g,!1):p!==!!o.multiple&&(o.defaultValue!=null?Kn(i,!!o.multiple,o.defaultValue,!0):Kn(i,!!o.multiple,o.multiple?[]:"",!1))}i[qr]=o}catch(x){re(e,e.return,x)}}break;case 6:if(Ze(t,e),at(e),r&4){if(e.stateNode===null)throw Error(C(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){re(e,e.return,x)}}break;case 3:if(Ze(t,e),at(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Kr(t.containerInfo)}catch(x){re(e,e.return,x)}break;case 4:Ze(t,e),at(e);break;case 13:Ze(t,e),at(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(Kl=se())),r&4&&Tc(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(we=(u=we)||c,Ze(t,e),we=u):Ze(t,e),at(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(E=e,c=e.child;c!==null;){for(d=E=c;E!==null;){switch(p=E,g=p.child,p.tag){case 0:case 11:case 14:case 15:Rr(4,p,p.return);break;case 1:Fn(p,p.return);var v=p.stateNode;if(typeof v.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){re(r,n,x)}}break;case 5:Fn(p,p.return);break;case 22:if(p.memoizedState!==null){Nc(d);continue}}g!==null?(g.return=p,E=g):Nc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=vf("display",s))}catch(x){re(e,e.return,x)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(x){re(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Ze(t,e),at(e),r&4&&Tc(e);break;case 21:break;default:Ze(t,e),at(e)}}function at(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Wp(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&($r(i,""),r.flags&=-33);var o=jc(e);_a(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=jc(e);za(e,a,s);break;default:throw Error(C(161))}}catch(l){re(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Xg(e,t,n){E=e,Kp(e)}function Kp(e,t,n){for(var r=(e.mode&1)!==0;E!==null;){var i=E,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Ri;if(!s){var a=i.alternate,l=a!==null&&a.memoizedState!==null||we;a=Ri;var u=we;if(Ri=s,(we=l)&&!u)for(E=i;E!==null;)s=E,l=s.child,s.tag===22&&s.memoizedState!==null?Ec(i):l!==null?(l.return=s,E=l):Ec(i);for(;o!==null;)E=o,Kp(o),o=o.sibling;E=i,Ri=a,we=u}bc(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,E=o):bc(e)}}function bc(e){for(;E!==null;){var t=E;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:we||Go(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!we)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Je(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&dc(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}dc(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&Kr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}we||t.flags&512&&Da(t)}catch(p){re(t,t.return,p)}}if(t===e){E=null;break}if(n=t.sibling,n!==null){n.return=t.return,E=n;break}E=t.return}}function Nc(e){for(;E!==null;){var t=E;if(t===e){E=null;break}var n=t.sibling;if(n!==null){n.return=t.return,E=n;break}E=t.return}}function Ec(e){for(;E!==null;){var t=E;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Go(4,t)}catch(l){re(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){re(t,i,l)}}var o=t.return;try{Da(t)}catch(l){re(t,o,l)}break;case 5:var s=t.return;try{Da(t)}catch(l){re(t,s,l)}}}catch(l){re(t,t.return,l)}if(t===e){E=null;break}var a=t.sibling;if(a!==null){a.return=t.return,E=a;break}E=t.return}}var Zg=Math.ceil,Co=Tt.ReactCurrentDispatcher,Hl=Tt.ReactCurrentOwner,Ke=Tt.ReactCurrentBatchConfig,B=0,pe=null,le=null,me=0,De=0,Bn=qt(0),ce=0,ii=null,wn=0,Ko=0,Gl=0,Dr=null,Ne=null,Kl=0,or=1/0,pt=null,Po=!1,Oa=null,Ut=null,Di=!1,zt=null,jo=0,zr=0,Ia=null,qi=-1,Ji=0;function je(){return B&6?se():qi!==-1?qi:qi=se()}function $t(e){return e.mode&1?B&2&&me!==0?me&-me:Rg.transition!==null?(Ji===0&&(Ji=Af()),Ji):(e=$,e!==0||(e=window.event,e=e===void 0?16:Of(e.type)),e):1}function it(e,t,n,r){if(50<zr)throw zr=0,Ia=null,Error(C(185));di(e,n,r),(!(B&2)||e!==pe)&&(e===pe&&(!(B&2)&&(Ko|=n),ce===4&&Rt(e,me)),Ve(e,r),n===1&&B===0&&!(t.mode&1)&&(or=se()+500,$o&&Jt()))}function Ve(e,t){var n=e.callbackNode;R0(e,t);var r=ao(e,e===pe?me:0);if(r===0)n!==null&&Ou(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ou(n),t===1)e.tag===0?Lg(Mc.bind(null,e)):rp(Mc.bind(null,e)),Eg(function(){!(B&6)&&Jt()}),n=null;else{switch(Vf(r)){case 1:n=xl;break;case 4:n=Ef;break;case 16:n=so;break;case 536870912:n=Mf;break;default:n=so}n=th(n,Qp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Qp(e,t){if(qi=-1,Ji=0,B&6)throw Error(C(327));var n=e.callbackNode;if(qn()&&e.callbackNode!==n)return null;var r=ao(e,e===pe?me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=To(e,r);else{t=r;var i=B;B|=2;var o=Xp();(pe!==e||me!==t)&&(pt=null,or=se()+500,mn(e,t));do try{ey();break}catch(a){Yp(e,a)}while(1);Vl(),Co.current=o,B=i,le!==null?t=0:(pe=null,me=0,t=ce)}if(t!==0){if(t===2&&(i=fa(e),i!==0&&(r=i,t=Fa(e,i))),t===1)throw n=ii,mn(e,0),Rt(e,r),Ve(e,se()),n;if(t===6)Rt(e,r);else{if(i=e.current.alternate,!(r&30)&&!qg(i)&&(t=To(e,r),t===2&&(o=fa(e),o!==0&&(r=o,t=Fa(e,o))),t===1))throw n=ii,mn(e,0),Rt(e,r),Ve(e,se()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:an(e,Ne,pt);break;case 3:if(Rt(e,r),(r&130023424)===r&&(t=Kl+500-se(),10<t)){if(ao(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){je(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=wa(an.bind(null,e,Ne,pt),t);break}an(e,Ne,pt);break;case 4:if(Rt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-rt(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=se()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Zg(r/1960))-r,10<r){e.timeoutHandle=wa(an.bind(null,e,Ne,pt),r);break}an(e,Ne,pt);break;case 5:an(e,Ne,pt);break;default:throw Error(C(329))}}}return Ve(e,se()),e.callbackNode===n?Qp.bind(null,e):null}function Fa(e,t){var n=Dr;return e.current.memoizedState.isDehydrated&&(mn(e,t).flags|=256),e=To(e,t),e!==2&&(t=Ne,Ne=n,t!==null&&Ba(t)),e}function Ba(e){Ne===null?Ne=e:Ne.push.apply(Ne,e)}function qg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!ot(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Rt(e,t){for(t&=~Gl,t&=~Ko,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-rt(t),r=1<<n;e[n]=-1,t&=~r}}function Mc(e){if(B&6)throw Error(C(327));qn();var t=ao(e,0);if(!(t&1))return Ve(e,se()),null;var n=To(e,t);if(e.tag!==0&&n===2){var r=fa(e);r!==0&&(t=r,n=Fa(e,r))}if(n===1)throw n=ii,mn(e,0),Rt(e,t),Ve(e,se()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,an(e,Ne,pt),Ve(e,se()),null}function Ql(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(or=se()+500,$o&&Jt())}}function Sn(e){zt!==null&&zt.tag===0&&!(B&6)&&qn();var t=B;B|=1;var n=Ke.transition,r=$;try{if(Ke.transition=null,$=1,e)return e()}finally{$=r,Ke.transition=n,B=t,!(B&6)&&Jt()}}function Yl(){De=Bn.current,Q(Bn)}function mn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ng(n)),le!==null)for(n=le.return;n!==null;){var r=n;switch(El(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&po();break;case 3:rr(),Q(Me),Q(Se),Ol();break;case 5:_l(r);break;case 4:rr();break;case 13:Q(q);break;case 19:Q(q);break;case 10:Ll(r.type._context);break;case 22:case 23:Yl()}n=n.return}if(pe=e,le=e=Wt(e.current,null),me=De=t,ce=0,ii=null,Gl=Ko=wn=0,Ne=Dr=null,dn!==null){for(t=0;t<dn.length;t++)if(n=dn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}dn=null}return e}function Yp(e,t){do{var n=le;try{if(Vl(),Yi.current=ko,So){for(var r=ee.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}So=!1}if(xn=0,fe=ue=ee=null,Lr=!1,ti=0,Hl.current=null,n===null||n.return===null){ce=1,ii=t,le=null;break}e:{var o=e,s=n.return,a=n,l=t;if(t=me,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var p=c.alternate;p?(c.updateQueue=p.updateQueue,c.memoizedState=p.memoizedState,c.lanes=p.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=yc(s);if(g!==null){g.flags&=-257,vc(g,s,a,o,t),g.mode&1&&gc(o,u,t),t=g,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){gc(o,u,t),Xl();break e}l=Error(C(426))}}else if(X&&a.mode&1){var k=yc(s);if(k!==null){!(k.flags&65536)&&(k.flags|=256),vc(k,s,a,o,t),Ml(ir(l,a));break e}}o=l=ir(l,a),ce!==4&&(ce=2),Dr===null?Dr=[o]:Dr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var y=Vp(o,l,t);cc(o,y);break e;case 1:a=l;var h=o.type,m=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Ut===null||!Ut.has(m)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=Lp(o,a,t);cc(o,w);break e}}o=o.return}while(o!==null)}qp(n)}catch(S){t=S,le===n&&n!==null&&(le=n=n.return);continue}break}while(1)}function Xp(){var e=Co.current;return Co.current=ko,e===null?ko:e}function Xl(){(ce===0||ce===3||ce===2)&&(ce=4),pe===null||!(wn&268435455)&&!(Ko&268435455)||Rt(pe,me)}function To(e,t){var n=B;B|=2;var r=Xp();(pe!==e||me!==t)&&(pt=null,mn(e,t));do try{Jg();break}catch(i){Yp(e,i)}while(1);if(Vl(),B=n,Co.current=r,le!==null)throw Error(C(261));return pe=null,me=0,ce}function Jg(){for(;le!==null;)Zp(le)}function ey(){for(;le!==null&&!j0();)Zp(le)}function Zp(e){var t=eh(e.alternate,e,De);e.memoizedProps=e.pendingProps,t===null?qp(e):le=t,Hl.current=null}function qp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Kg(n,t),n!==null){n.flags&=32767,le=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ce=6,le=null;return}}else if(n=Gg(n,t,De),n!==null){le=n;return}if(t=t.sibling,t!==null){le=t;return}le=t=e}while(t!==null);ce===0&&(ce=5)}function an(e,t,n){var r=$,i=Ke.transition;try{Ke.transition=null,$=1,ty(e,t,n,r)}finally{Ke.transition=i,$=r}return null}function ty(e,t,n,r){do qn();while(zt!==null);if(B&6)throw Error(C(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(D0(e,o),e===pe&&(le=pe=null,me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Di||(Di=!0,th(so,function(){return qn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ke.transition,Ke.transition=null;var s=$;$=1;var a=B;B|=4,Hl.current=null,Yg(e,n),Gp(n,e),Sg(va),lo=!!ya,va=ya=null,e.current=n,Xg(n),T0(),B=a,$=s,Ke.transition=o}else e.current=n;if(Di&&(Di=!1,zt=e,jo=i),o=e.pendingLanes,o===0&&(Ut=null),E0(n.stateNode),Ve(e,se()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Po)throw Po=!1,e=Oa,Oa=null,e;return jo&1&&e.tag!==0&&qn(),o=e.pendingLanes,o&1?e===Ia?zr++:(zr=0,Ia=e):zr=0,Jt(),null}function qn(){if(zt!==null){var e=Vf(jo),t=Ke.transition,n=$;try{if(Ke.transition=null,$=16>e?16:e,zt===null)var r=!1;else{if(e=zt,zt=null,jo=0,B&6)throw Error(C(331));var i=B;for(B|=4,E=e.current;E!==null;){var o=E,s=o.child;if(E.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(E=u;E!==null;){var c=E;switch(c.tag){case 0:case 11:case 15:Rr(8,c,o)}var d=c.child;if(d!==null)d.return=c,E=d;else for(;E!==null;){c=E;var p=c.sibling,g=c.return;if($p(c),c===u){E=null;break}if(p!==null){p.return=g,E=p;break}E=g}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var k=x.sibling;x.sibling=null,x=k}while(x!==null)}}E=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,E=s;else e:for(;E!==null;){if(o=E,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Rr(9,o,o.return)}var y=o.sibling;if(y!==null){y.return=o.return,E=y;break e}E=o.return}}var h=e.current;for(E=h;E!==null;){s=E;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,E=m;else e:for(s=h;E!==null;){if(a=E,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Go(9,a)}}catch(S){re(a,a.return,S)}if(a===s){E=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,E=w;break e}E=a.return}}if(B=i,Jt(),ct&&typeof ct.onPostCommitFiberRoot=="function")try{ct.onPostCommitFiberRoot(Oo,e)}catch{}r=!0}return r}finally{$=n,Ke.transition=t}}return!1}function Ac(e,t,n){t=ir(n,t),t=Vp(e,t,1),e=Bt(e,t,1),t=je(),e!==null&&(di(e,1,t),Ve(e,t))}function re(e,t,n){if(e.tag===3)Ac(e,e,n);else for(;t!==null;){if(t.tag===3){Ac(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ut===null||!Ut.has(r))){e=ir(n,e),e=Lp(t,e,1),t=Bt(t,e,1),e=je(),t!==null&&(di(t,1,e),Ve(t,e));break}}t=t.return}}function ny(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=je(),e.pingedLanes|=e.suspendedLanes&n,pe===e&&(me&n)===n&&(ce===4||ce===3&&(me&130023424)===me&&500>se()-Kl?mn(e,0):Gl|=n),Ve(e,t)}function Jp(e,t){t===0&&(e.mode&1?(t=ji,ji<<=1,!(ji&130023424)&&(ji=4194304)):t=1);var n=je();e=Ct(e,t),e!==null&&(di(e,t,n),Ve(e,n))}function ry(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Jp(e,n)}function iy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),Jp(e,n)}var eh;eh=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Me.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,Hg(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,X&&t.flags&1048576&&ip(t,go,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zi(e,t),e=t.pendingProps;var i=er(t,Se.current);Zn(t,n),i=Fl(null,t,r,e,i,n);var o=Bl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ae(r)?(o=!0,ho(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Dl(t),i.updater=Ho,t.stateNode=i,i._reactInternals=t,ba(t,r,e,n),t=Ma(null,t,r,!0,o,n)):(t.tag=0,X&&o&&Nl(t),Pe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=sy(r),e=Je(r,e),i){case 0:t=Ea(null,t,r,e,n);break e;case 1:t=Sc(null,t,r,e,n);break e;case 11:t=xc(null,t,r,e,n);break e;case 14:t=wc(null,t,r,Je(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Je(r,i),Ea(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Je(r,i),Sc(e,t,r,i,n);case 3:e:{if(_p(t),e===null)throw Error(C(387));r=t.pendingProps,o=t.memoizedState,i=o.element,cp(e,t),xo(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=ir(Error(C(423)),t),t=kc(e,t,r,n,i);break e}else if(r!==i){i=ir(Error(C(424)),t),t=kc(e,t,r,n,i);break e}else for(ze=Ft(t.stateNode.containerInfo.firstChild),_e=t,X=!0,tt=null,n=lp(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(tr(),r===i){t=Pt(e,t,n);break e}Pe(e,t,r,n)}t=t.child}return t;case 5:return dp(t),e===null&&Pa(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,xa(r,i)?s=null:o!==null&&xa(r,o)&&(t.flags|=32),zp(e,t),Pe(e,t,s,n),t.child;case 6:return e===null&&Pa(t),null;case 13:return Op(e,t,n);case 4:return zl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=nr(t,null,r,n):Pe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Je(r,i),xc(e,t,r,i,n);case 7:return Pe(e,t,t.pendingProps,n),t.child;case 8:return Pe(e,t,t.pendingProps.children,n),t.child;case 12:return Pe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,H(yo,r._currentValue),r._currentValue=s,o!==null)if(ot(o.value,s)){if(o.children===i.children&&!Me.current){t=Pt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=vt(-1,n&-n),l.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),ja(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(C(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),ja(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}Pe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Zn(t,n),i=Qe(i),r=r(i),t.flags|=1,Pe(e,t,r,n),t.child;case 14:return r=t.type,i=Je(r,t.pendingProps),i=Je(r.type,i),wc(e,t,r,i,n);case 15:return Rp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Je(r,i),Zi(e,t),t.tag=1,Ae(r)?(e=!0,ho(t)):e=!1,Zn(t,n),Ap(t,r,i),ba(t,r,i,n),Ma(null,t,r,!0,e,n);case 19:return Ip(e,t,n);case 22:return Dp(e,t,n)}throw Error(C(156,t.tag))};function th(e,t){return Nf(e,t)}function oy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ge(e,t,n,r){return new oy(e,t,n,r)}function Zl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function sy(e){if(typeof e=="function")return Zl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===gl)return 11;if(e===yl)return 14}return 2}function Wt(e,t){var n=e.alternate;return n===null?(n=Ge(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function eo(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Zl(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case An:return gn(n.children,i,o,t);case ml:s=8,i|=8;break;case Zs:return e=Ge(12,n,t,i|2),e.elementType=Zs,e.lanes=o,e;case qs:return e=Ge(13,n,t,i),e.elementType=qs,e.lanes=o,e;case Js:return e=Ge(19,n,t,i),e.elementType=Js,e.lanes=o,e;case df:return Qo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case uf:s=10;break e;case cf:s=9;break e;case gl:s=11;break e;case yl:s=14;break e;case Mt:s=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=Ge(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function gn(e,t,n,r){return e=Ge(7,e,r,t),e.lanes=n,e}function Qo(e,t,n,r){return e=Ge(22,e,r,t),e.elementType=df,e.lanes=n,e.stateNode={isHidden:!1},e}function Ds(e,t,n){return e=Ge(6,e,null,t),e.lanes=n,e}function zs(e,t,n){return t=Ge(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ay(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gs(0),this.expirationTimes=gs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gs(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ql(e,t,n,r,i,o,s,a,l){return e=new ay(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ge(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Dl(o),e}function ly(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Mn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function nh(e){if(!e)return Qt;e=e._reactInternals;e:{if(Cn(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ae(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Ae(n))return np(e,n,t)}return t}function rh(e,t,n,r,i,o,s,a,l){return e=ql(n,r,!0,e,i,o,s,a,l),e.context=nh(null),n=e.current,r=je(),i=$t(n),o=vt(r,i),o.callback=t??null,Bt(n,o,i),e.current.lanes=i,di(e,i,r),Ve(e,r),e}function Yo(e,t,n,r){var i=t.current,o=je(),s=$t(i);return n=nh(n),t.context===null?t.context=n:t.pendingContext=n,t=vt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Bt(i,t,s),e!==null&&(it(e,i,s,o),Qi(e,i,s)),s}function bo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Vc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Jl(e,t){Vc(e,t),(e=e.alternate)&&Vc(e,t)}function uy(){return null}var ih=typeof reportError=="function"?reportError:function(e){console.error(e)};function eu(e){this._internalRoot=e}Xo.prototype.render=eu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));Yo(e,t,null,null)};Xo.prototype.unmount=eu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sn(function(){Yo(null,e,null,null)}),t[kt]=null}};function Xo(e){this._internalRoot=e}Xo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Df();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&t!==0&&t<Lt[n].priority;n++);Lt.splice(n,0,e),n===0&&_f(e)}};function tu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Zo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Lc(){}function cy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=bo(s);o.call(u)}}var s=rh(t,r,e,0,null,!1,!1,"",Lc);return e._reactRootContainer=s,e[kt]=s.current,Xr(e.nodeType===8?e.parentNode:e),Sn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=bo(l);a.call(u)}}var l=ql(e,0,!1,null,null,!1,!1,"",Lc);return e._reactRootContainer=l,e[kt]=l.current,Xr(e.nodeType===8?e.parentNode:e),Sn(function(){Yo(t,l,n,r)}),l}function qo(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var l=bo(s);a.call(l)}}Yo(t,s,e,i)}else s=cy(n,t,e,i,r);return bo(s)}Lf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=jr(t.pendingLanes);n!==0&&(wl(t,n|1),Ve(t,se()),!(B&6)&&(or=se()+500,Jt()))}break;case 13:Sn(function(){var r=Ct(e,1);if(r!==null){var i=je();it(r,e,1,i)}}),Jl(e,1)}};Sl=function(e){if(e.tag===13){var t=Ct(e,134217728);if(t!==null){var n=je();it(t,e,134217728,n)}Jl(e,134217728)}};Rf=function(e){if(e.tag===13){var t=$t(e),n=Ct(e,t);if(n!==null){var r=je();it(n,e,t,r)}Jl(e,t)}};Df=function(){return $};zf=function(e,t){var n=$;try{return $=e,t()}finally{$=n}};ua=function(e,t,n){switch(t){case"input":if(na(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Uo(r);if(!i)throw Error(C(90));pf(r),na(r,i)}}}break;case"textarea":mf(e,n);break;case"select":t=n.value,t!=null&&Kn(e,!!n.multiple,t,!1)}};kf=Ql;Cf=Sn;var dy={usingClientEntryPoint:!1,Events:[pi,Dn,Uo,wf,Sf,Ql]},vr={findFiberByHostInstance:cn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},fy={bundleType:vr.bundleType,version:vr.version,rendererPackageName:vr.rendererPackageName,rendererConfig:vr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Tt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Tf(e),e===null?null:e.stateNode},findFiberByHostInstance:vr.findFiberByHostInstance||uy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var zi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!zi.isDisabled&&zi.supportsFiber)try{Oo=zi.inject(fy),ct=zi}catch{}}Fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=dy;Fe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!tu(t))throw Error(C(200));return ly(e,t,null,n)};Fe.createRoot=function(e,t){if(!tu(e))throw Error(C(299));var n=!1,r="",i=ih;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ql(e,1,!1,null,null,n,!1,r,i),e[kt]=t.current,Xr(e.nodeType===8?e.parentNode:e),new eu(t)};Fe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=Tf(t),e=e===null?null:e.stateNode,e};Fe.flushSync=function(e){return Sn(e)};Fe.hydrate=function(e,t,n){if(!Zo(t))throw Error(C(200));return qo(null,e,t,!0,n)};Fe.hydrateRoot=function(e,t,n){if(!tu(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=ih;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=rh(t,null,e,1,n??null,i,!1,o,s),e[kt]=t.current,Xr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Xo(t)};Fe.render=function(e,t,n){if(!Zo(t))throw Error(C(200));return qo(null,e,t,!1,n)};Fe.unmountComponentAtNode=function(e){if(!Zo(e))throw Error(C(40));return e._reactRootContainer?(Sn(function(){qo(null,null,e,!1,function(){e._reactRootContainer=null,e[kt]=null})}),!0):!1};Fe.unstable_batchedUpdates=Ql;Fe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Zo(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return qo(e,t,n,!1,r)};Fe.version="18.3.1-next-f1338f8080-20240426";function oh(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(oh)}catch(e){console.error(e)}}oh(),of.exports=Fe;var py=of.exports,Rc=py;Ys.createRoot=Rc.createRoot,Ys.hydrateRoot=Rc.hydrateRoot;const sh=b.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Jo=b.createContext({}),nu=b.createContext(null),es=typeof document<"u",hy=es?b.useLayoutEffect:b.useEffect,ah=b.createContext({strict:!1}),ru=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),my="framerAppearId",lh="data-"+ru(my);function gy(e,t,n,r){const{visualElement:i}=b.useContext(Jo),o=b.useContext(ah),s=b.useContext(nu),a=b.useContext(sh).reducedMotion,l=b.useRef();r=r||o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;b.useInsertionEffect(()=>{u&&u.update(n,s)});const c=b.useRef(!!(n[lh]&&!window.HandoffComplete));return hy(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),b.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function Un(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function yy(e,t,n){return b.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Un(n)&&(n.current=r))},[t])}function oi(e){return typeof e=="string"||Array.isArray(e)}function ts(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const iu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ou=["initial",...iu];function ns(e){return ts(e.animate)||ou.some(t=>oi(e[t]))}function uh(e){return!!(ns(e)||e.variants)}function vy(e,t){if(ns(e)){const{initial:n,animate:r}=e;return{initial:n===!1||oi(n)?n:void 0,animate:oi(r)?r:void 0}}return e.inherit!==!1?t:{}}function xy(e){const{initial:t,animate:n}=vy(e,b.useContext(Jo));return b.useMemo(()=>({initial:t,animate:n}),[Dc(t),Dc(n)])}function Dc(e){return Array.isArray(e)?e.join(" "):e}const zc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},si={};for(const e in zc)si[e]={isEnabled:t=>zc[e].some(n=>!!t[n])};function wy(e){for(const t in e)si[t]={...si[t],...e[t]}}const ch=b.createContext({}),dh=b.createContext({}),Sy=Symbol.for("motionComponentSymbol");function ky({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&wy(e);function o(a,l){let u;const c={...b.useContext(sh),...a,layoutId:Cy(a)},{isStatic:d}=c,p=xy(a),g=r(a,d);if(!d&&es){p.visualElement=gy(i,g,c,t);const v=b.useContext(dh),x=b.useContext(ah).strict;p.visualElement&&(u=p.visualElement.loadFeatures(c,x,e,v))}return b.createElement(Jo.Provider,{value:p},u&&p.visualElement?b.createElement(u,{visualElement:p.visualElement,...c}):null,n(i,a,yy(g,p.visualElement,l),g,d,p.visualElement))}const s=b.forwardRef(o);return s[Sy]=i,s}function Cy({layoutId:e}){const t=b.useContext(ch).id;return t&&e!==void 0?t+"-"+e:e}function Py(e){function t(r,i={}){return ky(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const jy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function su(e){return typeof e!="string"||e.includes("-")?!1:!!(jy.indexOf(e)>-1||/[A-Z]/.test(e))}const No={};function Ty(e){Object.assign(No,e)}const mi=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Pn=new Set(mi);function fh(e,{layout:t,layoutId:n}){return Pn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!No[e]||e==="opacity")}const Le=e=>!!(e&&e.getVelocity),by={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ny=mi.length;function Ey(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ny;s++){const a=mi[s];if(e[a]!==void 0){const l=by[a]||a;o+=`${l}(${e[a]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const ph=e=>t=>typeof t=="string"&&t.startsWith(e),hh=ph("--"),Ua=ph("var(--"),My=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Ay=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Yt=(e,t,n)=>Math.min(Math.max(n,e),t),jn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},_r={...jn,transform:e=>Yt(0,1,e)},_i={...jn,default:1},Or=e=>Math.round(e*1e5)/1e5,rs=/(-)?([\d]*\.?[\d])+/g,mh=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Vy=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function gi(e){return typeof e=="string"}const yi=e=>({test:t=>gi(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Et=yi("deg"),ft=yi("%"),L=yi("px"),Ly=yi("vh"),Ry=yi("vw"),_c={...ft,parse:e=>ft.parse(e)/100,transform:e=>ft.transform(e*100)},Oc={...jn,transform:Math.round},gh={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:Et,rotateX:Et,rotateY:Et,rotateZ:Et,scale:_i,scaleX:_i,scaleY:_i,scaleZ:_i,skew:Et,skewX:Et,skewY:Et,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:_r,originX:_c,originY:_c,originZ:L,zIndex:Oc,fillOpacity:_r,strokeOpacity:_r,numOctaves:Oc};function au(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const d in t){const p=t[d];if(hh(d)){o[d]=p;continue}const g=gh[d],v=Ay(p,g);if(Pn.has(d)){if(l=!0,s[d]=v,!c)continue;p!==(g.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,a[d]=v):i[d]=v}if(t.transform||(l||r?i.transform=Ey(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:d="50%",originY:p="50%",originZ:g=0}=a;i.transformOrigin=`${d} ${p} ${g}`}}const lu=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function yh(e,t,n){for(const r in t)!Le(t[r])&&!fh(r,n)&&(e[r]=t[r])}function Dy({transformTemplate:e},t,n){return b.useMemo(()=>{const r=lu();return au(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function zy(e,t,n){const r=e.style||{},i={};return yh(i,r,e),Object.assign(i,Dy(e,t,n)),e.transformValues?e.transformValues(i):i}function _y(e,t,n){const r={},i=zy(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const Oy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Eo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Oy.has(e)}let vh=e=>!Eo(e);function Iy(e){e&&(vh=t=>t.startsWith("on")?!Eo(t):e(t))}try{Iy(require("@emotion/is-prop-valid").default)}catch{}function Fy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(vh(i)||n===!0&&Eo(i)||!t&&!Eo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Ic(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function By(e,t,n){const r=Ic(t,e.x,e.width),i=Ic(n,e.y,e.height);return`${r} ${i}`}const Uy={offset:"stroke-dashoffset",array:"stroke-dasharray"},$y={offset:"strokeDashoffset",array:"strokeDasharray"};function Wy(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Uy:$y;e[o.offset]=L.transform(-r);const s=L.transform(t),a=L.transform(n);e[o.array]=`${s} ${a}`}function uu(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...u},c,d,p){if(au(e,u,c,p),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:v,dimensions:x}=e;g.transform&&(x&&(v.transform=g.transform),delete g.transform),x&&(i!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=By(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&Wy(g,s,a,l,!1)}const xh=()=>({...lu(),attrs:{}}),cu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Hy(e,t,n,r){const i=b.useMemo(()=>{const o=xh();return uu(o,t,{enableHardwareAcceleration:!1},cu(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};yh(o,e.style,e),i.style={...o,...i.style}}return i}function Gy(e=!1){return(n,r,i,{latestValues:o},s)=>{const l=(su(n)?Hy:_y)(r,o,s,n),c={...Fy(r,typeof n=="string",e),...l,ref:i},{children:d}=r,p=b.useMemo(()=>Le(d)?d.get():d,[d]);return b.createElement(n,{...c,children:p})}}function wh(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Sh=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function kh(e,t,n,r){wh(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Sh.has(i)?i:ru(i),t.attrs[i])}function du(e,t){const{style:n}=e,r={};for(const i in n)(Le(n[i])||t.style&&Le(t.style[i])||fh(i,e))&&(r[i]=n[i]);return r}function Ch(e,t){const n=du(e,t);for(const r in e)if(Le(e[r])||Le(t[r])){const i=mi.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function fu(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Ky(e){const t=b.useRef(null);return t.current===null&&(t.current=e()),t.current}const Mo=e=>Array.isArray(e),Qy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Yy=e=>Mo(e)?e[e.length-1]||0:e;function to(e){const t=Le(e)?e.get():e;return Qy(t)?t.toValue():t}function Xy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Zy(r,i,o,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const Ph=e=>(t,n)=>{const r=b.useContext(Jo),i=b.useContext(nu),o=()=>Xy(e,t,r,i);return n?o():Ky(o)};function Zy(e,t,n,r){const i={},o=r(e,{});for(const p in o)i[p]=to(o[p]);let{initial:s,animate:a}=e;const l=ns(e),u=uh(e);t&&u&&!l&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const d=c?a:s;return d&&typeof d!="boolean"&&!ts(d)&&(Array.isArray(d)?d:[d]).forEach(g=>{const v=fu(e,g);if(!v)return;const{transitionEnd:x,transition:k,...y}=v;for(const h in y){let m=y[h];if(Array.isArray(m)){const w=c?m.length-1:0;m=m[w]}m!==null&&(i[h]=m)}for(const h in x)i[h]=x[h]}),i}const ie=e=>e;class Fc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function qy(e){let t=new Fc,n=new Fc,r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const d=c&&i,p=d?t:n;return u&&s.add(l),p.add(l)&&d&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),s.delete(l)},process:l=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),s.has(c)&&(a.schedule(c),e())}i=!1,o&&(o=!1,a.process(l))}};return a}const Oi=["prepare","read","update","preRender","render","postRender"],Jy=40;function ev(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Oi.reduce((d,p)=>(d[p]=qy(()=>n=!0),d),{}),s=d=>o[d].process(i),a=()=>{const d=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(d-i.timestamp,Jy),1),i.timestamp=d,i.isProcessing=!0,Oi.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:Oi.reduce((d,p)=>{const g=o[p];return d[p]=(v,x=!1,k=!1)=>(n||l(),g.schedule(v,x,k)),d},{}),cancel:d=>Oi.forEach(p=>o[p].cancel(d)),state:i,steps:o}}const{schedule:Y,cancel:jt,state:xe,steps:_s}=ev(typeof requestAnimationFrame<"u"?requestAnimationFrame:ie,!0),tv={useVisualState:Ph({scrapeMotionValuesFromProps:Ch,createRenderState:xh,onMount:(e,t,{renderState:n,latestValues:r})=>{Y.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),Y.render(()=>{uu(n,r,{enableHardwareAcceleration:!1},cu(t.tagName),e.transformTemplate),kh(t,n)})}})},nv={useVisualState:Ph({scrapeMotionValuesFromProps:du,createRenderState:lu})};function rv(e,{forwardMotionProps:t=!1},n,r){return{...su(e)?tv:nv,preloadedFeatures:n,useRender:Gy(t),createVisualElement:r,Component:e}}function yt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const jh=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function is(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const iv=e=>t=>jh(t)&&e(t,is(t));function xt(e,t,n,r){return yt(e,t,iv(n),r)}const ov=(e,t)=>n=>t(e(n)),Ht=(...e)=>e.reduce(ov);function Th(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Bc=Th("dragHorizontal"),Uc=Th("dragVertical");function bh(e){let t=!1;if(e==="y")t=Uc();else if(e==="x")t=Bc();else{const n=Bc(),r=Uc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Nh(){const e=bh(!0);return e?(e(),!1):!0}class en{constructor(t){this.isMounted=!1,this.node=t}update(){}}function $c(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||Nh())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&Y.update(()=>a[r](o,s))};return xt(e.current,n,i,{passive:!e.getProps()[r]})}class sv extends en{mount(){this.unmount=Ht($c(this.node,!0),$c(this.node,!1))}unmount(){}}class av extends en{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ht(yt(this.node.current,"focus",()=>this.onFocus()),yt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Eh=(e,t)=>t?e===t?!0:Eh(e,t.parentElement):!1;function Os(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,is(n))}class lv extends en{constructor(){super(...arguments),this.removeStartListeners=ie,this.removeEndListeners=ie,this.removeAccessibleListeners=ie,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=xt(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps();Y.update(()=>{!d&&!Eh(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),s=xt(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ht(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||Os("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&Y.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=yt(this.node.current,"keyup",s),Os("down",(a,l)=>{this.startPress(a,l)})},n=yt(this.node.current,"keydown",t),r=()=>{this.isPressing&&Os("cancel",(o,s)=>this.cancelPress(o,s))},i=yt(this.node.current,"blur",r);this.removeAccessibleListeners=Ht(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&Y.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Nh()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&Y.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=xt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=yt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ht(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const $a=new WeakMap,Is=new WeakMap,uv=e=>{const t=$a.get(e.target);t&&t(e)},cv=e=>{e.forEach(uv)};function dv({root:e,...t}){const n=e||document;Is.has(n)||Is.set(n,{});const r=Is.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(cv,{root:e,...t})),r[i]}function fv(e,t,n){const r=dv(t);return $a.set(e,n),r.observe(e),()=>{$a.delete(e),r.unobserve(e)}}const pv={some:0,all:1};class hv extends en{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:pv[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),p=u?c:d;p&&p(l)};return fv(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(mv(t,n))&&this.startObserver()}unmount(){}}function mv({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const gv={inView:{Feature:hv},tap:{Feature:lv},focus:{Feature:av},hover:{Feature:sv}};function Mh(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function yv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function vv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function os(e,t,n){const r=e.getProps();return fu(r,t,n!==void 0?n:r.custom,yv(e),vv(e))}let xv=ie,pu=ie;const Gt=e=>e*1e3,wt=e=>e/1e3,wv={current:!1},Ah=e=>Array.isArray(e)&&typeof e[0]=="number";function Vh(e){return!!(!e||typeof e=="string"&&Lh[e]||Ah(e)||Array.isArray(e)&&e.every(Vh))}const br=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Lh={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:br([0,.65,.55,1]),circOut:br([.55,0,1,.45]),backIn:br([.31,.01,.66,-.59]),backOut:br([.33,1.53,.69,.99])};function Rh(e){if(e)return Ah(e)?br(e):Array.isArray(e)?e.map(Rh):Lh[e]}function Sv(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=Rh(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function kv(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Dh=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Cv=1e-7,Pv=12;function jv(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=Dh(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>Cv&&++a<Pv);return s}function vi(e,t,n,r){if(e===t&&n===r)return ie;const i=o=>jv(o,0,1,e,n);return o=>o===0||o===1?o:Dh(i(o),t,r)}const Tv=vi(.42,0,1,1),bv=vi(0,0,.58,1),zh=vi(.42,0,.58,1),Nv=e=>Array.isArray(e)&&typeof e[0]!="number",_h=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Oh=e=>t=>1-e(1-t),hu=e=>1-Math.sin(Math.acos(e)),Ih=Oh(hu),Ev=_h(hu),Fh=vi(.33,1.53,.69,.99),mu=Oh(Fh),Mv=_h(mu),Av=e=>(e*=2)<1?.5*mu(e):.5*(2-Math.pow(2,-10*(e-1))),Vv={linear:ie,easeIn:Tv,easeInOut:zh,easeOut:bv,circIn:hu,circInOut:Ev,circOut:Ih,backIn:mu,backInOut:Mv,backOut:Fh,anticipate:Av},Wc=e=>{if(Array.isArray(e)){pu(e.length===4);const[t,n,r,i]=e;return vi(t,n,r,i)}else if(typeof e=="string")return Vv[e];return e},gu=(e,t)=>n=>!!(gi(n)&&Vy.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Bh=(e,t,n)=>r=>{if(!gi(r))return r;const[i,o,s,a]=r.match(rs);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},Lv=e=>Yt(0,255,e),Fs={...jn,transform:e=>Math.round(Lv(e))},pn={test:gu("rgb","red"),parse:Bh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+Fs.transform(e)+", "+Fs.transform(t)+", "+Fs.transform(n)+", "+Or(_r.transform(r))+")"};function Rv(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Wa={test:gu("#"),parse:Rv,transform:pn.transform},$n={test:gu("hsl","hue"),parse:Bh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+ft.transform(Or(t))+", "+ft.transform(Or(n))+", "+Or(_r.transform(r))+")"},Ce={test:e=>pn.test(e)||Wa.test(e)||$n.test(e),parse:e=>pn.test(e)?pn.parse(e):$n.test(e)?$n.parse(e):Wa.parse(e),transform:e=>gi(e)?e:e.hasOwnProperty("red")?pn.transform(e):$n.transform(e)},J=(e,t,n)=>-n*e+n*t+e;function Bs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Dv({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=Bs(l,a,e+1/3),o=Bs(l,a,e),s=Bs(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const Us=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},zv=[Wa,pn,$n],_v=e=>zv.find(t=>t.test(e));function Hc(e){const t=_v(e);let n=t.parse(e);return t===$n&&(n=Dv(n)),n}const Uh=(e,t)=>{const n=Hc(e),r=Hc(t),i={...n};return o=>(i.red=Us(n.red,r.red,o),i.green=Us(n.green,r.green,o),i.blue=Us(n.blue,r.blue,o),i.alpha=J(n.alpha,r.alpha,o),pn.transform(i))};function Ov(e){var t,n;return isNaN(e)&&gi(e)&&(((t=e.match(rs))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(mh))===null||n===void 0?void 0:n.length)||0)>0}const $h={regex:My,countKey:"Vars",token:"${v}",parse:ie},Wh={regex:mh,countKey:"Colors",token:"${c}",parse:Ce.parse},Hh={regex:rs,countKey:"Numbers",token:"${n}",parse:jn.parse};function $s(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function Ao(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&$s(n,$h),$s(n,Wh),$s(n,Hh),n}function Gh(e){return Ao(e).values}function Kh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Ao(e),o=t.length;return s=>{let a=i;for(let l=0;l<o;l++)l<r?a=a.replace($h.token,s[l]):l<r+n?a=a.replace(Wh.token,Ce.transform(s[l])):a=a.replace(Hh.token,Or(s[l]));return a}}const Iv=e=>typeof e=="number"?0:e;function Fv(e){const t=Gh(e);return Kh(e)(t.map(Iv))}const Xt={test:Ov,parse:Gh,createTransformer:Kh,getAnimatableNone:Fv},Qh=(e,t)=>n=>`${n>0?t:e}`;function Yh(e,t){return typeof e=="number"?n=>J(e,t,n):Ce.test(e)?Uh(e,t):e.startsWith("var(")?Qh(e,t):Zh(e,t)}const Xh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>Yh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},Bv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Yh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},Zh=(e,t)=>{const n=Xt.createTransformer(t),r=Ao(e),i=Ao(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ht(Xh(r.values,i.values),n):Qh(e,t)},ai=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Gc=(e,t)=>n=>J(e,t,n);function Uv(e){return typeof e=="number"?Gc:typeof e=="string"?Ce.test(e)?Uh:Zh:Array.isArray(e)?Xh:typeof e=="object"?Bv:Gc}function $v(e,t,n){const r=[],i=n||Uv(e[0]),o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const l=Array.isArray(t)?t[s]||ie:t;a=Ht(l,a)}r.push(a)}return r}function qh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(pu(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=$v(t,r,i),a=s.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=ai(e[c],e[c+1],u);return s[c](d)};return n?u=>l(Yt(e[0],e[o-1],u)):l}function Wv(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=ai(0,t,r);e.push(J(n,1,i))}}function Hv(e){const t=[0];return Wv(t,e.length-1),t}function Gv(e,t){return e.map(n=>n*t)}function Kv(e,t){return e.map(()=>t||zh).splice(0,e.length-1)}function Vo({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Nv(r)?r.map(Wc):Wc(r),o={done:!1,value:t[0]},s=Gv(n&&n.length===t.length?n:Hv(t),e),a=qh(s,t,{ease:Array.isArray(i)?i:Kv(t,i)});return{calculatedDuration:e,next:l=>(o.value=a(l),o.done=l>=e,o)}}function Jh(e,t){return t?e*(1e3/t):0}const Qv=5;function em(e,t,n){const r=Math.max(t-Qv,0);return Jh(n-e(r),t-r)}const Ws=.001,Yv=.01,Kc=10,Xv=.05,Zv=1;function qv({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o;xv(e<=Gt(Kc));let s=1-t;s=Yt(Xv,Zv,s),e=Yt(Yv,Kc,wt(e)),s<1?(i=u=>{const c=u*s,d=c*e,p=c-n,g=Ha(u,s),v=Math.exp(-d);return Ws-p/g*v},o=u=>{const d=u*s*e,p=d*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-d),x=Ha(Math.pow(u,2),s);return(-i(u)+Ws>0?-1:1)*((p-g)*v)/x}):(i=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-Ws+c*d},o=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=e1(i,o,a);if(e=Gt(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const Jv=12;function e1(e,t,n){let r=n;for(let i=1;i<Jv;i++)r=r-e(r)/t(r);return r}function Ha(e,t){return e*Math.sqrt(1-t*t)}const t1=["duration","bounce"],n1=["stiffness","damping","mass"];function Qc(e,t){return t.some(n=>e[n]!==void 0)}function r1(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Qc(e,n1)&&Qc(e,t1)){const n=qv(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function tm({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:d,isResolvedFromDuration:p}=r1({...r,velocity:-wt(r.velocity||0)}),g=d||0,v=l/(2*Math.sqrt(a*u)),x=o-i,k=wt(Math.sqrt(a/u)),y=Math.abs(x)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let h;if(v<1){const m=Ha(k,v);h=w=>{const S=Math.exp(-v*k*w);return o-S*((g+v*k*x)/m*Math.sin(m*w)+x*Math.cos(m*w))}}else if(v===1)h=m=>o-Math.exp(-k*m)*(x+(g+k*x)*m);else{const m=k*Math.sqrt(v*v-1);h=w=>{const S=Math.exp(-v*k*w),T=Math.min(m*w,300);return o-S*((g+v*k*x)*Math.sinh(T)+m*x*Math.cosh(T))/m}}return{calculatedDuration:p&&c||null,next:m=>{const w=h(m);if(p)s.done=m>=c;else{let S=g;m!==0&&(v<1?S=em(h,m,w):S=0);const T=Math.abs(S)<=n,j=Math.abs(o-w)<=t;s.done=T&&j}return s.value=s.done?o:w,s}}}function Yc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],p={done:!1,value:d},g=P=>a!==void 0&&P<a||l!==void 0&&P>l,v=P=>a===void 0?l:l===void 0||Math.abs(a-P)<Math.abs(l-P)?a:l;let x=n*t;const k=d+x,y=s===void 0?k:s(k);y!==k&&(x=y-d);const h=P=>-x*Math.exp(-P/r),m=P=>y+h(P),w=P=>{const R=h(P),V=m(P);p.done=Math.abs(R)<=u,p.value=p.done?y:V};let S,T;const j=P=>{g(p.value)&&(S=P,T=tm({keyframes:[p.value,v(p.value)],velocity:em(m,P,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:P=>{let R=!1;return!T&&S===void 0&&(R=!0,w(P),j(P)),S!==void 0&&P>S?T.next(P-S):(!R&&w(P),p)}}}const i1=e=>{const t=({timestamp:n})=>e(n);return{start:()=>Y.update(t,!0),stop:()=>jt(t),now:()=>xe.isProcessing?xe.timestamp:performance.now()}},Xc=2e4;function Zc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Xc;)t+=n,r=e.next(t);return t>=Xc?1/0:t}const o1={decay:Yc,inertia:Yc,tween:Vo,keyframes:Vo,spring:tm};function Lo({autoplay:e=!0,delay:t=0,driver:n=i1,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...p}){let g=1,v=!1,x,k;const y=()=>{k=new Promise(A=>{x=A})};y();let h;const m=o1[i]||Vo;let w;m!==Vo&&typeof r[0]!="number"&&(w=qh([0,100],r,{clamp:!1}),r=[0,100]);const S=m({...p,keyframes:r});let T;a==="mirror"&&(T=m({...p,keyframes:[...r].reverse(),velocity:-(p.velocity||0)}));let j="idle",P=null,R=null,V=null;S.calculatedDuration===null&&o&&(S.calculatedDuration=Zc(S));const{calculatedDuration:ne}=S;let _=1/0,de=1/0;ne!==null&&(_=ne+s,de=_*(o+1)-s);let Z=0;const Xe=A=>{if(R===null)return;g>0&&(R=Math.min(R,A)),g<0&&(R=Math.min(A-de/g,R)),P!==null?Z=P:Z=Math.round(A-R)*g;const U=Z-t*(g>=0?1:-1),oe=g>=0?U<0:U>de;Z=Math.max(U,0),j==="finished"&&P===null&&(Z=de);let st=Z,bn=S;if(o){const cs=Math.min(Z,de)/_;let xi=Math.floor(cs),rn=cs%1;!rn&&cs>=1&&(rn=1),rn===1&&xi--,xi=Math.min(xi,o+1),!!(xi%2)&&(a==="reverse"?(rn=1-rn,s&&(rn-=s/_)):a==="mirror"&&(bn=T)),st=Yt(0,1,rn)*_}const Re=oe?{done:!1,value:r[0]}:bn.next(st);w&&(Re.value=w(Re.value));let{done:nn}=Re;!oe&&ne!==null&&(nn=g>=0?Z>=de:Z<=0);const Fm=P===null&&(j==="finished"||j==="running"&&nn);return d&&d(Re.value),Fm&&N(),Re},W=()=>{h&&h.stop(),h=void 0},ke=()=>{j="idle",W(),x(),y(),R=V=null},N=()=>{j="finished",c&&c(),W(),x()},M=()=>{if(v)return;h||(h=n(Xe));const A=h.now();l&&l(),P!==null?R=A-P:(!R||j==="finished")&&(R=A),j==="finished"&&y(),V=R,P=null,j="running",h.start()};e&&M();const z={then(A,U){return k.then(A,U)},get time(){return wt(Z)},set time(A){A=Gt(A),Z=A,P!==null||!h||g===0?P=A:R=h.now()-A/g},get duration(){const A=S.calculatedDuration===null?Zc(S):S.calculatedDuration;return wt(A)},get speed(){return g},set speed(A){A===g||!h||(g=A,z.time=wt(Z))},get state(){return j},play:M,pause:()=>{j="paused",P=Z},stop:()=>{v=!0,j!=="idle"&&(j="idle",u&&u(),ke())},cancel:()=>{V!==null&&Xe(V),ke()},complete:()=>{j="finished"},sample:A=>(R=0,Xe(A))};return z}function s1(e){let t;return()=>(t===void 0&&(t=e()),t)}const a1=s1(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),l1=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ii=10,u1=2e4,c1=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Vh(t.ease);function d1(e,t,{onUpdate:n,onComplete:r,...i}){if(!(a1()&&l1.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,a,l,u=!1;const c=()=>{l=new Promise(m=>{a=m})};c();let{keyframes:d,duration:p=300,ease:g,times:v}=i;if(c1(t,i)){const m=Lo({...i,repeat:0,delay:0});let w={done:!1,value:d[0]};const S=[];let T=0;for(;!w.done&&T<u1;)w=m.sample(T),S.push(w.value),T+=Ii;v=void 0,d=S,p=T-Ii,g="linear"}const x=Sv(e.owner.current,t,d,{...i,duration:p,ease:g,times:v}),k=()=>{u=!1,x.cancel()},y=()=>{u=!0,Y.update(k),a(),c()};return x.onfinish=()=>{u||(e.set(kv(d,i)),r&&r(),y())},{then(m,w){return l.then(m,w)},attachTimeline(m){return x.timeline=m,x.onfinish=null,ie},get time(){return wt(x.currentTime||0)},set time(m){x.currentTime=Gt(m)},get speed(){return x.playbackRate},set speed(m){x.playbackRate=m},get duration(){return wt(p)},play:()=>{s||(x.play(),jt(k))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:m}=x;if(m){const w=Lo({...i,autoplay:!1});e.setWithVelocity(w.sample(m-Ii).value,w.sample(m).value,Ii)}y()},complete:()=>{u||x.finish()},cancel:y}}function f1({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:ie,pause:ie,stop:ie,then:o=>(o(),Promise.resolve()),cancel:ie,complete:ie});return t?Lo({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const p1={type:"spring",stiffness:500,damping:25,restSpeed:10},h1=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),m1={type:"keyframes",duration:.8},g1={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},y1=(e,{keyframes:t})=>t.length>2?m1:Pn.has(e)?e.startsWith("scale")?h1(t[1]):p1:g1,Ga=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Xt.test(t)||t==="0")&&!t.startsWith("url(")),v1=new Set(["brightness","contrast","saturate","opacity"]);function x1(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(rs)||[];if(!r)return e;const i=n.replace(r,"");let o=v1.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const w1=/([a-z-]*)\(.*?\)/g,Ka={...Xt,getAnimatableNone:e=>{const t=e.match(w1);return t?t.map(x1).join(" "):e}},S1={...gh,color:Ce,backgroundColor:Ce,outlineColor:Ce,fill:Ce,stroke:Ce,borderColor:Ce,borderTopColor:Ce,borderRightColor:Ce,borderBottomColor:Ce,borderLeftColor:Ce,filter:Ka,WebkitFilter:Ka},yu=e=>S1[e];function nm(e,t){let n=yu(e);return n!==Ka&&(n=Xt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const rm=e=>/^0[^.\s]+$/.test(e);function k1(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||rm(e)}function C1(e,t,n,r){const i=Ga(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),k1(o[u])&&l.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(a=o[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];o[c]=nm(t,a)}return o}function P1({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function vu(e,t){return e[t]||e.default||e}const j1={skipAnimations:!1},xu=(e,t,n,r={})=>i=>{const o=vu(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a=a-Gt(s);const l=C1(t,e,n,o),u=l[0],c=l[l.length-1],d=Ga(e,u),p=Ga(e,c);let g={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:v=>{t.set(v),o.onUpdate&&o.onUpdate(v)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(P1(o)||(g={...g,...y1(e,g)}),g.duration&&(g.duration=Gt(g.duration)),g.repeatDelay&&(g.repeatDelay=Gt(g.repeatDelay)),!d||!p||wv.current||o.type===!1||j1.skipAnimations)return f1(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const v=d1(t,e,g);if(v)return v}return Lo(g)};function Ro(e){return!!(Le(e)&&e.add)}const im=e=>/^\-?\d*\.?\d+$/.test(e);function wu(e,t){e.indexOf(t)===-1&&e.push(t)}function Su(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class ku{constructor(){this.subscriptions=[]}add(t){return wu(this.subscriptions,t),()=>Su(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const T1=e=>!isNaN(parseFloat(e));class b1{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=xe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,Y.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>Y.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=T1(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new ku);const r=this.events[t].add(n);return t==="change"?()=>{r(),Y.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Jh(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function sr(e,t){return new b1(e,t)}const om=e=>t=>t.test(e),N1={test:e=>e==="auto",parse:e=>e},sm=[jn,L,ft,Et,Ry,Ly,N1],xr=e=>sm.find(om(e)),E1=[...sm,Ce,Xt],M1=e=>E1.find(om(e));function A1(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,sr(n))}function V1(e,t){const n=os(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const a=Yy(o[s]);A1(e,s,a)}}function L1(e,t,n){var r,i;const o=Object.keys(t).filter(a=>!e.hasValue(a)),s=o.length;if(s)for(let a=0;a<s;a++){const l=o[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),c!=null&&(typeof c=="string"&&(im(c)||rm(c))?c=parseFloat(c):!M1(c)&&Xt.test(u)&&(c=nm(l,u)),e.addValue(l,sr(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function R1(e,t){return t?(t[e]||t.default||t).from:void 0}function D1(e,t,n){const r={};for(const i in e){const o=R1(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function z1({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function _1(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function am(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in a){const p=e.getValue(d),g=a[d];if(!p||g===void 0||c&&z1(c,d))continue;const v={delay:n,elapsed:0,...vu(o||{},d)};if(window.HandoffAppearAnimations){const y=e.getProps()[lh];if(y){const h=window.HandoffAppearAnimations(y,d,p,Y);h!==null&&(v.elapsed=h,v.isHandoff=!0)}}let x=!v.isHandoff&&!_1(p,g);if(v.type==="spring"&&(p.getVelocity()||v.velocity)&&(x=!1),p.animation&&(x=!1),x)continue;p.start(xu(d,p,g,e.shouldReduceMotion&&Pn.has(d)?{type:!1}:v));const k=p.animation;Ro(l)&&(l.add(d),k.then(()=>l.remove(d))),u.push(k)}return s&&Promise.all(u).then(()=>{s&&V1(e,s)}),u}function Qa(e,t,n={}){const r=os(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(am(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:d}=i;return O1(e,t,u+l,c,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[o,s]:[s,o];return l().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function O1(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(I1).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Qa(u,t,{...o,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function I1(e,t){return e.sortNodePosition(t)}function F1(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Qa(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Qa(e,t,n);else{const i=typeof t=="function"?os(e,t,n.custom):t;r=Promise.all(am(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const B1=[...iu].reverse(),U1=iu.length;function $1(e){return t=>Promise.all(t.map(({animation:n,options:r})=>F1(e,n,r)))}function W1(e){let t=$1(e);const n=G1();let r=!0;const i=(l,u)=>{const c=os(e,u);if(c){const{transition:d,transitionEnd:p,...g}=c;l={...l,...g,...p}}return l};function o(l){t=l(e)}function s(l,u){const c=e.getProps(),d=e.getVariantContext(!0)||{},p=[],g=new Set;let v={},x=1/0;for(let y=0;y<U1;y++){const h=B1[y],m=n[h],w=c[h]!==void 0?c[h]:d[h],S=oi(w),T=h===u?m.isActive:null;T===!1&&(x=y);let j=w===d[h]&&w!==c[h]&&S;if(j&&r&&e.manuallyAnimateOnMount&&(j=!1),m.protectedKeys={...v},!m.isActive&&T===null||!w&&!m.prevProp||ts(w)||typeof w=="boolean")continue;let R=H1(m.prevProp,w)||h===u&&m.isActive&&!j&&S||y>x&&S,V=!1;const ne=Array.isArray(w)?w:[w];let _=ne.reduce(i,{});T===!1&&(_={});const{prevResolvedValues:de={}}=m,Z={...de,..._},Xe=W=>{R=!0,g.has(W)&&(V=!0,g.delete(W)),m.needsAnimating[W]=!0};for(const W in Z){const ke=_[W],N=de[W];if(v.hasOwnProperty(W))continue;let M=!1;Mo(ke)&&Mo(N)?M=!Mh(ke,N):M=ke!==N,M?ke!==void 0?Xe(W):g.add(W):ke!==void 0&&g.has(W)?Xe(W):m.protectedKeys[W]=!0}m.prevProp=w,m.prevResolvedValues=_,m.isActive&&(v={...v,..._}),r&&e.blockInitialAnimation&&(R=!1),R&&(!j||V)&&p.push(...ne.map(W=>({animation:W,options:{type:h,...l}})))}if(g.size){const y={};g.forEach(h=>{const m=e.getBaseTarget(h);m!==void 0&&(y[h]=m)}),p.push({animation:y})}let k=!!p.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(k=!1),r=!1,k?t(p):Promise.resolve()}function a(l,u,c){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(g=>{var v;return(v=g.animationState)===null||v===void 0?void 0:v.setActive(l,u)}),n[l].isActive=u;const p=s(c,l);for(const g in n)n[g].protectedKeys={};return p}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n}}function H1(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Mh(t,e):!1}function on(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function G1(){return{animate:on(!0),whileInView:on(),whileHover:on(),whileTap:on(),whileDrag:on(),whileFocus:on(),exit:on()}}class K1 extends en{constructor(t){super(t),t.animationState||(t.animationState=W1(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),ts(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Q1=0;class Y1 extends en{constructor(){super(...arguments),this.id=Q1++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const X1={animation:{Feature:K1},exit:{Feature:Y1}},qc=(e,t)=>Math.abs(e-t);function Z1(e,t){const n=qc(e.x,t.x),r=qc(e.y,t.y);return Math.sqrt(n**2+r**2)}class lm{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Gs(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,g=Z1(d.offset,{x:0,y:0})>=3;if(!p&&!g)return;const{point:v}=d,{timestamp:x}=xe;this.history.push({...v,timestamp:x});const{onStart:k,onMove:y}=this.handlers;p||(k&&k(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,d)},this.handlePointerMove=(d,p)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Hs(p,this.transformPagePoint),Y.update(this.updatePoint,!0)},this.handlePointerUp=(d,p)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const k=Gs(d.type==="pointercancel"?this.lastMoveEventInfo:Hs(p,this.transformPagePoint),this.history);this.startEvent&&g&&g(d,k),v&&v(d,k)},!jh(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=is(t),a=Hs(s,this.transformPagePoint),{point:l}=a,{timestamp:u}=xe;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Gs(a,this.history)),this.removeListeners=Ht(xt(this.contextWindow,"pointermove",this.handlePointerMove),xt(this.contextWindow,"pointerup",this.handlePointerUp),xt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),jt(this.updatePoint)}}function Hs(e,t){return t?{point:t(e.point)}:e}function Jc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Gs({point:e},t){return{point:e,delta:Jc(e,um(t)),offset:Jc(e,q1(t)),velocity:J1(t,.1)}}function q1(e){return e[0]}function um(e){return e[e.length-1]}function J1(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=um(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Gt(t)));)n--;if(!r)return{x:0,y:0};const o=wt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Ie(e){return e.max-e.min}function Ya(e,t=0,n=.01){return Math.abs(e-t)<=n}function ed(e,t,n,r=.5){e.origin=r,e.originPoint=J(t.min,t.max,e.origin),e.scale=Ie(n)/Ie(t),(Ya(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=J(n.min,n.max,e.origin)-e.originPoint,(Ya(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Ir(e,t,n,r){ed(e.x,t.x,n.x,r?r.originX:void 0),ed(e.y,t.y,n.y,r?r.originY:void 0)}function td(e,t,n){e.min=n.min+t.min,e.max=e.min+Ie(t)}function ex(e,t,n){td(e.x,t.x,n.x),td(e.y,t.y,n.y)}function nd(e,t,n){e.min=t.min-n.min,e.max=e.min+Ie(t)}function Fr(e,t,n){nd(e.x,t.x,n.x),nd(e.y,t.y,n.y)}function tx(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?J(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?J(n,e,r.max):Math.min(e,n)),e}function rd(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function nx(e,{top:t,left:n,bottom:r,right:i}){return{x:rd(e.x,n,i),y:rd(e.y,t,r)}}function id(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function rx(e,t){return{x:id(e.x,t.x),y:id(e.y,t.y)}}function ix(e,t){let n=.5;const r=Ie(e),i=Ie(t);return i>r?n=ai(t.min,t.max-r,e.min):r>i&&(n=ai(e.min,e.max-i,t.min)),Yt(0,1,n)}function ox(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Xa=.35;function sx(e=Xa){return e===!1?e=0:e===!0&&(e=Xa),{x:od(e,"left","right"),y:od(e,"top","bottom")}}function od(e,t,n){return{min:sd(e,t),max:sd(e,n)}}function sd(e,t){return typeof e=="number"?e:e[t]||0}const ad=()=>({translate:0,scale:1,origin:0,originPoint:0}),Wn=()=>({x:ad(),y:ad()}),ld=()=>({min:0,max:0}),ae=()=>({x:ld(),y:ld()});function $e(e){return[e("x"),e("y")]}function cm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function ax({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function lx(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ks(e){return e===void 0||e===1}function Za({scale:e,scaleX:t,scaleY:n}){return!Ks(e)||!Ks(t)||!Ks(n)}function ln(e){return Za(e)||dm(e)||e.z||e.rotate||e.rotateX||e.rotateY}function dm(e){return ud(e.x)||ud(e.y)}function ud(e){return e&&e!=="0%"}function Do(e,t,n){const r=e-n,i=t*r;return n+i}function cd(e,t,n,r,i){return i!==void 0&&(e=Do(e,i,r)),Do(e,n,r)+t}function qa(e,t=0,n=1,r,i){e.min=cd(e.min,t,n,r,i),e.max=cd(e.max,t,n,r,i)}function fm(e,{x:t,y:n}){qa(e.x,t.translate,t.scale,t.originPoint),qa(e.y,n.translate,n.scale,n.originPoint)}function ux(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let a=0;a<i;a++){o=n[a],s=o.projectionDelta;const l=o.instance;l&&l.style&&l.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Hn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,fm(e,s)),r&&ln(o.latestValues)&&Hn(e,o.latestValues))}t.x=dd(t.x),t.y=dd(t.y)}function dd(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Vt(e,t){e.min=e.min+t,e.max=e.max+t}function fd(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=J(e.min,e.max,o);qa(e,t[n],t[r],s,t.scale)}const cx=["x","scaleX","originX"],dx=["y","scaleY","originY"];function Hn(e,t){fd(e.x,t,cx),fd(e.y,t,dx)}function pm(e,t){return cm(lx(e.getBoundingClientRect(),t))}function fx(e,t,n){const r=pm(e,n),{scroll:i}=t;return i&&(Vt(r.x,i.offset.x),Vt(r.y,i.offset.y)),r}const hm=({current:e})=>e?e.ownerDocument.defaultView:null,px=new WeakMap;class hx{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ae(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(is(c,"page").point)},o=(c,d)=>{const{drag:p,dragPropagation:g,onDragStart:v}=this.getProps();if(p&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=bh(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),$e(k=>{let y=this.getAxisMotionValue(k).get()||0;if(ft.test(y)){const{projection:h}=this.visualElement;if(h&&h.layout){const m=h.layout.layoutBox[k];m&&(y=Ie(m)*(parseFloat(y)/100))}}this.originPoint[k]=y}),v&&Y.update(()=>v(c,d),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(c,d)=>{const{dragPropagation:p,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!p&&!this.openGlobalLock)return;const{offset:k}=d;if(g&&this.currentDirection===null){this.currentDirection=mx(k),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,k),this.updateAxis("y",d.point,k),this.visualElement.render(),x&&x(c,d)},a=(c,d)=>this.stop(c,d),l=()=>$e(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new lm(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:hm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&Y.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Fi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=tx(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&Un(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=nx(i.layoutBox,n):this.constraints=!1,this.elastic=sx(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&$e(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=ox(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Un(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=fx(r,i.root,this.visualElement.getTransformPagePoint());let s=rx(i.layout.layoutBox,o);if(n){const a=n(ax(s));this.hasMutatedConstraints=!!a,a&&(s=cm(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=$e(c=>{if(!Fi(c,n,this.currentDirection))return;let d=l&&l[c]||{};s&&(d={min:0,max:0});const p=i?200:1e6,g=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:p,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...d};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(xu(t,r,0,n))}stopAnimation(){$e(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){$e(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){$e(n=>{const{drag:r}=this.getProps();if(!Fi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-J(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Un(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};$e(s=>{const a=this.getAxisMotionValue(s);if(a){const l=a.get();i[s]=ix({min:l,max:l},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),$e(s=>{if(!Fi(s,t,null))return;const a=this.getAxisMotionValue(s),{min:l,max:u}=this.constraints[s];a.set(J(l,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;px.set(this.visualElement,this);const t=this.visualElement.current,n=xt(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Un(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=yt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&($e(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Xa,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function Fi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function mx(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class gx extends en{constructor(t){super(t),this.removeGroupControls=ie,this.removeListeners=ie,this.controls=new hx(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ie}unmount(){this.removeGroupControls(),this.removeListeners()}}const pd=e=>(t,n)=>{e&&Y.update(()=>e(t,n))};class yx extends en{constructor(){super(...arguments),this.removePointerDownListener=ie}onPointerDown(t){this.session=new lm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:hm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:pd(t),onStart:pd(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&Y.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=xt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function vx(){const e=b.useContext(nu);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=b.useId();return b.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const no={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function hd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const wr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=hd(e,t.target.x),r=hd(e,t.target.y);return`${n}% ${r}%`}},xx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Xt.parse(e);if(i.length>5)return r;const o=Xt.createTransformer(e),s=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+s]/=a,i[1+s]/=l;const u=J(a,l,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class wx extends Br.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ty(Sx),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),no.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||Y.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function mm(e){const[t,n]=vx(),r=b.useContext(ch);return Br.createElement(wx,{...e,layoutGroup:r,switchLayoutGroup:b.useContext(dh),isPresent:t,safeToRemove:n})}const Sx={borderRadius:{...wr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:wr,borderTopRightRadius:wr,borderBottomLeftRadius:wr,borderBottomRightRadius:wr,boxShadow:xx},gm=["TopLeft","TopRight","BottomLeft","BottomRight"],kx=gm.length,md=e=>typeof e=="string"?parseFloat(e):e,gd=e=>typeof e=="number"||L.test(e);function Cx(e,t,n,r,i,o){i?(e.opacity=J(0,n.opacity!==void 0?n.opacity:1,Px(r)),e.opacityExit=J(t.opacity!==void 0?t.opacity:1,0,jx(r))):o&&(e.opacity=J(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<kx;s++){const a=`border${gm[s]}Radius`;let l=yd(t,a),u=yd(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||gd(l)===gd(u)?(e[a]=Math.max(J(md(l),md(u),r),0),(ft.test(u)||ft.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=J(t.rotate||0,n.rotate||0,r))}function yd(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Px=ym(0,.5,Ih),jx=ym(.5,.95,ie);function ym(e,t,n){return r=>r<e?0:r>t?1:n(ai(e,t,r))}function vd(e,t){e.min=t.min,e.max=t.max}function Ue(e,t){vd(e.x,t.x),vd(e.y,t.y)}function xd(e,t,n,r,i){return e-=t,e=Do(e,1/n,r),i!==void 0&&(e=Do(e,1/i,r)),e}function Tx(e,t=0,n=1,r=.5,i,o=e,s=e){if(ft.test(t)&&(t=parseFloat(t),t=J(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=J(o.min,o.max,r);e===o&&(a-=t),e.min=xd(e.min,t,n,a,i),e.max=xd(e.max,t,n,a,i)}function wd(e,t,[n,r,i],o,s){Tx(e,t[n],t[r],t[i],t.scale,o,s)}const bx=["x","scaleX","originX"],Nx=["y","scaleY","originY"];function Sd(e,t,n,r){wd(e.x,t,bx,n?n.x:void 0,r?r.x:void 0),wd(e.y,t,Nx,n?n.y:void 0,r?r.y:void 0)}function kd(e){return e.translate===0&&e.scale===1}function vm(e){return kd(e.x)&&kd(e.y)}function Ex(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function xm(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Cd(e){return Ie(e.x)/Ie(e.y)}class Mx{constructor(){this.members=[]}add(t){wu(this.members,t),t.scheduleRender()}remove(t){if(Su(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Pd(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const Ax=(e,t)=>e.depth-t.depth;class Vx{constructor(){this.children=[],this.isDirty=!1}add(t){wu(this.children,t),this.isDirty=!0}remove(t){Su(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ax),this.isDirty=!1,this.children.forEach(t)}}function Lx(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(jt(r),e(o-t))};return Y.read(r,!0),()=>jt(r)}function Rx(e){window.MotionDebug&&window.MotionDebug.record(e)}function Dx(e){return e instanceof SVGElement&&e.tagName!=="svg"}function zx(e,t,n){const r=Le(e)?e:sr(e);return r.start(xu("",r,t,n)),r.animation}const jd=["","X","Y","Z"],_x={visibility:"hidden"},Td=1e3;let Ox=0;const un={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function wm({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},a=t==null?void 0:t()){this.id=Ox++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,un.totalNodes=un.resolvedTargetDeltas=un.recalculatedProjection=0,this.nodes.forEach(Bx),this.nodes.forEach(Gx),this.nodes.forEach(Kx),this.nodes.forEach(Ux),Rx(un)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Vx)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new ku),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Dx(s),this.instance=s;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=Lx(p,250),no.hasAnimatedSinceResize&&(no.hasAnimatedSinceResize=!1,this.nodes.forEach(Nd))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:p,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||qx,{onLayoutAnimationStart:k,onLayoutAnimationComplete:y}=c.getProps(),h=!this.targetLayout||!xm(this.targetLayout,v)||g,m=!p&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||p&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,m);const w={...vu(x,"layout"),onPlay:k,onComplete:y};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else p||Nd(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,jt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Qx),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(bd);return}this.isUpdating||this.nodes.forEach(Wx),this.isUpdating=!1,this.nodes.forEach(Hx),this.nodes.forEach(Ix),this.nodes.forEach(Fx),this.clearAllSnapshots();const a=performance.now();xe.delta=Yt(0,1e3/60,a-xe.timestamp),xe.timestamp=a,xe.isProcessing=!0,_s.update.process(xe),_s.preRender.process(xe),_s.render.process(xe),xe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach($x),this.sharedNodes.forEach(Yx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Y.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Y.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ae(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!vm(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(a||ln(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),Jx(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ae();const a=s.measureViewportBox(),{scroll:l}=this.root;return l&&(Vt(a.x,l.offset.x),Vt(a.y,l.offset.y)),a}removeElementScroll(s){const a=ae();Ue(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:d}=u;if(u!==this.root&&c&&d.layoutScroll){if(c.isRoot){Ue(a,s);const{scroll:p}=this.root;p&&(Vt(a.x,-p.offset.x),Vt(a.y,-p.offset.y))}Vt(a.x,c.offset.x),Vt(a.y,c.offset.y)}}return a}applyTransform(s,a=!1){const l=ae();Ue(l,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Hn(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),ln(c.latestValues)&&Hn(l,c.latestValues)}return ln(this.latestValues)&&Hn(l,this.latestValues),l}removeTransform(s){const a=ae();Ue(a,s);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!ln(u.latestValues))continue;Za(u.latestValues)&&u.updateSnapshot();const c=ae(),d=u.measurePageBox();Ue(c,d),Sd(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return ln(this.latestValues)&&Sd(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==xe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:p}=this.options;if(!(!this.layout||!(d||p))){if(this.resolvedRelativeTargetAt=xe.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),Fr(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Ue(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ae(),this.targetWithTransforms=ae()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),ex(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ue(this.target,this.layout.layoutBox),fm(this.target,this.targetDelta)):Ue(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ae(),this.relativeTargetOrigin=ae(),Fr(this.relativeTargetOrigin,this.target,g.target),Ue(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}un.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Za(this.parent.latestValues)||dm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===xe.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Ue(this.layoutCorrected,this.layout.layoutBox);const p=this.treeScale.x,g=this.treeScale.y;ux(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:v}=a;if(!v){this.projectionTransform&&(this.projectionDelta=Wn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Wn(),this.projectionDeltaWithTransform=Wn());const x=this.projectionTransform;Ir(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=Pd(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==p||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),un.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=Wn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=ae(),g=l?l.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,k=this.getStack(),y=!k||k.members.length<=1,h=!!(x&&!y&&this.options.crossfade===!0&&!this.path.some(Zx));this.animationProgress=0;let m;this.mixTargetDelta=w=>{const S=w/1e3;Ed(d.x,s.x,S),Ed(d.y,s.y,S),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Fr(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Xx(this.relativeTarget,this.relativeTargetOrigin,p,S),m&&Ex(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=ae()),Ue(m,this.relativeTarget)),x&&(this.animationValues=c,Cx(c,u,this.latestValues,S,h,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(jt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Y.update(()=>{no.hasAnimatedSinceResize=!0,this.currentAnimation=zx(0,Td,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Td),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=s;if(!(!a||!l||!u)){if(this!==s&&this.layout&&u&&Sm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||ae();const d=Ie(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+d;const p=Ie(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+p}Ue(a,l),Hn(a,c),Ir(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Mx),this.sharedNodes.get(s).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<jd.length;c++){const d="rotate"+jd[c];l[d]&&(u[d]=l[d],s.setStaticValue(d,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return _x;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=to(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=to(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!ln(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const p=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=Pd(this.projectionDeltaWithTransform,this.treeScale,p),c&&(u.transform=c(p,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=p.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:u.opacity=d===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const x in No){if(p[x]===void 0)continue;const{correct:k,applyTo:y}=No[x],h=u.transform==="none"?p[x]:k(p[x],d);if(y){const m=y.length;for(let w=0;w<m;w++)u[y[w]]=h}else u[x]=h}return this.options.layoutId&&(u.pointerEvents=d===this?to(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(bd),this.root.sharedNodes.clear()}}}function Ix(e){e.updateLayout()}function Fx(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?$e(d=>{const p=s?n.measuredBox[d]:n.layoutBox[d],g=Ie(p);p.min=r[d].min,p.max=p.min+g}):Sm(o,n.layoutBox,r)&&$e(d=>{const p=s?n.measuredBox[d]:n.layoutBox[d],g=Ie(r[d]);p.max=p.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+g)});const a=Wn();Ir(a,r,n.layoutBox);const l=Wn();s?Ir(l,e.applyTransform(i,!0),n.measuredBox):Ir(l,r,n.layoutBox);const u=!vm(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:p,layout:g}=d;if(p&&g){const v=ae();Fr(v,n.layoutBox,p.layoutBox);const x=ae();Fr(x,r,g.layoutBox),xm(v,x)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Bx(e){un.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Ux(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function $x(e){e.clearSnapshot()}function bd(e){e.clearMeasurements()}function Wx(e){e.isLayoutDirty=!1}function Hx(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Nd(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Gx(e){e.resolveTargetDelta()}function Kx(e){e.calcProjection()}function Qx(e){e.resetRotation()}function Yx(e){e.removeLeadSnapshot()}function Ed(e,t,n){e.translate=J(t.translate,0,n),e.scale=J(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Md(e,t,n,r){e.min=J(t.min,n.min,r),e.max=J(t.max,n.max,r)}function Xx(e,t,n,r){Md(e.x,t.x,n.x,r),Md(e.y,t.y,n.y,r)}function Zx(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const qx={duration:.45,ease:[.4,0,.1,1]},Ad=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Vd=Ad("applewebkit/")&&!Ad("chrome/")?Math.round:ie;function Ld(e){e.min=Vd(e.min),e.max=Vd(e.max)}function Jx(e){Ld(e.x),Ld(e.y)}function Sm(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ya(Cd(t),Cd(n),.2)}const ew=wm({attachResizeListener:(e,t)=>yt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Qs={current:void 0},km=wm({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Qs.current){const e=new ew({});e.mount(window),e.setOptions({layoutScroll:!0}),Qs.current=e}return Qs.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),tw={pan:{Feature:yx},drag:{Feature:gx,ProjectionNode:km,MeasureLayout:mm}},nw=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function rw(e){const t=nw.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ja(e,t,n=1){const[r,i]=rw(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return im(s)?parseFloat(s):s}else return Ua(i)?Ja(i,t,n+1):i}function iw(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!Ua(o))return;const s=Ja(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!Ua(o))continue;const s=Ja(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const ow=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Cm=e=>ow.has(e),sw=e=>Object.keys(e).some(Cm),Rd=e=>e===jn||e===L,Dd=(e,t)=>parseFloat(e.split(", ")[t]),zd=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return Dd(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?Dd(o[1],e):0}},aw=new Set(["x","y","z"]),lw=mi.filter(e=>!aw.has(e));function uw(e){const t=[];return lw.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const ar={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:zd(4,13),y:zd(5,14)};ar.translateX=ar.x;ar.translateY=ar.y;const cw=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=ar[u](r,o)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=ar[u](l,o)}),e},dw=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Cm);let o=[],s=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],d=xr(c);const p=t[l];let g;if(Mo(p)){const v=p.length,x=p[0]===null?1:0;c=p[x],d=xr(c);for(let k=x;k<v&&p[k]!==null;k++)g?pu(xr(p[k])===g):g=xr(p[k])}else g=xr(p);if(d!==g)if(Rd(d)&&Rd(g)){const v=u.get();typeof v=="string"&&u.set(parseFloat(v)),typeof p=="string"?t[l]=parseFloat(p):Array.isArray(p)&&g===L&&(t[l]=p.map(parseFloat))}else d!=null&&d.transform&&(g!=null&&g.transform)&&(c===0||p===0)?c===0?u.set(g.transform(c)):t[l]=d.transform(p):(s||(o=uw(e),s=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(p))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=cw(t,e,a);return o.length&&o.forEach(([c,d])=>{e.getValue(c).set(d)}),e.render(),es&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function fw(e,t,n,r){return sw(t)?dw(e,t,n,r):{target:t,transitionEnd:r}}const pw=(e,t,n,r)=>{const i=iw(e,t,r);return t=i.target,r=i.transitionEnd,fw(e,t,n,r)},el={current:null},Pm={current:!1};function hw(){if(Pm.current=!0,!!es)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>el.current=e.matches;e.addListener(t),t()}else el.current=!1}function mw(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Le(o))e.addValue(i,o),Ro(r)&&r.add(i);else if(Le(s))e.addValue(i,sr(o,{owner:e})),Ro(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=e.getStaticValue(i);e.addValue(i,sr(a!==void 0?a:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const _d=new WeakMap,jm=Object.keys(si),gw=jm.length,Od=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],yw=ou.length;class vw{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>Y.render(this.render,!1,!0);const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=ns(n),this.isVariantNode=uh(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const p=c[d];a[d]!==void 0&&Le(p)&&(p.set(a[d],!1),Ro(u)&&u.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,_d.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Pm.current||hw(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:el.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){_d.delete(this.current),this.projection&&this.projection.unmount(),jt(this.notifyUpdate),jt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=Pn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&Y.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,a;for(let l=0;l<gw;l++){const u=jm[l],{isEnabled:c,Feature:d,ProjectionNode:p,MeasureLayout:g}=si[u];p&&(s=p),c(n)&&(!this.features[u]&&d&&(this.features[u]=new d(this)),g&&(a=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:d,layoutScroll:p,layoutRoot:g}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||d&&Un(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:g})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ae()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Od.length;r++){const i=Od[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=mw(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<yw;r++){const i=ou[r],o=this.props[i];(oi(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=sr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=fu(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Le(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new ku),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Tm extends vw{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=D1(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){L1(this,r,s);const a=pw(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function xw(e){return window.getComputedStyle(e)}class ww extends Tm{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(Pn.has(n)){const r=yu(n);return r&&r.default||0}else{const r=xw(t),i=(hh(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return pm(t,n)}build(t,n,r,i){au(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return du(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Le(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){wh(t,n,r,i)}}class Sw extends Tm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Pn.has(n)){const r=yu(n);return r&&r.default||0}return n=Sh.has(n)?n:ru(n),t.getAttribute(n)}measureInstanceViewportBox(){return ae()}scrapeMotionValuesFromProps(t,n){return Ch(t,n)}build(t,n,r,i){uu(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){kh(t,n,r,i)}mount(t){this.isSVGTag=cu(t.tagName),super.mount(t)}}const kw=(e,t)=>su(e)?new Sw(t,{enableHardwareAcceleration:!1}):new ww(t,{enableHardwareAcceleration:!0}),Cw={layout:{ProjectionNode:km,MeasureLayout:mm}},Pw={...X1,...gv,...tw,...Cw},D=Py((e,t)=>rv(e,t,Pw,kw));var jw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Tw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),bw=(e,t)=>{const n=b.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,children:a,...l},u)=>b.createElement("svg",{ref:u,...jw,width:i,height:i,stroke:r,strokeWidth:s?Number(o)*24/Number(i):o,className:`lucide lucide-${Tw(e)}`,...l},[...t.map(([c,d])=>b.createElement(c,d)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n};var O=bw;const bm=O("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),Nw=O("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),Ew=O("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]),Mw=O("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),Aw=O("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Vw=O("Check",[["polyline",{points:"20 6 9 17 4 12",key:"10jjfj"}]]),Lw=O("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),Rw=O("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),Dw=O("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),Nm=O("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),tl=O("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),zw=O("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),_w=O("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]),Ow=O("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),Iw=O("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),Cu=O("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["path",{d:"M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z",key:"nb9nel"}]]),Fw=O("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),Bw=O("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),Em=O("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),nl=O("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),Uw=O("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),$w=O("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),Ww=O("Package",[["path",{d:"M16.5 9.4 7.55 4.24",key:"10qotr"}],["path",{d:"M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z",key:"yt0hxn"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["line",{x1:"12",x2:"12",y1:"22",y2:"12",key:"a4e8g8"}]]),Hw=O("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]),zo=O("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),Gw=O("Printer",[["polyline",{points:"6 9 6 2 18 2 18 9",key:"1306q4"}],["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["rect",{width:"12",height:"8",x:"6",y:"14",key:"5ipwut"}]]),Kw=O("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]),Pu=O("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Mm=O("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z",key:"3xmgem"}]]),ss=O("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),rl=O("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),Qw=O("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),Yw=O("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),Xw=O("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),Am=O("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Zw=O("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),Id=O("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),qw=O("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),il=O("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);function Fd(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Jw(...e){return t=>{let n=!1;const r=e.map(i=>{const o=Fd(i,t);return!n&&typeof o=="function"&&(n=!0),o});if(n)return()=>{for(let i=0;i<r.length;i++){const o=r[i];typeof o=="function"?o():Fd(e[i],null)}}}}function e2(e){const t=n2(e),n=b.forwardRef((r,i)=>{const{children:o,...s}=r,a=b.Children.toArray(o),l=a.find(i2);if(l){const u=l.props.children,c=a.map(d=>d===l?b.Children.count(u)>1?b.Children.only(null):b.isValidElement(u)?u.props.children:null:d);return f.jsx(t,{...s,ref:i,children:b.isValidElement(u)?b.cloneElement(u,void 0,c):null})}return f.jsx(t,{...s,ref:i,children:o})});return n.displayName=`${e}.Slot`,n}var t2=e2("Slot");function n2(e){const t=b.forwardRef((n,r)=>{const{children:i,...o}=n;if(b.isValidElement(i)){const s=s2(i),a=o2(o,i.props);return i.type!==b.Fragment&&(a.ref=r?Jw(r,s):s),b.cloneElement(i,a)}return b.Children.count(i)>1?b.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var r2=Symbol("radix.slottable");function i2(e){return b.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===r2}function o2(e,t){const n={...t};for(const r in t){const i=e[r],o=t[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...a)=>{const l=o(...a);return i(...a),l}:i&&(n[r]=i):r==="style"?n[r]={...i,...o}:r==="className"&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}function s2(e){var r,i;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(i=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:i.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Vm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=Vm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Lm(){for(var e,t,n=0,r="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=Vm(e))&&(r&&(r+=" "),r+=t);return r}const Bd=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Ud=Lm,a2=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Ud(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:i,defaultVariants:o}=t,s=Object.keys(i).map(u=>{const c=n==null?void 0:n[u],d=o==null?void 0:o[u];if(c===null)return null;const p=Bd(c)||Bd(d);return i[u][p]}),a=n&&Object.entries(n).reduce((u,c)=>{let[d,p]=c;return p===void 0||(u[d]=p),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,c)=>{let{class:d,className:p,...g}=c;return Object.entries(g).every(v=>{let[x,k]=v;return Array.isArray(k)?k.includes({...o,...a}[x]):{...o,...a}[x]===k})?[...u,d,p]:u},[]);return Ud(e,s,l,n==null?void 0:n.class,n==null?void 0:n.className)};function l2(){for(var e=0,t,n,r="";e<arguments.length;)(t=arguments[e++])&&(n=Rm(t))&&(r&&(r+=" "),r+=n);return r}function Rm(e){if(typeof e=="string")return e;for(var t,n="",r=0;r<e.length;r++)e[r]&&(t=Rm(e[r]))&&(n&&(n+=" "),n+=t);return n}var ju="-";function u2(e){var t=d2(e),n=e.conflictingClassGroups,r=e.conflictingClassGroupModifiers,i=r===void 0?{}:r;function o(a){var l=a.split(ju);return l[0]===""&&l.length!==1&&l.shift(),Dm(l,t)||c2(a)}function s(a,l){var u=n[a]||[];return l&&i[a]?[].concat(u,i[a]):u}return{getClassGroupId:o,getConflictingClassGroupIds:s}}function Dm(e,t){var s;if(e.length===0)return t.classGroupId;var n=e[0],r=t.nextPart.get(n),i=r?Dm(e.slice(1),r):void 0;if(i)return i;if(t.validators.length!==0){var o=e.join(ju);return(s=t.validators.find(function(a){var l=a.validator;return l(o)}))==null?void 0:s.classGroupId}}var $d=/^\[(.+)\]$/;function c2(e){if($d.test(e)){var t=$d.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}}function d2(e){var t=e.theme,n=e.prefix,r={nextPart:new Map,validators:[]},i=p2(Object.entries(e.classGroups),n);return i.forEach(function(o){var s=o[0],a=o[1];ol(a,r,s,t)}),r}function ol(e,t,n,r){e.forEach(function(i){if(typeof i=="string"){var o=i===""?t:Wd(t,i);o.classGroupId=n;return}if(typeof i=="function"){if(f2(i)){ol(i(r),t,n,r);return}t.validators.push({validator:i,classGroupId:n});return}Object.entries(i).forEach(function(s){var a=s[0],l=s[1];ol(l,Wd(t,a),n,r)})})}function Wd(e,t){var n=e;return t.split(ju).forEach(function(r){n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n}function f2(e){return e.isThemeGetter}function p2(e,t){return t?e.map(function(n){var r=n[0],i=n[1],o=i.map(function(s){return typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(function(a){var l=a[0],u=a[1];return[t+l,u]})):s});return[r,o]}):e}function h2(e){if(e<1)return{get:function(){},set:function(){}};var t=0,n=new Map,r=new Map;function i(o,s){n.set(o,s),t++,t>e&&(t=0,r=n,n=new Map)}return{get:function(s){var a=n.get(s);if(a!==void 0)return a;if((a=r.get(s))!==void 0)return i(s,a),a},set:function(s,a){n.has(s)?n.set(s,a):i(s,a)}}}var zm="!";function m2(e){var t=e.separator||":",n=t.length===1,r=t[0],i=t.length;return function(s){for(var a=[],l=0,u=0,c,d=0;d<s.length;d++){var p=s[d];if(l===0){if(p===r&&(n||s.slice(d,d+i)===t)){a.push(s.slice(u,d)),u=d+i;continue}if(p==="/"){c=d;continue}}p==="["?l++:p==="]"&&l--}var g=a.length===0?s:s.substring(u),v=g.startsWith(zm),x=v?g.substring(1):g,k=c&&c>u?c-u:void 0;return{modifiers:a,hasImportantModifier:v,baseClassName:x,maybePostfixModifierPosition:k}}}function g2(e){if(e.length<=1)return e;var t=[],n=[];return e.forEach(function(r){var i=r[0]==="[";i?(t.push.apply(t,n.sort().concat([r])),n=[]):n.push(r)}),t.push.apply(t,n.sort()),t}function y2(e){return{cache:h2(e.cacheSize),splitModifiers:m2(e),...u2(e)}}var v2=/\s+/;function x2(e,t){var n=t.splitModifiers,r=t.getClassGroupId,i=t.getConflictingClassGroupIds,o=new Set;return e.trim().split(v2).map(function(s){var a=n(s),l=a.modifiers,u=a.hasImportantModifier,c=a.baseClassName,d=a.maybePostfixModifierPosition,p=r(d?c.substring(0,d):c),g=!!d;if(!p){if(!d)return{isTailwindClass:!1,originalClassName:s};if(p=r(c),!p)return{isTailwindClass:!1,originalClassName:s};g=!1}var v=g2(l).join(":"),x=u?v+zm:v;return{isTailwindClass:!0,modifierId:x,classGroupId:p,originalClassName:s,hasPostfixModifier:g}}).reverse().filter(function(s){if(!s.isTailwindClass)return!0;var a=s.modifierId,l=s.classGroupId,u=s.hasPostfixModifier,c=a+l;return o.has(c)?!1:(o.add(c),i(l,u).forEach(function(d){return o.add(a+d)}),!0)}).reverse().map(function(s){return s.originalClassName}).join(" ")}function w2(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i,o,s=a;function a(u){var c=t[0],d=t.slice(1),p=d.reduce(function(g,v){return v(g)},c());return r=y2(p),i=r.cache.get,o=r.cache.set,s=l,l(u)}function l(u){var c=i(u);if(c)return c;var d=x2(u,r);return o(u,d),d}return function(){return s(l2.apply(null,arguments))}}function G(e){var t=function(r){return r[e]||[]};return t.isThemeGetter=!0,t}var _m=/^\[(?:([a-z-]+):)?(.+)\]$/i,S2=/^\d+\/\d+$/,k2=new Set(["px","full","screen"]),C2=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P2=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,j2=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function qe(e){return hn(e)||k2.has(e)||S2.test(e)||sl(e)}function sl(e){return Tn(e,"length",A2)}function T2(e){return Tn(e,"size",Om)}function b2(e){return Tn(e,"position",Om)}function N2(e){return Tn(e,"url",V2)}function Bi(e){return Tn(e,"number",hn)}function hn(e){return!Number.isNaN(Number(e))}function E2(e){return e.endsWith("%")&&hn(e.slice(0,-1))}function Sr(e){return Hd(e)||Tn(e,"number",Hd)}function F(e){return _m.test(e)}function kr(){return!0}function Nt(e){return C2.test(e)}function M2(e){return Tn(e,"",L2)}function Tn(e,t,n){var r=_m.exec(e);return r?r[1]?r[1]===t:n(r[2]):!1}function A2(e){return P2.test(e)}function Om(){return!1}function V2(e){return e.startsWith("url(")}function Hd(e){return Number.isInteger(Number(e))}function L2(e){return j2.test(e)}function R2(){var e=G("colors"),t=G("spacing"),n=G("blur"),r=G("brightness"),i=G("borderColor"),o=G("borderRadius"),s=G("borderSpacing"),a=G("borderWidth"),l=G("contrast"),u=G("grayscale"),c=G("hueRotate"),d=G("invert"),p=G("gap"),g=G("gradientColorStops"),v=G("gradientColorStopPositions"),x=G("inset"),k=G("margin"),y=G("opacity"),h=G("padding"),m=G("saturate"),w=G("scale"),S=G("sepia"),T=G("skew"),j=G("space"),P=G("translate"),R=function(){return["auto","contain","none"]},V=function(){return["auto","hidden","clip","visible","scroll"]},ne=function(){return["auto",F,t]},_=function(){return[F,t]},de=function(){return["",qe]},Z=function(){return["auto",hn,F]},Xe=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},W=function(){return["solid","dashed","dotted","double","none"]},ke=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},N=function(){return["start","end","center","between","around","evenly","stretch"]},M=function(){return["","0",F]},z=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},A=function(){return[hn,Bi]},U=function(){return[hn,F]};return{cacheSize:500,theme:{colors:[kr],spacing:[qe],blur:["none","",Nt,F],brightness:A(),borderColor:[e],borderRadius:["none","","full",Nt,F],borderSpacing:_(),borderWidth:de(),contrast:A(),grayscale:M(),hueRotate:U(),invert:M(),gap:_(),gradientColorStops:[e],gradientColorStopPositions:[E2,sl],inset:ne(),margin:ne(),opacity:A(),padding:_(),saturate:A(),scale:A(),sepia:M(),skew:U(),space:_(),translate:_()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[Nt]}],"break-after":[{"break-after":z()}],"break-before":[{"break-before":z()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(Xe(),[F])}],overflow:[{overflow:V()}],"overflow-x":[{"overflow-x":V()}],"overflow-y":[{"overflow-y":V()}],overscroll:[{overscroll:R()}],"overscroll-x":[{"overscroll-x":R()}],"overscroll-y":[{"overscroll-y":R()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Sr]}],basis:[{basis:ne()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:M()}],shrink:[{shrink:M()}],order:[{order:["first","last","none",Sr]}],"grid-cols":[{"grid-cols":[kr]}],"col-start-end":[{col:["auto",{span:["full",Sr]},F]}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":[kr]}],"row-start-end":[{row:["auto",{span:[Sr]},F]}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal"].concat(N())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(N(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(N(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[k]}],mx:[{mx:[k]}],my:[{my:[k]}],ms:[{ms:[k]}],me:[{me:[k]}],mt:[{mt:[k]}],mr:[{mr:[k]}],mb:[{mb:[k]}],ml:[{ml:[k]}],"space-x":[{"space-x":[j]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[j]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",F,t]}],"min-w":[{"min-w":["min","max","fit",F,qe]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[Nt]},Nt,F]}],h:[{h:[F,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",F,qe]}],"max-h":[{"max-h":[F,t,"min","max","fit"]}],"font-size":[{text:["base",Nt,sl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Bi]}],"font-family":[{font:[kr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",hn,Bi]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",F,qe]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(W(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",qe]}],"underline-offset":[{"underline-offset":["auto",F,qe]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:_()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(Xe(),[b2])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",T2]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},N2]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[v]}],"gradient-via-pos":[{via:[v]}],"gradient-to-pos":[{to:[v]}],"gradient-from":[{from:[g]}],"gradient-via":[{via:[g]}],"gradient-to":[{to:[g]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[].concat(W(),["hidden"])}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:W()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:[""].concat(W())}],"outline-offset":[{"outline-offset":[F,qe]}],"outline-w":[{outline:[qe]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:de()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[qe]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Nt,M2]}],"shadow-color":[{shadow:[kr]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":ke()}],"bg-blend":[{"bg-blend":ke()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",Nt,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[m]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[m]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:U()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:U()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[Sr,F]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":_()}],"scroll-mx":[{"scroll-mx":_()}],"scroll-my":[{"scroll-my":_()}],"scroll-ms":[{"scroll-ms":_()}],"scroll-me":[{"scroll-me":_()}],"scroll-mt":[{"scroll-mt":_()}],"scroll-mr":[{"scroll-mr":_()}],"scroll-mb":[{"scroll-mb":_()}],"scroll-ml":[{"scroll-ml":_()}],"scroll-p":[{"scroll-p":_()}],"scroll-px":[{"scroll-px":_()}],"scroll-py":[{"scroll-py":_()}],"scroll-ps":[{"scroll-ps":_()}],"scroll-pe":[{"scroll-pe":_()}],"scroll-pt":[{"scroll-pt":_()}],"scroll-pr":[{"scroll-pr":_()}],"scroll-pb":[{"scroll-pb":_()}],"scroll-pl":[{"scroll-pl":_()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[qe,Bi]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var D2=w2(R2);function tn(...e){return D2(Lm(e))}function Gn(e){const t=document.getElementById(e);t&&t.scrollIntoView({behavior:"smooth"})}const z2=a2("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-14 rounded-lg px-10 text-lg",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),nt=b.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...i},o)=>{const s=r?t2:"button";return f.jsx(s,{className:tn(z2({variant:t,size:n,className:e})),ref:o,...i})});nt.displayName="Button";const _2=()=>{const[e,t]=b.useState(!1),[n,r]=b.useState(!1);b.useEffect(()=>{const o=()=>{r(window.scrollY>50)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[]);const i=[{label:"Features",href:"features",icon:Qw},{label:"Capabilities",href:"capabilities",icon:il},{label:"Technology",href:"technology",icon:Pu},{label:"Contact",href:"contact",icon:zo}];return f.jsx(D.header,{initial:{y:-100},animate:{y:0},className:`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${n?"bg-white/98 backdrop-blur-lg shadow-xl border-b border-green-100":"bg-white/10 backdrop-blur-sm"}`,children:f.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[f.jsxs("div",{className:"flex items-center justify-between h-16 lg:h-20",children:[f.jsxs(D.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1},className:"flex items-center space-x-4",children:[f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg",children:f.jsx(ss,{className:"w-7 h-7 text-white"})}),f.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center",children:f.jsx("span",{className:"text-xs text-white font-bold",children:"✓"})})]}),f.jsxs("div",{children:[f.jsx("h1",{className:`text-2xl font-bold transition-colors ${n?"text-gray-900":"text-white"}`,children:"POS Pro"}),f.jsx("p",{className:`text-sm font-medium transition-colors ${n?"text-green-600":"text-green-100"} hidden sm:block`,children:"Professional Point of Sale"})]})]}),f.jsx("nav",{className:"hidden lg:flex items-center space-x-1",children:i.map((o,s)=>f.jsxs(D.button,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.1+s*.1},onClick:()=>Gn(o.href),className:`flex items-center px-4 py-3 rounded-xl font-semibold transition-all duration-300 relative group ${n?"text-gray-700 hover:text-green-600 hover:bg-green-50":"text-white hover:text-green-100 hover:bg-white/10"}`,children:[f.jsx(o.icon,{className:"w-4 h-4 mr-2"}),o.label,f.jsx("span",{className:`absolute bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0.5 transition-all duration-300 group-hover:w-3/4 rounded-full ${n?"bg-green-500":"bg-white"}`})]},o.href))}),f.jsxs(D.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.2},className:"hidden lg:flex items-center space-x-3",children:[f.jsx(nt,{variant:"outline",onClick:()=>window.open("http://localhost:8080","_blank"),className:`border-2 transition-all duration-300 ${n?"border-green-500 text-green-600 hover:bg-green-50 hover:border-green-600":"border-white text-white hover:bg-white/10 hover:border-green-200"}`,children:"🚀 Live Demo"}),f.jsx(nt,{onClick:()=>Gn("contact"),className:"bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:"✨ Get Started"})]}),f.jsx("button",{className:`lg:hidden p-3 rounded-xl transition-all duration-300 ${n?"text-gray-700 hover:text-green-600 hover:bg-green-50":"text-white hover:text-green-100 hover:bg-white/10"}`,onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?f.jsx(qw,{className:"w-6 h-6"}):f.jsx(Uw,{className:"w-6 h-6"})})]}),e&&f.jsx(D.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"lg:hidden border-t border-green-100 bg-white/98 backdrop-blur-lg shadow-xl",children:f.jsxs("div",{className:"py-6 space-y-2",children:[i.map(o=>f.jsxs("button",{onClick:()=>{Gn(o.href),t(!1)},className:"flex items-center w-full text-left px-6 py-4 text-gray-700 hover:text-green-600 hover:bg-green-50 transition-all duration-200 rounded-xl mx-2 font-semibold",children:[f.jsx(o.icon,{className:"w-5 h-5 mr-3 text-green-500"}),o.label]},o.href)),f.jsxs("div",{className:"px-6 pt-6 space-y-4",children:[f.jsx(nt,{variant:"outline",className:"w-full border-2 border-green-500 text-green-600 hover:bg-green-50 hover:border-green-600 py-4 text-lg font-bold rounded-xl",onClick:()=>{window.open("http://localhost:8080","_blank"),t(!1)},children:"🚀 Live Demo"}),f.jsx(nt,{className:"w-full bg-green-500 hover:bg-green-600 text-white shadow-lg py-4 text-lg font-bold rounded-xl",onClick:()=>{Gn("contact"),t(!1)},children:"✨ Get Started"})]})]})})]})})},En=({iconName:e,alt:t,className:n="w-8 h-8",fallbackEmoji:r="🔧"})=>{const[i,o]=Br.useState(!1),[s,a]=Br.useState(!1),l=`/icons/${e}.svg`,u=`/icons/${e}.png`,c=()=>{a(!0)},d=()=>{o(!0)};return i&&s?f.jsx("span",{className:`${n} flex items-center justify-center text-2xl`,children:r}):s?f.jsx("img",{src:u,alt:t,className:n,onError:d,loading:"lazy"}):f.jsx("img",{src:l,alt:t,className:n,onError:c,loading:"lazy"})},O2=()=>{const e=[{icon:Zw,label:"Works Offline",value:"100%"},{icon:tl,label:"Local Storage",value:"SQLite"},{icon:Fw,label:"Cloud Backup",value:"Auto"},{icon:Mm,label:"Multi-Platform",value:"Ready"}];return f.jsxs("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[f.jsx("div",{className:"absolute inset-0 bg-gray-900"}),f.jsx("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}}),f.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-800/50 via-gray-900/80 to-black/60"}),f.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:f.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 lg:gap-20 items-center",children:[f.jsxs(D.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"text-center lg:text-left",children:[f.jsxs(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"inline-flex items-center px-6 py-3 rounded-full bg-white/20 backdrop-blur-sm border border-white/30 text-white text-sm font-medium mb-8",children:[f.jsx("span",{className:"w-2 h-2 bg-white rounded-full mr-3 animate-pulse"}),"🌐 Works 100% Offline - No Internet Required"]}),f.jsxs(D.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-4xl sm:text-5xl lg:text-7xl font-bold text-white leading-tight mb-8",children:["Professional",f.jsx("span",{className:"text-white",children:" POS System"}),f.jsx("br",{}),f.jsx("span",{className:"text-3xl sm:text-4xl lg:text-5xl text-green-100",children:"for Modern Business"})]}),f.jsx(D.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"text-xl text-green-100 mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed",children:"Advanced Flutter-based point of sale solution that works completely offline. Features partial payments, multi-language support, thermal printer integration, and comprehensive inventory management. Built for businesses that demand excellence."}),f.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.45},className:"mb-10",children:f.jsxs("div",{className:"bg-white/15 backdrop-blur-md rounded-3xl p-8 border-2 border-white/30 shadow-2xl",children:[f.jsxs("h3",{className:"text-2xl font-bold text-white mb-6 flex items-center",children:[f.jsx("span",{className:"w-4 h-4 bg-white rounded-full mr-4 animate-pulse"}),"🌐 Complete Offline Functionality"]}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-green-100",children:[f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"mr-4 mt-1 flex-shrink-0",children:f.jsx(En,{iconName:"backup-icon",alt:"Local & Cloud Backup",className:"w-10 h-10",fallbackEmoji:"💾"})}),f.jsxs("div",{children:[f.jsx("strong",{className:"text-white text-base",children:"Local & Cloud Backup"}),f.jsx("p",{className:"text-sm text-green-100 mt-1",children:"Automatic SQLite backup with Google Drive sync"})]})]}),f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"mr-4 mt-1 flex-shrink-0",children:f.jsx(En,{iconName:"barcode-icon",alt:"Barcode Generation & Printing",className:"w-10 h-10",fallbackEmoji:"📊"})}),f.jsxs("div",{children:[f.jsx("strong",{className:"text-white text-base",children:"Barcode Generation & Printing"}),f.jsx("p",{className:"text-sm text-green-100 mt-1",children:"Create and print barcodes for all products"})]})]}),f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"mr-4 mt-1 flex-shrink-0",children:f.jsx(En,{iconName:"thermal-printer-icon",alt:"Thermal Receipt Printing",className:"w-10 h-10",fallbackEmoji:"🖨️"})}),f.jsxs("div",{children:[f.jsx("strong",{className:"text-white text-base",children:"Thermal Receipt Printing"}),f.jsx("p",{className:"text-sm text-green-100 mt-1",children:"58mm/80mm thermal printer support via Bluetooth"})]})]}),f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"mr-4 mt-1 flex-shrink-0",children:f.jsx(En,{iconName:"cross-platform-icon",alt:"Cross-Platform Sync",className:"w-10 h-10",fallbackEmoji:"📱"})}),f.jsxs("div",{children:[f.jsx("strong",{className:"text-white text-base",children:"Cross-Platform Sync"}),f.jsx("p",{className:"text-sm text-green-100 mt-1",children:"Works on mobile, tablet, and desktop seamlessly"})]})]}),f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"mr-4 mt-1 flex-shrink-0",children:f.jsx(En,{iconName:"secure-storage-icon",alt:"Secure Local Storage",className:"w-10 h-10",fallbackEmoji:"🔒"})}),f.jsxs("div",{children:[f.jsx("strong",{className:"text-white text-base",children:"Secure Local Storage"}),f.jsx("p",{className:"text-sm text-green-100 mt-1",children:"Encrypted SQLite database with data protection"})]})]}),f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"mr-4 mt-1 flex-shrink-0",children:f.jsx(En,{iconName:"real-time-icon",alt:"Real-Time Processing",className:"w-10 h-10",fallbackEmoji:"⚡"})}),f.jsxs("div",{children:[f.jsx("strong",{className:"text-white text-base",children:"Real-Time Processing"}),f.jsx("p",{className:"text-sm text-green-100 mt-1",children:"Lightning-fast transactions without internet delays"})]})]})]})]})}),f.jsxs(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"flex flex-col sm:flex-row gap-6 justify-center lg:justify-start mb-16",children:[f.jsx(nt,{size:"xl",onClick:()=>Gn("features"),className:"bg-green-500 text-white hover:bg-green-600 shadow-2xl hover:shadow-green-500/25 transition-all duration-300 transform hover:scale-105 border-2 border-green-500 hover:border-green-600 font-bold px-12 py-6 text-xl",children:"Explore Offline Features"}),f.jsx(nt,{size:"xl",onClick:()=>window.open("http://localhost:8080","_blank"),className:"bg-gray-700 hover:bg-gray-600 border-2 border-gray-600 hover:border-gray-500 text-white transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-gray-700/30 font-bold px-12 py-6 text-xl",children:"Try Live Demo"})]}),f.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map((t,n)=>f.jsx(D.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.7+n*.1},className:"group",children:f.jsxs("div",{className:"relative bg-gradient-to-br from-white/25 to-white/10 backdrop-blur-xl rounded-2xl p-6 border border-white/30 hover:border-white/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-white/20 min-h-[160px]",children:[f.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-400/10 to-blue-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),f.jsxs("div",{className:"relative z-10 flex flex-col items-center text-center h-full justify-between",children:[f.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-white/40 to-white/20 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110",children:f.jsx(t.icon,{className:"w-8 h-8 text-white drop-shadow-lg"})}),f.jsx("div",{className:"my-3",children:f.jsx("span",{className:"text-4xl font-black text-white stats-counter tracking-tight",children:t.value})}),f.jsx("p",{className:"text-sm font-semibold text-white/90 uppercase tracking-wide leading-tight",children:t.label})]}),f.jsx("div",{className:"absolute top-3 right-3 w-2 h-2 bg-green-400 rounded-full opacity-60 group-hover:opacity-100 transition-opacity duration-300"}),f.jsx("div",{className:"absolute bottom-3 left-3 w-1 h-1 bg-white/60 rounded-full"})]})},t.label))})]}),f.jsx(D.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"relative flex justify-center lg:justify-end items-start lg:-mt-[32rem]",children:f.jsx("div",{className:"relative",children:f.jsx(D.div,{initial:{opacity:0,scale:.3,rotate:-180},animate:{opacity:1,scale:1,rotate:0},transition:{duration:1.2,delay:.4,type:"spring",stiffness:100},className:"relative z-20 transform hover:scale-105 transition-transform duration-700",children:f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-400/40 via-cyan-400/40 to-blue-400/40 rounded-full blur-3xl scale-125 animate-pulse"}),f.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-green-300/30 to-teal-300/30 rounded-full blur-2xl scale-110 animate-ping"}),f.jsx("div",{className:"absolute inset-0 bg-white/10 rounded-full blur-xl scale-105"}),f.jsx(D.img,{src:"/TT.png",alt:"TT Logo",className:"relative z-10 w-[32rem] h-[32rem] sm:w-[40rem] sm:h-[40rem] lg:w-[48rem] lg:h-[48rem] object-contain filter brightness-110 contrast-110",whileHover:{scale:1.1,rotate:[0,10,-10,0],filter:"brightness(1.2) contrast(1.2)",transition:{duration:.6,type:"spring"}},animate:{y:[0,-20,0],x:[0,5,-5,0],scale:[1,1.02,1],rotateY:[0,5,-5,0]},transition:{y:{duration:5,repeat:1/0,ease:"easeInOut"},x:{duration:7,repeat:1/0,ease:"easeInOut"},scale:{duration:4,repeat:1/0,ease:"easeInOut"},rotateY:{duration:8,repeat:1/0,ease:"easeInOut"}}})]})})})})]})})]})};var al=new Map,Ui=new WeakMap,Gd=0,I2=void 0;function F2(e){return e?(Ui.has(e)||(Gd+=1,Ui.set(e,Gd.toString())),Ui.get(e)):"0"}function B2(e){return Object.keys(e).sort().filter(t=>e[t]!==void 0).map(t=>`${t}_${t==="root"?F2(e.root):e[t]}`).toString()}function U2(e){const t=B2(e);let n=al.get(t);if(!n){const r=new Map;let i;const o=new IntersectionObserver(s=>{s.forEach(a=>{var l;const u=a.isIntersecting&&i.some(c=>a.intersectionRatio>=c);e.trackVisibility&&typeof a.isVisible>"u"&&(a.isVisible=u),(l=r.get(a.target))==null||l.forEach(c=>{c(u,a)})})},e);i=o.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:o,elements:r},al.set(t,n)}return n}function $2(e,t,n={},r=I2){if(typeof window.IntersectionObserver>"u"&&r!==void 0){const l=e.getBoundingClientRect();return t(r,{isIntersecting:r,target:e,intersectionRatio:typeof n.threshold=="number"?n.threshold:0,time:0,boundingClientRect:l,intersectionRect:l,rootBounds:l}),()=>{}}const{id:i,observer:o,elements:s}=U2(n),a=s.get(e)||[];return s.has(e)||s.set(e,a),a.push(t),o.observe(e),function(){a.splice(a.indexOf(t),1),a.length===0&&(s.delete(e),o.unobserve(e)),s.size===0&&(o.disconnect(),al.delete(i))}}function as({threshold:e,delay:t,trackVisibility:n,rootMargin:r,root:i,triggerOnce:o,skip:s,initialInView:a,fallbackInView:l,onChange:u}={}){var c;const[d,p]=b.useState(null),g=b.useRef(u),[v,x]=b.useState({inView:!!a,entry:void 0});g.current=u,b.useEffect(()=>{if(s||!d)return;let m;return m=$2(d,(w,S)=>{x({inView:w,entry:S}),g.current&&g.current(w,S),S.isIntersecting&&o&&m&&(m(),m=void 0)},{root:i,rootMargin:r,threshold:e,trackVisibility:n,delay:t},l),()=>{m&&m()}},[Array.isArray(e)?e.toString():e,d,i,r,o,s,n,l,t]);const k=(c=v.entry)==null?void 0:c.target,y=b.useRef(void 0);!d&&k&&!o&&!s&&y.current!==k&&(y.current=k,x({inView:!!a,entry:void 0}));const h=[p,v.inView,v.entry];return h.ref=h[0],h.inView=h[1],h.entry=h[2],h}const li=b.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:tn("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));li.displayName="Card";const ls=b.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:tn("flex flex-col space-y-1.5 p-6",e),...t}));ls.displayName="CardHeader";const us=b.forwardRef(({className:e,...t},n)=>f.jsx("h3",{ref:n,className:tn("text-2xl font-semibold leading-none tracking-tight",e),...t}));us.displayName="CardTitle";const Im=b.forwardRef(({className:e,...t},n)=>f.jsx("p",{ref:n,className:tn("text-sm text-muted-foreground",e),...t}));Im.displayName="CardDescription";const ui=b.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:tn("p-6 pt-0",e),...t}));ui.displayName="CardContent";const W2=b.forwardRef(({className:e,...t},n)=>f.jsx("div",{ref:n,className:tn("flex items-center p-6 pt-0",e),...t}));W2.displayName="CardFooter";const H2=()=>{const[e,t]=as({triggerOnce:!0,threshold:.1}),n=[{icon:ss,title:"Advanced Cart Management",description:"Smart cart with auto-save functionality, quantity controls, and persistent storage across sessions.",color:"from-blue-500 to-blue-600"},{icon:Nm,title:"Partial Payment System",description:"Revolutionary partial payment support with automatic debt tracking and customer balance management.",color:"from-green-500 to-green-600"},{icon:Pu,title:"Comprehensive Settings",description:"Professional settings interface with printer configuration, business setup, and system preferences.",color:"from-purple-500 to-purple-600"},{icon:Cu,title:"Multi-Language Support",description:"Full support for Arabic (RTL), English, French, Spanish, and German with seamless switching.",color:"from-orange-500 to-orange-600"},{icon:zw,title:"Multi-Currency System",description:"Support for DZD, USD, EUR, GBP, CAD with real-time currency formatting and conversion.",color:"from-emerald-500 to-emerald-600"},{icon:Gw,title:"Thermal Printer Integration",description:"Bluetooth thermal printer support for 58mm and 80mm receipts with advanced configuration.",color:"from-red-500 to-red-600"},{icon:Mw,title:"Real-Time Analytics",description:"Modern dashboard with live sales data, inventory tracking, and comprehensive reporting.",color:"from-indigo-500 to-indigo-600"},{icon:Ww,title:"Inventory Management",description:"Complete inventory control with low stock alerts, barcode scanning, and category management.",color:"from-teal-500 to-teal-600"},{icon:Am,title:"Customer & Supplier Management",description:"Comprehensive contact management with debt tracking, purchase history, and detailed profiles.",color:"from-pink-500 to-pink-600"},{icon:Ow,title:"Invoice Management",description:"Professional invoicing with multiple payment statuses, PDF generation, and WhatsApp sharing.",color:"from-yellow-500 to-yellow-600"},{icon:$w,title:"Dark/Light Theme",description:"Beautiful dark and light themes with automatic switching and user preference persistence.",color:"from-gray-500 to-gray-600"},{icon:rl,title:"Responsive Design",description:"Perfect experience across all devices - mobile phones, tablets, and desktop computers.",color:"from-cyan-500 to-cyan-600"}],r={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},i={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6}}};return f.jsx("section",{id:"features",className:"py-20 bg-gray-50",children:f.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[f.jsxs(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{duration:.6},className:"text-center mb-16",children:[f.jsxs("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4",children:["Powerful Features for",f.jsx("span",{className:"bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent",children:" Modern Business"})]}),f.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Our comprehensive POS system includes everything you need to run your business efficiently, from advanced payment processing to detailed analytics and reporting."})]}),f.jsx(D.div,{ref:e,variants:r,initial:"hidden",animate:t?"visible":"hidden",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:n.map(o=>f.jsx(D.div,{variants:i,className:"group",children:f.jsxs(li,{className:"h-full feature-card border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm",children:[f.jsxs(ls,{className:"pb-4",children:[f.jsx("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${o.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`,children:f.jsx(o.icon,{className:"w-6 h-6 text-white"})}),f.jsx(us,{className:"text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors",children:o.title})]}),f.jsx(ui,{children:f.jsx(Im,{className:"text-gray-600 leading-relaxed",children:o.description})})]})},o.title))}),f.jsx(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{duration:.6,delay:.8},className:"text-center mt-16",children:f.jsxs("div",{className:"bg-gradient-to-r from-primary-500 to-primary-600 rounded-2xl p-8 text-white",children:[f.jsx("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Transform Your Business?"}),f.jsx("p",{className:"text-primary-100 mb-6 max-w-2xl mx-auto",children:"Join thousands of businesses already using our POS system to streamline operations and boost sales."}),f.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[f.jsx("button",{onClick:()=>window.open("http://localhost:8080","_blank"),className:"bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Try Live Demo"}),f.jsx("button",{className:"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors",children:"Contact Sales"})]})]})})]})})},G2=({screenshot:e,alt:t,className:n,variant:r="default",showReflection:i=!0,animate:o=!0})=>{const s="relative select-none",a={default:"",floating:"animate-float",tilted:"transform rotate-3 hover:rotate-0 transition-transform duration-500"};return f.jsxs("div",{className:tn(s,a[r],n),children:[f.jsxs(D.div,{initial:o?{opacity:0,scale:.8}:{},animate:o?{opacity:1,scale:1}:{},transition:{duration:.8,ease:"easeOut"},className:"relative group",children:[f.jsx("div",{className:"absolute inset-0 bg-black/20 blur-2xl transform translate-y-8 scale-95 opacity-60 group-hover:opacity-80 transition-opacity duration-300"}),f.jsxs("div",{className:"relative bg-black rounded-[3rem] p-2 shadow-2xl group-hover:shadow-3xl transition-shadow duration-300",children:[f.jsx("div",{className:"absolute inset-2 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-[2.5rem] opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"}),f.jsxs("div",{className:"relative bg-black rounded-[2.5rem] overflow-hidden",children:[f.jsx("div",{className:"absolute top-2 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black rounded-full z-10"}),f.jsxs("div",{className:"relative w-[280px] h-[600px] bg-white rounded-[2.5rem] overflow-hidden",children:[f.jsx("img",{src:e,alt:t,className:"w-full h-full object-cover object-top",loading:"lazy",onError:l=>{const u=l.target;u.src="/api/placeholder/280/600"}}),f.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 pointer-events-none"}),i&&f.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent pointer-events-none opacity-30"})]})]}),f.jsx("div",{className:"absolute left-[-2px] top-20 w-1 h-8 bg-gray-800 rounded-l-sm"}),f.jsx("div",{className:"absolute left-[-2px] top-32 w-1 h-12 bg-gray-800 rounded-l-sm"}),f.jsx("div",{className:"absolute left-[-2px] top-48 w-1 h-12 bg-gray-800 rounded-l-sm"}),f.jsx("div",{className:"absolute right-[-2px] top-24 w-1 h-16 bg-gray-800 rounded-r-sm"})]}),f.jsx(D.div,{animate:o?{y:[0,-10,0]}:{},transition:{duration:3,repeat:1/0,ease:"easeInOut"},className:"absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg z-10",children:f.jsx("span",{className:"text-white text-xs font-bold",children:"POS"})}),f.jsx(D.div,{animate:o?{y:[0,10,0]}:{},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"absolute -bottom-4 -right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-green-200 z-10",children:f.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"})})]}),i&&f.jsx("div",{className:"absolute top-full left-0 w-full h-32 opacity-20 pointer-events-none",children:f.jsx("div",{className:"w-full h-full bg-gradient-to-b from-gray-900 to-transparent transform scale-y-[-1] blur-sm",style:{maskImage:"linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 70%)",WebkitMaskImage:"linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 70%)"}})})]})},K2=({title:e,description:t,features:n,icon:r,imagePosition:i,gradient:o,screenshot:s,screenshotAlt:a})=>{const[l,u]=as({triggerOnce:!0,threshold:.2}),c={hidden:{opacity:0,x:i==="left"?50:-50},visible:{opacity:1,x:0}},d={hidden:{opacity:0,x:i==="left"?-50:50},visible:{opacity:1,x:0}};return f.jsx("div",{ref:l,className:"py-20",children:f.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:f.jsxs("div",{className:`grid lg:grid-cols-2 gap-12 lg:gap-20 items-center ${i==="right"?"lg:grid-flow-col-dense":""}`,children:[f.jsxs(D.div,{variants:c,initial:"hidden",animate:u?"visible":"hidden",transition:{duration:.8},className:`${i==="right"?"lg:col-start-1":""}`,children:[f.jsx("div",{className:`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-r ${o} mb-6`,children:f.jsx(r,{className:"w-8 h-8 text-white"})}),f.jsx("h3",{className:"text-3xl lg:text-4xl font-bold text-gray-900 mb-6",children:e}),f.jsx("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:t}),f.jsx("ul",{className:"space-y-4 mb-8",children:n.map((p,g)=>f.jsxs(D.li,{initial:{opacity:0,x:-20},animate:u?{opacity:1,x:0}:{},transition:{delay:.2+g*.1},className:"flex items-start",children:[f.jsx("div",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center mr-3 mt-0.5",children:f.jsx(Vw,{className:"w-4 h-4 text-primary-600"})}),f.jsx("span",{className:"text-gray-700 leading-relaxed",children:p})]},g))}),f.jsxs(nt,{size:"lg",onClick:()=>window.open("http://localhost:8080","_blank"),className:"bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700",children:["Try This Feature",f.jsx(bm,{className:"ml-2 w-5 h-5"})]})]}),f.jsx(D.div,{variants:d,initial:"hidden",animate:u?"visible":"hidden",transition:{duration:.8,delay:.2},className:`flex justify-center ${i==="right"?"lg:col-start-2":""}`,children:f.jsx(G2,{screenshot:s,alt:a,variant:"tilted",className:"scale-90 lg:scale-100"})})]})})})},Q2=()=>{const e=[{title:"Smart Cart Management",description:"Experience the most advanced cart system with automatic saving, intelligent quantity controls, and seamless product management. Never lose your work again.",features:["Auto-save functionality with persistent storage","Smart quantity controls with validation","Real-time price calculations and updates","Beautiful empty cart state with guidance","Smooth animations and transitions"],icon:ss,imagePosition:"right",gradient:"from-blue-500 to-blue-600",screenshot:"/screenshots/cart.png",screenshotAlt:"POS Pro Smart Cart - Advanced cart management with auto-save and quantity controls"},{title:"Partial Payment System",description:"Revolutionary payment processing that supports partial payments with automatic debt tracking. Perfect for businesses that need flexible payment options.",features:["Partial payment support with debt tracking","Automatic customer balance management","Real-time payment status updates","Comprehensive payment history","Integration with customer profiles"],icon:Nm,imagePosition:"left",gradient:"from-green-500 to-green-600",screenshot:"/screenshots/payment.png",screenshotAlt:"POS Pro Partial Payment - Revolutionary payment system with debt tracking"},{title:"Professional Settings",description:"Comprehensive settings interface that gives you complete control over your POS system. Configure everything from printer settings to business preferences.",features:["Intuitive settings organization and navigation","Advanced printer configuration and testing","Multi-language and currency preferences","Business information and branding setup","System backup and security options"],icon:Pu,imagePosition:"right",gradient:"from-purple-500 to-purple-600",screenshot:"/screenshots/settings.png",screenshotAlt:"POS Pro Settings - Comprehensive configuration interface with printer setup"},{title:"Multi-Language Support",description:"Seamlessly switch between 5 languages including full RTL support for Arabic. Perfect for international businesses and diverse customer bases.",features:["Support for Arabic (RTL), English, French, Spanish, German","Instant language switching without restart","Proper RTL layout and text direction","Localized number and currency formatting","Persistent language preferences"],icon:Cu,imagePosition:"left",gradient:"from-orange-500 to-orange-600",screenshot:"/screenshots/language.png",screenshotAlt:"POS Pro Multi-Language - Support for 5 languages including Arabic RTL"}];return f.jsx("section",{id:"capabilities",className:"bg-white",children:e.map((t,n)=>f.jsx("div",{className:n%2===1?"bg-gray-50":"bg-white",children:f.jsx(K2,{...t})},t.title))})},Y2=()=>{const[e,t]=as({triggerOnce:!0,threshold:.1}),n=(a,l=2e3)=>{const[u,c]=b.useState(0);return b.useEffect(()=>{if(!t)return;let d;const p=g=>{d||(d=g);const v=Math.min((g-d)/l,1);c(Math.floor(v*a)),v<1&&requestAnimationFrame(p)};requestAnimationFrame(p)},[t,a,l]),u},r=[{icon:rl,title:"Flutter Framework",description:"Cross-platform mobile development with native performance",color:"from-blue-500 to-blue-600"},{icon:tl,title:"SQLite Database",description:"Reliable local storage with real-time synchronization",color:"from-green-500 to-green-600"},{icon:Id,title:"Bluetooth Integration",description:"Seamless thermal printer connectivity and management",color:"from-purple-500 to-purple-600"},{icon:Mm,title:"Secure Architecture",description:"Enterprise-grade security with encrypted data storage",color:"from-red-500 to-red-600"},{icon:il,title:"Real-time Updates",description:"Instant data synchronization and live notifications",color:"from-yellow-500 to-yellow-600"},{icon:Rw,title:"Cloud Backup",description:"Automatic Google Drive integration for data safety",color:"from-indigo-500 to-indigo-600"}],i=[{icon:Am,label:"Active Users",value:n(1e4),suffix:"+",color:"text-blue-600"},{icon:Yw,label:"Transactions Processed",value:n(1e6),suffix:"+",color:"text-green-600"},{icon:Cu,label:"Countries Supported",value:n(25),suffix:"+",color:"text-purple-600"},{icon:Ew,label:"Customer Satisfaction",value:n(99),suffix:"%",color:"text-orange-600"}],o={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},s={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.6}}};return f.jsxs("section",{id:"technology",className:"py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden",children:[f.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[f.jsx("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"}),f.jsx("div",{className:"absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"})]}),f.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[f.jsxs(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{duration:.6},className:"text-center mb-16",children:[f.jsxs("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold mb-4",children:["Built with",f.jsx("span",{className:"bg-gradient-to-r from-primary-400 to-blue-400 bg-clip-text text-transparent",children:" Modern Technology"})]}),f.jsx("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Our POS system leverages cutting-edge technologies to deliver exceptional performance, security, and user experience across all platforms."})]}),f.jsx(D.div,{ref:e,variants:o,initial:"hidden",animate:t?"visible":"hidden",className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-20",children:r.map(a=>f.jsx(D.div,{variants:s,className:"group",children:f.jsxs(li,{className:"h-full bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 group-hover:scale-105",children:[f.jsxs(ls,{className:"pb-4",children:[f.jsx("div",{className:`w-12 h-12 rounded-lg bg-gradient-to-r ${a.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`,children:f.jsx(a.icon,{className:"w-6 h-6 text-white"})}),f.jsx(us,{className:"text-lg font-semibold text-white",children:a.title})]}),f.jsx(ui,{children:f.jsx("p",{className:"text-gray-300 leading-relaxed",children:a.description})})]})},a.title))}),f.jsxs(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{duration:.6,delay:.4},className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20",children:[f.jsxs("div",{className:"text-center mb-8",children:[f.jsx("h3",{className:"text-2xl font-bold text-white mb-2",children:"Trusted by Businesses Worldwide"}),f.jsx("p",{className:"text-gray-300",children:"Join thousands of satisfied customers using our POS system"})]}),f.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-8",children:i.map((a,l)=>f.jsxs(D.div,{initial:{opacity:0,scale:.8},animate:t?{opacity:1,scale:1}:{},transition:{delay:.6+l*.1},className:"text-center",children:[f.jsxs("div",{className:"flex items-center justify-center mb-3",children:[f.jsx(a.icon,{className:`w-8 h-8 ${a.color} mr-2`}),f.jsxs("span",{className:"text-3xl lg:text-4xl font-bold text-white stats-counter",children:[a.value.toLocaleString(),a.suffix]})]}),f.jsx("p",{className:"text-gray-300 text-sm",children:a.label})]},a.label))})]}),f.jsxs(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{duration:.6,delay:.8},className:"mt-16 text-center",children:[f.jsx("h3",{className:"text-xl font-semibold text-white mb-6",children:"Powered by Industry-Leading Technologies"}),f.jsx("div",{className:"flex flex-wrap justify-center items-center gap-8 opacity-60",children:[{name:"Flutter",icon:Dw},{name:"Dart",icon:il},{name:"SQLite",icon:tl},{name:"Material Design",icon:Hw},{name:"Provider",icon:rl},{name:"Bluetooth",icon:Id}].map((a,l)=>f.jsxs(D.div,{initial:{opacity:0,y:10},animate:t?{opacity:.6,y:0}:{},transition:{delay:1+l*.1},className:"flex items-center space-x-2 text-gray-300 hover:text-white transition-colors",children:[f.jsx(a.icon,{className:"w-5 h-5"}),f.jsx("span",{className:"text-sm font-medium",children:a.name})]},a.name))})]})]})]})},X2=()=>{const[e,t]=as({triggerOnce:!0,threshold:.1}),n=[{icon:Em,title:"Email Us",description:"Get in touch with our sales team",value:"<EMAIL>",action:"mailto:<EMAIL>"},{icon:zo,title:"Call Us",description:"Speak with our experts",value:"+****************",action:"tel:+***********"},{icon:nl,title:"Visit Us",description:"Our headquarters",value:"123 Business Ave, Tech City",action:"#"},{icon:Lw,title:"Business Hours",description:"We're here to help",value:"Mon-Fri: 9AM-6PM EST",action:"#"}],r=["Free 30-day trial with full features","Dedicated customer success manager","Free setup and training included","No long-term contracts required","24/7 technical support available","Custom integrations available"];return f.jsx("section",{id:"contact",className:"py-20 bg-gradient-to-br from-primary-50 via-white to-primary-50",children:f.jsx("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:f.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 lg:gap-20 items-start",children:[f.jsxs(D.div,{ref:e,initial:{opacity:0,x:-50},animate:t?{opacity:1,x:0}:{},transition:{duration:.8},children:[f.jsxs("div",{className:"mb-8",children:[f.jsxs("h2",{className:"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4",children:["Ready to Get",f.jsx("span",{className:"bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent",children:" Started?"})]}),f.jsx("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Transform your business with our professional POS system. Contact us today for a personalized demo and see how we can help you grow."})]}),f.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8",children:n.map((i,o)=>f.jsx(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{delay:.2+o*.1},children:f.jsxs(li,{className:"h-full hover:shadow-lg transition-shadow duration-300 cursor-pointer group",onClick:()=>i.action!=="#"&&window.open(i.action,"_blank"),children:[f.jsx(ls,{className:"pb-3",children:f.jsxs("div",{className:"flex items-center",children:[f.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-primary-200 transition-colors",children:f.jsx(i.icon,{className:"w-5 h-5 text-primary-600"})}),f.jsx(us,{className:"text-lg font-semibold text-gray-900",children:i.title})]})}),f.jsxs(ui,{children:[f.jsx("p",{className:"text-sm text-gray-600 mb-2",children:i.description}),f.jsx("p",{className:"font-medium text-gray-900",children:i.value})]})]})},i.title))}),f.jsxs(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{delay:.6},className:"bg-white rounded-xl p-6 shadow-sm border border-primary-100",children:[f.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"What You Get:"}),f.jsx("ul",{className:"space-y-3",children:r.map((i,o)=>f.jsxs(D.li,{initial:{opacity:0,x:-20},animate:t?{opacity:1,x:0}:{},transition:{delay:.7+o*.1},className:"flex items-start",children:[f.jsx(Aw,{className:"w-5 h-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0"}),f.jsx("span",{className:"text-gray-700",children:i})]},o))})]})]}),f.jsxs(D.div,{initial:{opacity:0,x:50},animate:t?{opacity:1,x:0}:{},transition:{duration:.8,delay:.2},className:"lg:sticky lg:top-8",children:[f.jsxs(li,{className:"shadow-xl border-0 bg-gradient-to-br from-primary-500 to-primary-600 text-white overflow-hidden",children:[f.jsx("div",{className:"absolute inset-0 bg-black/10"}),f.jsxs(ui,{className:"p-8 relative z-10",children:[f.jsxs("div",{className:"text-center mb-8",children:[f.jsx("h3",{className:"text-2xl font-bold mb-2",children:"Start Your Free Trial"}),f.jsx("p",{className:"text-primary-100",children:"Experience the full power of our POS system with a 30-day free trial"})]}),f.jsxs("div",{className:"space-y-6",children:[f.jsxs(nt,{size:"xl",onClick:()=>window.open("http://localhost:8080","_blank"),className:"w-full bg-white text-primary-600 hover:bg-gray-100 font-semibold",children:["Try Live Demo Now",f.jsx(bm,{className:"ml-2 w-5 h-5"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("p",{className:"text-primary-100 text-sm mb-4",children:"Or contact our sales team"}),f.jsxs(nt,{size:"lg",variant:"outline",className:"w-full border-2 border-white text-white hover:bg-white hover:text-primary-600",onClick:()=>window.open("mailto:<EMAIL>","_blank"),children:[f.jsx(Kw,{className:"mr-2 w-4 h-4"}),"Request Demo"]})]}),f.jsx("div",{className:"border-t border-primary-400 pt-6",children:f.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[f.jsxs("div",{children:[f.jsx("div",{className:"text-2xl font-bold",children:"30"}),f.jsx("div",{className:"text-xs text-primary-100",children:"Day Trial"})]}),f.jsxs("div",{children:[f.jsx("div",{className:"text-2xl font-bold",children:"24/7"}),f.jsx("div",{className:"text-xs text-primary-100",children:"Support"})]}),f.jsxs("div",{children:[f.jsx("div",{className:"text-2xl font-bold",children:"Free"}),f.jsx("div",{className:"text-xs text-primary-100",children:"Setup"})]})]})})]})]})]}),f.jsxs(D.div,{initial:{opacity:0,y:20},animate:t?{opacity:1,y:0}:{},transition:{delay:.8},className:"mt-6 text-center",children:[f.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"Trusted by businesses worldwide"}),f.jsx("div",{className:"flex justify-center items-center space-x-6 opacity-60",children:["🏪","🍕","☕","👕","📱","💊"].map((i,o)=>f.jsx(D.div,{initial:{opacity:0,scale:.8},animate:t?{opacity:.6,scale:1}:{},transition:{delay:.9+o*.1},className:"text-2xl",children:i},o))})]})]})]})})})},Z2=()=>{const e=()=>{window.scrollTo({top:0,behavior:"smooth"})},t={product:[{label:"Features",href:"#features"},{label:"Pricing",href:"#contact"},{label:"Live Demo",href:"http://localhost:8080"},{label:"Documentation",href:"#"}],company:[{label:"About Us",href:"#"},{label:"Careers",href:"#"},{label:"Blog",href:"#"},{label:"News",href:"#"}],support:[{label:"Help Center",href:"#"},{label:"Technical Support",href:"#contact"},{label:"System Status",href:"#"},{label:"Training",href:"#"}],legal:[{label:"Privacy Policy",href:"#"},{label:"Terms of Service",href:"#"},{label:"Cookie Policy",href:"#"},{label:"Data Protection",href:"#"}]},n=[{icon:Xw,href:"#",label:"Twitter"},{icon:_w,href:"#",label:"Facebook"},{icon:Bw,href:"#",label:"LinkedIn"},{icon:Iw,href:"#",label:"GitHub"}];return f.jsxs("footer",{className:"bg-gray-900 text-white relative overflow-hidden",children:[f.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[f.jsx("div",{className:"absolute top-0 left-1/4 w-64 h-64 bg-primary-500 rounded-full mix-blend-multiply filter blur-xl opacity-10"}),f.jsx("div",{className:"absolute bottom-0 right-1/4 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-10"})]}),f.jsxs("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10",children:[f.jsx("div",{className:"py-16",children:f.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-5 gap-8 lg:gap-12",children:[f.jsxs(D.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6},className:"lg:col-span-2",children:[f.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[f.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg",children:f.jsx(ss,{className:"w-7 h-7 text-white"})}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-2xl font-bold",children:"POS Pro Algeria"}),f.jsx("p",{className:"text-gray-400 text-sm",children:"Professional Point of Sale System"})]})]}),f.jsx("p",{className:"text-gray-300 mb-6 leading-relaxed",children:"The most advanced point of sale system designed for modern businesses in Algeria. Features partial payments, multi-language support, and comprehensive inventory management. Works completely offline with automatic cloud backup and synchronization."}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"flex items-center text-gray-300",children:[f.jsx(Em,{className:"w-5 h-5 mr-3 text-green-400"}),f.jsx("span",{children:"<EMAIL>"})]}),f.jsxs("div",{className:"flex items-center text-gray-300",children:[f.jsx(zo,{className:"w-5 h-5 mr-3 text-green-400"}),f.jsx("span",{children:"+213 (0) 21 123 456"})]}),f.jsxs("div",{className:"flex items-center text-gray-300",children:[f.jsx(zo,{className:"w-5 h-5 mr-3 text-green-400"}),f.jsx("span",{children:"+213 (0) 555 789 123"})]}),f.jsxs("div",{className:"flex items-center text-gray-300",children:[f.jsx(nl,{className:"w-5 h-5 mr-3 text-green-400"}),f.jsx("span",{children:"شارع ديدوش مراد، الجزائر العاصمة 16000"})]}),f.jsxs("div",{className:"flex items-center text-gray-300",children:[f.jsx(nl,{className:"w-5 h-5 mr-3 text-green-400"}),f.jsx("span",{children:"حي النصر، وهران 31000"})]})]})]}),Object.entries(t).map(([r,i],o)=>f.jsxs(D.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.1+o*.1},children:[f.jsx("h4",{className:"text-lg font-semibold mb-4 capitalize",children:r==="product"?"Product":r==="company"?"Company":r==="support"?"Support":"Legal"}),f.jsx("ul",{className:"space-y-3",children:i.map(s=>f.jsx("li",{children:f.jsx("button",{onClick:()=>{s.href.startsWith("#")?(s.href==="#features"||s.href==="#contact")&&Gn(s.href.substring(1)):s.href.startsWith("http")&&window.open(s.href,"_blank")},className:"text-gray-300 hover:text-primary-400 transition-colors duration-200 text-left",children:s.label})},s.label))})]},r))]})}),f.jsx("div",{className:"border-t border-gray-800 py-8",children:f.jsxs("div",{className:"flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0",children:[f.jsx(D.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6},className:"text-gray-400 text-sm",children:"© 2024 POS Pro Algeria. All rights reserved. Built with ❤️ using Flutter & React."}),f.jsx(D.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.2},className:"flex items-center space-x-4",children:n.map(r=>f.jsx("button",{onClick:()=>window.open(r.href,"_blank"),className:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-primary-400 hover:bg-gray-700 transition-all duration-200","aria-label":r.label,children:f.jsx(r.icon,{className:"w-5 h-5"})},r.label))}),f.jsx(D.div,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.6,delay:.4},children:f.jsxs(nt,{variant:"outline",size:"sm",onClick:e,className:"border-gray-700 text-gray-300 hover:bg-primary-500 hover:border-primary-500 hover:text-white",children:[f.jsx(Nw,{className:"w-4 h-4 mr-2"}),"Back to Top"]})})]})}),f.jsx(D.div,{initial:{opacity:0,y:10},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"border-t border-gray-800 py-6",children:f.jsxs("div",{className:"text-center",children:[f.jsx("p",{className:"text-gray-500 text-sm mb-4",children:"This POS system supports multiple languages (Arabic RTL, English, French, Spanish, German) and multiple currencies (Algerian Dinar, USD, EUR, GBP, CAD)"}),f.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-gray-600",children:[f.jsx("span",{className:"flex items-center justify-center",children:"🌍 Multi-Language"}),f.jsx("span",{className:"flex items-center justify-center",children:"💰 Multi-Currency"}),f.jsx("span",{className:"flex items-center justify-center",children:"🖨️ Thermal Printing"}),f.jsx("span",{className:"flex items-center justify-center",children:"📱 Cross-Platform"}),f.jsx("span",{className:"flex items-center justify-center",children:"☁️ Cloud Backup"})]}),f.jsx("div",{className:"mt-4 pt-4 border-t border-gray-800",children:f.jsx("p",{className:"text-gray-600 text-xs",children:"🇩🇿 Designed specifically for the Algerian market | Supports Algerian Dinar | Compliant with local regulations"})})]})})]})]})};function q2(){return f.jsxs("div",{className:"min-h-screen bg-white",children:[f.jsx(_2,{}),f.jsxs("main",{children:[f.jsx(O2,{}),f.jsx(H2,{}),f.jsx(Q2,{}),f.jsx(Y2,{}),f.jsx(X2,{})]}),f.jsx(Z2,{})]})}Ys.createRoot(document.getElementById("root")).render(f.jsx(Br.StrictMode,{children:f.jsx(q2,{})}));
