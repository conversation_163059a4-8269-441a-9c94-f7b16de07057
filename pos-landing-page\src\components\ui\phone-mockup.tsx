import React from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface PhoneMockupProps {
  screenshot: string
  alt: string
  className?: string
  variant?: 'default' | 'floating' | 'tilted'
  showReflection?: boolean
  animate?: boolean
}

const PhoneMockup: React.FC<PhoneMockupProps> = ({
  screenshot,
  alt,
  className,
  variant = 'default',
  showReflection = true,
  animate = true,
}) => {
  const baseClasses = "relative select-none"
  const variantClasses = {
    default: "",
    floating: "animate-float",
    tilted: "transform rotate-3 hover:rotate-0 transition-transform duration-500",
  }

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)}>
      {/* Phone Container with Shadow */}
      <motion.div
        initial={animate ? { opacity: 0, scale: 0.8 } : {}}
        animate={animate ? { opacity: 1, scale: 1 } : {}}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative group"
      >
        {/* Drop Shadow */}
        <div className="absolute inset-0 bg-black/20 blur-2xl transform translate-y-8 scale-95 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
        
        {/* Phone Frame */}
        <div className="relative bg-black rounded-[3rem] p-2 shadow-2xl group-hover:shadow-3xl transition-shadow duration-300">
          {/* Screen Glow Effect */}
          <div className="absolute inset-2 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-[2.5rem] opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
          
          {/* Screen Container */}
          <div className="relative bg-black rounded-[2.5rem] overflow-hidden">
            {/* Dynamic Island (iPhone 14/15 style) */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black rounded-full z-10" />
            
            {/* Screen Content */}
            <div className="relative w-[280px] h-[600px] bg-white rounded-[2.5rem] overflow-hidden">
              {/* Screenshot */}
              <img
                src={screenshot}
                alt={alt}
                className="w-full h-full object-cover object-top"
                loading="lazy"
                onError={(e) => {
                  // Fallback to placeholder if screenshot fails to load
                  const target = e.target as HTMLImageElement;
                  target.src = '/api/placeholder/280/600';
                }}
              />
              
              {/* Screen Overlay for Realism */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 pointer-events-none" />
              
              {/* Screen Reflection */}
              {showReflection && (
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent pointer-events-none opacity-30" />
              )}
            </div>
          </div>
          
          {/* Side Buttons */}
          <div className="absolute left-[-2px] top-20 w-1 h-8 bg-gray-800 rounded-l-sm" />
          <div className="absolute left-[-2px] top-32 w-1 h-12 bg-gray-800 rounded-l-sm" />
          <div className="absolute left-[-2px] top-48 w-1 h-12 bg-gray-800 rounded-l-sm" />
          <div className="absolute right-[-2px] top-24 w-1 h-16 bg-gray-800 rounded-r-sm" />
        </div>

        {/* Floating Elements */}
        <motion.div
          animate={animate ? { y: [0, -10, 0] } : {}}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          className="absolute -top-4 -left-4 w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg z-10"
        >
          <span className="text-white text-xs font-bold">POS</span>
        </motion.div>

        <motion.div
          animate={animate ? { y: [0, 10, 0] } : {}}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          className="absolute -bottom-4 -right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg border-2 border-green-200 z-10"
        >
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
        </motion.div>
      </motion.div>

      {/* Reflection Effect */}
      {showReflection && (
        <div className="absolute top-full left-0 w-full h-32 opacity-20 pointer-events-none">
          <div 
            className="w-full h-full bg-gradient-to-b from-gray-900 to-transparent transform scale-y-[-1] blur-sm"
            style={{
              maskImage: 'linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 70%)',
              WebkitMaskImage: 'linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 70%)',
            }}
          />
        </div>
      )}
    </div>
  )
}

export default PhoneMockup
