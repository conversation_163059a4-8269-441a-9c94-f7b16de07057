import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../providers/locale_provider.dart';
import '../providers/currency_provider.dart';

class InvoiceDetailsScreen extends StatelessWidget {
  final Invoice invoice;

  const InvoiceDetailsScreen({super.key, required this.invoice});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocaleProvider>(
          builder: (context, locale, _) => Text(
            locale.isRTL ? 'تفاصيل الفاتورة' : 'Invoice Details',
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(FontAwesomeIcons.print),
            onPressed: () => _printInvoice(context),
          ),
          IconButton(
            icon: const Icon(FontAwesomeIcons.share),
            onPressed: () => _shareInvoice(context),
          ),
        ],
      ),
      body: Consumer2<LocaleProvider, CurrencyProvider>(
        builder: (context, locale, currency, _) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInvoiceHeader(context, locale, currency),
                const SizedBox(height: 24),
                _buildCustomerInfo(context, locale),
                const SizedBox(height: 24),
                _buildItemsList(context, locale, currency),
                const SizedBox(height: 24),
                _buildTotals(context, locale, currency),
                const SizedBox(height: 24),
                _buildPaymentInfo(context, locale, currency),
                if (invoice.notes?.isNotEmpty == true) ...[
                  const SizedBox(height: 24),
                  _buildNotes(context, locale),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInvoiceHeader(BuildContext context, LocaleProvider locale, CurrencyProvider currency) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  locale.isRTL ? 'رقم الفاتورة' : 'Invoice Number',
                  style: theme.textTheme.titleMedium,
                ),
                Text(
                  invoice.invoiceNumber,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  locale.isRTL ? 'التاريخ' : 'Date',
                  style: theme.textTheme.bodyMedium,
                ),
                Text(
                  DateFormat('dd/MM/yyyy HH:mm').format(invoice.date),
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  locale.isRTL ? 'الحالة' : 'Status',
                  style: theme.textTheme.bodyMedium,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(invoice.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(invoice.status, locale.isRTL),
                    style: TextStyle(
                      color: _getStatusColor(invoice.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerInfo(BuildContext context, LocaleProvider locale) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              locale.isRTL ? 'معلومات العميل' : 'Customer Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(FontAwesomeIcons.user, color: theme.colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  invoice.customerName ?? (locale.isRTL ? 'عميل نقدي' : 'Cash Customer'),
                  style: theme.textTheme.bodyLarge,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList(BuildContext context, LocaleProvider locale, CurrencyProvider currency) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              locale.isRTL ? 'عناصر الفاتورة' : 'Invoice Items',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...invoice.items.map((item) => _buildItemRow(context, item, locale, currency)),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRow(BuildContext context, InvoiceItem item, LocaleProvider locale, CurrencyProvider currency) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              item.productName,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Expanded(
            child: Text(
              '${item.quantity.toInt()}',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              currency.formatCurrency(item.unitPrice),
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.end,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              currency.formatCurrency(item.totalPrice),
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotals(BuildContext context, LocaleProvider locale, CurrencyProvider currency) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildTotalRow(
              locale.isRTL ? 'المجموع الفرعي' : 'Subtotal',
              currency.formatCurrency(invoice.totalAmount),
              theme,
            ),
            if (invoice.discountAmount > 0)
              _buildTotalRow(
                locale.isRTL ? 'الخصم' : 'Discount',
                '- ${currency.formatCurrency(invoice.discountAmount)}',
                theme,
                color: Colors.red,
              ),
            if (invoice.taxAmount > 0)
              _buildTotalRow(
                locale.isRTL ? 'الضريبة' : 'Tax',
                currency.formatCurrency(invoice.taxAmount),
                theme,
              ),
            const Divider(),
            _buildTotalRow(
              locale.isRTL ? 'المجموع النهائي' : 'Total',
              currency.formatCurrency(invoice.finalAmount),
              theme,
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, String value, ThemeData theme, {Color? color, bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal
                ? theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)
                : theme.textTheme.bodyMedium,
          ),
          Text(
            value,
            style: isTotal
                ? theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color ?? theme.colorScheme.primary,
                  )
                : theme.textTheme.bodyMedium?.copyWith(color: color),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo(BuildContext context, LocaleProvider locale, CurrencyProvider currency) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              locale.isRTL ? 'معلومات الدفع' : 'Payment Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  locale.isRTL ? 'طريقة الدفع' : 'Payment Method',
                  style: theme.textTheme.bodyMedium,
                ),
                Text(
                  _getPaymentMethodText(invoice.paymentMethod, locale.isRTL),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotes(BuildContext context, LocaleProvider locale) {
    final theme = Theme.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              locale.isRTL ? 'ملاحظات' : 'Notes',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              invoice.notes!,
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'partial':
        return Colors.orange;
      case 'unpaid':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status, bool isRTL) {
    switch (status.toLowerCase()) {
      case 'paid':
        return isRTL ? 'مدفوعة' : 'Paid';
      case 'partial':
        return isRTL ? 'مدفوعة جزئياً' : 'Partially Paid';
      case 'unpaid':
        return isRTL ? 'غير مدفوعة' : 'Unpaid';
      default:
        return status;
    }
  }

  String _getPaymentMethodText(String method, bool isRTL) {
    switch (method.toLowerCase()) {
      case 'cash':
        return isRTL ? 'نقدي' : 'Cash';
      case 'card':
        return isRTL ? 'بطاقة' : 'Card';
      case 'transfer':
        return isRTL ? 'تحويل' : 'Transfer';
      default:
        return method;
    }
  }

  void _printInvoice(BuildContext context) {
    // TODO: Implement print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Print functionality will be implemented')),
    );
  }

  void _shareInvoice(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share functionality will be implemented')),
    );
  }
}
