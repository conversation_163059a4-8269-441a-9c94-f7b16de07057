import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/store_settings_provider.dart';
import '../providers/localization_provider.dart';

class StoreSettingsScreen extends StatefulWidget {
  const StoreSettingsScreen({super.key});

  @override
  State<StoreSettingsScreen> createState() => _StoreSettingsScreenState();
}

class _StoreSettingsScreenState extends State<StoreSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _addressController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _websiteController;
  late TextEditingController _taxIdController;
  late TextEditingController _descriptionController;

  @override
  void initState() {
    super.initState();
    final storeSettings = Provider.of<StoreSettingsProvider>(context, listen: false);
    _nameController = TextEditingController(text: storeSettings.storeInfo.name);
    _addressController = TextEditingController(text: storeSettings.storeInfo.address);
    _phoneController = TextEditingController(text: storeSettings.storeInfo.phone);
    _emailController = TextEditingController(text: storeSettings.storeInfo.email);
    _websiteController = TextEditingController(text: storeSettings.storeInfo.website);
    _taxIdController = TextEditingController(text: storeSettings.storeInfo.taxId);
    _descriptionController = TextEditingController(text: storeSettings.storeInfo.description);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _taxIdController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocalizationProvider>(
          builder: (context, localization, _) => Text(
            localization.translate('store_name'),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveStoreInfo,
          ),
        ],
      ),
      body: Consumer<StoreSettingsProvider>(
        builder: (context, storeSettings, _) => Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Logo Section
              _buildLogoSection(storeSettings),
              
              const SizedBox(height: 24),
              
              // Basic Information
              _buildSectionHeader('Basic Information'),
              _buildTextField(
                controller: _nameController,
                label: 'Store Name',
                icon: FontAwesomeIcons.store,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Store name is required';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _addressController,
                label: 'Address',
                icon: FontAwesomeIcons.locationDot,
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Address is required';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _phoneController,
                label: 'Phone Number',
                icon: FontAwesomeIcons.phone,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Phone number is required';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 24),
              
              // Contact Information
              _buildSectionHeader('Contact Information'),
              _buildTextField(
                controller: _emailController,
                label: 'Email',
                icon: FontAwesomeIcons.envelope,
                keyboardType: TextInputType.emailAddress,
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _websiteController,
                label: 'Website',
                icon: FontAwesomeIcons.globe,
                keyboardType: TextInputType.url,
              ),
              
              const SizedBox(height: 24),
              
              // Business Information
              _buildSectionHeader('Business Information'),
              _buildTextField(
                controller: _taxIdController,
                label: 'Tax ID / Business Registration',
                icon: FontAwesomeIcons.fileInvoice,
              ),
              
              const SizedBox(height: 16),
              
              _buildTextField(
                controller: _descriptionController,
                label: 'Description',
                icon: FontAwesomeIcons.alignLeft,
                maxLines: 3,
              ),
              
              const SizedBox(height: 32),
              
              // Receipt Settings Section
              _buildReceiptSettingsSection(storeSettings),
              
              const SizedBox(height: 32),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveStoreInfo,
                      child: const Text('Save Changes'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _resetForm,
                      child: const Text('Reset'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoSection(StoreSettingsProvider storeSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'Store Logo',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GestureDetector(
              onTap: () => _showLogoOptions(storeSettings),
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: storeSettings.hasLogo
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          File(storeSettings.storeInfo.logoPath),
                          fit: BoxFit.cover,
                        ),
                      )
                    : const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add_photo_alternate, size: 40),
                          SizedBox(height: 8),
                          Text('Add Logo'),
                        ],
                      ),
              ),
            ),
            if (storeSettings.hasLogo) ...[
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: () => storeSettings.removeStoreLogo(),
                icon: const Icon(Icons.delete, color: Colors.red),
                label: const Text('Remove Logo', style: TextStyle(color: Colors.red)),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,
    );
  }

  Widget _buildReceiptSettingsSection(StoreSettingsProvider storeSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Receipt Settings',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Show Logo on Receipt'),
              value: storeSettings.receiptSettings.showLogo,
              onChanged: (value) {
                storeSettings.updateReceiptSettings(showLogo: value);
              },
            ),
            SwitchListTile(
              title: const Text('Show Store Info on Receipt'),
              value: storeSettings.receiptSettings.showStoreInfo,
              onChanged: (value) {
                storeSettings.updateReceiptSettings(showStoreInfo: value);
              },
            ),
            SwitchListTile(
              title: const Text('Enable Tax'),
              value: storeSettings.receiptSettings.enableTax,
              onChanged: (value) {
                storeSettings.updateReceiptSettings(enableTax: value);
              },
            ),
            if (storeSettings.receiptSettings.enableTax) ...[
              const SizedBox(height: 8),
              TextFormField(
                initialValue: storeSettings.receiptSettings.taxRate.toString(),
                decoration: const InputDecoration(
                  labelText: 'Tax Rate (%)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final rate = double.tryParse(value) ?? 0.0;
                  storeSettings.updateReceiptSettings(taxRate: rate);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showLogoOptions(StoreSettingsProvider storeSettings) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              onTap: () {
                Navigator.pop(context);
                storeSettings.updateStoreLogo(source: ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              onTap: () {
                Navigator.pop(context);
                storeSettings.updateStoreLogo(source: ImageSource.gallery);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _saveStoreInfo() {
    if (_formKey.currentState!.validate()) {
      final storeSettings = Provider.of<StoreSettingsProvider>(context, listen: false);
      storeSettings.updateStoreInfo(
        name: _nameController.text,
        address: _addressController.text,
        phone: _phoneController.text,
        email: _emailController.text,
        website: _websiteController.text,
        taxId: _taxIdController.text,
        description: _descriptionController.text,
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Store information saved successfully')),
      );
    }
  }

  void _resetForm() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Form'),
        content: const Text('Are you sure you want to reset all changes?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final storeSettings = Provider.of<StoreSettingsProvider>(context, listen: false);
              setState(() {
                _nameController.text = storeSettings.storeInfo.name;
                _addressController.text = storeSettings.storeInfo.address;
                _phoneController.text = storeSettings.storeInfo.phone;
                _emailController.text = storeSettings.storeInfo.email;
                _websiteController.text = storeSettings.storeInfo.website;
                _taxIdController.text = storeSettings.storeInfo.taxId;
                _descriptionController.text = storeSettings.storeInfo.description;
              });
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }
}
