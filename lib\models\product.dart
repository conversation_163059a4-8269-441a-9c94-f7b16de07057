class Product {
  final int? id;
  final String name;
  final String barcode;
  final double price; // Retail price
  final double costPrice; // Purchase cost price
  final double wholesalePrice; // Price for wholesale
  final bool enableWholesale; // Whether wholesale is enabled
  final int minWholesaleQty; // Minimum quantity for wholesale
  final int stock;
  final String category;
  final String? imageUrl;
  final DateTime? expirationDate; // Product expiration date
  final bool hasExpiration; // Whether product has expiration date

  Product({
    this.id,
    required this.name,
    required this.barcode,
    required this.price,
    required this.costPrice,
    required this.wholesalePrice,
    required this.enableWholesale,
    required this.minWholesaleQty,
    required this.stock,
    required this.category,
    this.imageUrl,
    this.expirationDate,
    this.hasExpiration = false,
  });

  // Calculate profit margin amount
  double get profitMargin => price - costPrice;

  // Calculate profit margin percentage
  double get profitMarginPercent =>
      costPrice > 0 ? ((price - costPrice) / costPrice) * 100 : 0;

  // Calculate wholesale profit margin amount
  double get wholesaleProfitMargin => wholesalePrice - costPrice;

  // Calculate wholesale profit margin percentage
  double get wholesaleProfitMarginPercent =>
      costPrice > 0 ? ((wholesalePrice - costPrice) / costPrice) * 100 : 0;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'barcode': barcode,
      'price': price,
      'costPrice': costPrice,
      'wholesalePrice': wholesalePrice,
      'enableWholesale': enableWholesale ? 1 : 0,
      'minWholesaleQty': minWholesaleQty,
      'stock': stock,
      'category': category,
      'imageUrl': imageUrl,
      'expirationDate': expirationDate?.toIso8601String(),
      'hasExpiration': hasExpiration ? 1 : 0,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id'],
      name: map['name'],
      barcode: map['barcode'],
      price: map['price'],
      costPrice: map['costPrice'],
      wholesalePrice: map['wholesalePrice'],
      enableWholesale: map['enableWholesale'] == 1,
      minWholesaleQty: map['minWholesaleQty'],
      stock: map['stock'],
      category: map['category'],
      imageUrl: map['imageUrl'],
      expirationDate:
          map['expirationDate'] != null
              ? DateTime.parse(map['expirationDate'])
              : null,
      hasExpiration: map['hasExpiration'] == 1,
    );
  }

  Product copyWith({
    int? id,
    String? name,
    String? barcode,
    double? price,
    double? costPrice,
    double? wholesalePrice,
    bool? enableWholesale,
    int? minWholesaleQty,
    int? stock,
    String? category,
    String? imageUrl,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      barcode: barcode ?? this.barcode,
      price: price ?? this.price,
      costPrice: costPrice ?? this.costPrice,
      wholesalePrice: wholesalePrice ?? this.wholesalePrice,
      enableWholesale: enableWholesale ?? this.enableWholesale,
      minWholesaleQty: minWholesaleQty ?? this.minWholesaleQty,
      stock: stock ?? this.stock,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  // JSON serialization methods for cart persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'barcode': barcode,
      'price': price,
      'costPrice': costPrice,
      'wholesalePrice': wholesalePrice,
      'enableWholesale': enableWholesale,
      'minWholesaleQty': minWholesaleQty,
      'stock': stock,
      'category': category,
      'imageUrl': imageUrl,
      'expirationDate': expirationDate?.toIso8601String(),
      'hasExpiration': hasExpiration,
    };
  }

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      barcode: json['barcode'],
      price: json['price'].toDouble(),
      costPrice: json['costPrice'].toDouble(),
      wholesalePrice: json['wholesalePrice'].toDouble(),
      enableWholesale: json['enableWholesale'],
      minWholesaleQty: json['minWholesaleQty'],
      stock: json['stock'],
      category: json['category'],
      imageUrl: json['imageUrl'],
      expirationDate:
          json['expirationDate'] != null
              ? DateTime.parse(json['expirationDate'])
              : null,
      hasExpiration: json['hasExpiration'],
    );
  }
}
