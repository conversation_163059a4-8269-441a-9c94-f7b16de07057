import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/locale_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/session_provider.dart';
import '../providers/store_settings_provider.dart';
import '../providers/currency_provider.dart';

import '../providers/notification_provider.dart';
import '../widgets/bluetooth_scan_dialog.dart';
import 'auth/login_screen.dart';
import 'store_settings_screen.dart';
import 'backup_settings_screen.dart';

class ResponsiveSettingsScreen extends StatefulWidget {
  const ResponsiveSettingsScreen({super.key});

  @override
  State<ResponsiveSettingsScreen> createState() =>
      _ResponsiveSettingsScreenState();
}

class _ResponsiveSettingsScreenState extends State<ResponsiveSettingsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  // المتغيرات المحلية تم استبدالها بالـ providers

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title:
            _searchQuery.isEmpty
                ? Text(
                  isRTL ? 'الإعدادات' : 'Settings',
                  style: GoogleFonts.cairo(
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 24 : 20,
                  ),
                )
                : TextField(
                  controller: _searchController,
                  style: GoogleFonts.cairo(color: Colors.white),
                  decoration: InputDecoration(
                    hintText:
                        isRTL ? 'البحث في الإعدادات...' : 'Search settings...',
                    hintStyle: GoogleFonts.cairo(color: Colors.white70),
                    border: InputBorder.none,
                  ),
                  onChanged: (value) => setState(() => _searchQuery = value),
                ),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_searchQuery.isEmpty ? Icons.search : Icons.close),
            onPressed: () {
              setState(() {
                if (_searchQuery.isEmpty) {
                  _searchQuery = 'searching';
                } else {
                  _searchQuery = '';
                  _searchController.clear();
                }
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'reset') {
                _showResetDialog(
                  context,
                  Provider.of<LocaleProvider>(context, listen: false).isRTL,
                );
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'reset',
                    child: Row(
                      children: [
                        const Icon(Icons.restore, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          Provider.of<LocaleProvider>(
                                context,
                                listen: false,
                              ).isRTL
                              ? 'إعادة تعيين الإعدادات'
                              : 'Reset Settings',
                          style: GoogleFonts.cairo(),
                        ),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: GoogleFonts.cairo(
            fontSize: isTablet ? 14 : 12,
            fontWeight: FontWeight.w500,
          ),
          tabs: [
            Tab(
              icon: const FaIcon(FontAwesomeIcons.gear, size: 18),
              text: isRTL ? 'عام' : 'General',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.print, size: 18),
              text: isRTL ? 'طباعة' : 'Printing',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.receipt, size: 18),
              text: isRTL ? 'فواتير' : 'Invoices',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.shield, size: 18),
              text: isRTL ? 'أمان' : 'Security',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGeneralSettings(context, isRTL, isTablet),
          _buildPrintingSettings(context, isRTL, isTablet),
          _buildInvoiceSettings(context, isRTL, isTablet),
          _buildSecuritySettings(context, isRTL, isTablet),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings(
    BuildContext context,
    bool isRTL,
    bool isTablet,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            context,
            isRTL ? 'الإعدادات العامة' : 'General Settings',
            FontAwesomeIcons.sliders,
            isTablet,
          ),
          const SizedBox(height: 16),

          _buildSettingsCard(
            context,
            children: [
              Consumer<ThemeProvider>(
                builder:
                    (context, themeProvider, _) => _buildSwitchTile(
                      context,
                      title: isRTL ? 'الوضع المظلم' : 'Dark Mode',
                      subtitle:
                          isRTL ? 'تفعيل المظهر المظلم' : 'Enable dark theme',
                      icon: FontAwesomeIcons.moon,
                      value: themeProvider.isDarkMode,
                      onChanged: (value) => themeProvider.toggleTheme(),
                      isTablet: isTablet,
                    ),
              ),
              const Divider(height: 1),
              Consumer<NotificationProvider>(
                builder:
                    (context, notificationProvider, _) => _buildSwitchTile(
                      context,
                      title: isRTL ? 'الإشعارات' : 'Notifications',
                      subtitle:
                          isRTL
                              ? 'تلقي إشعارات التطبيق'
                              : 'Receive app notifications',
                      icon: FontAwesomeIcons.bell,
                      value: notificationProvider.systemAlertsEnabled,
                      onChanged:
                          (value) => notificationProvider
                              .setSystemAlertsEnabled(value),
                      isTablet: isTablet,
                    ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          _buildSectionHeader(
            context,
            isRTL ? 'اللغة والعملة' : 'Language & Currency',
            FontAwesomeIcons.globe,
            isTablet,
          ),
          const SizedBox(height: 16),

          _buildSettingsCard(
            context,
            children: [
              Consumer<LocaleProvider>(
                builder:
                    (context, localeProvider, _) => _buildDropdownTile(
                      context,
                      title: isRTL ? 'اللغة' : 'Language',
                      subtitle: _getLanguageName(
                        localeProvider.currentLanguage,
                      ),
                      icon: FontAwesomeIcons.language,
                      items:
                          SupportedLanguage.values
                              .map((lang) => _getLanguageName(lang))
                              .toList(),
                      value: _getLanguageName(localeProvider.currentLanguage),
                      onChanged: (value) {
                        final language = SupportedLanguage.values.firstWhere(
                          (lang) => _getLanguageName(lang) == value,
                        );
                        localeProvider.setLanguage(language);
                      },
                      isTablet: isTablet,
                    ),
              ),
              const Divider(height: 1),
              Consumer<CurrencyProvider>(
                builder:
                    (context, currencyProvider, _) => _buildDropdownTile(
                      context,
                      title: isRTL ? 'العملة' : 'Currency',
                      subtitle: currencyProvider.getCurrencyName(
                        isRTL ? 'ar' : 'en',
                      ),
                      icon: FontAwesomeIcons.coins,
                      items:
                          SupportedCurrency.values.map((currency) {
                            final info =
                                currencyProvider.allCurrencies[currency]!;
                            return '${info.nameEn} (${info.code})';
                          }).toList(),
                      value: () {
                        final info =
                            currencyProvider.allCurrencies[currencyProvider
                                .currentCurrency]!;
                        return '${info.nameEn} (${info.code})';
                      }(),
                      onChanged: (value) {
                        final currency = SupportedCurrency.values.firstWhere((
                          curr,
                        ) {
                          final info = currencyProvider.allCurrencies[curr]!;
                          return '${info.nameEn} (${info.code})' == value;
                        });
                        currencyProvider.setCurrency(currency);
                      },
                      isTablet: isTablet,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrintingSettings(
    BuildContext context,
    bool isRTL,
    bool isTablet,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            context,
            isRTL ? 'إعدادات الطباعة' : 'Printing Settings',
            FontAwesomeIcons.print,
            isTablet,
          ),
          const SizedBox(height: 16),

          // Bluetooth Printer Section
          _buildSectionHeader(
            context,
            isRTL ? 'طابعة البلوتوث' : 'Bluetooth Printer',
            FontAwesomeIcons.bluetooth,
            isTablet,
          ),
          const SizedBox(height: 16),

          Consumer<BluetoothProvider>(
            builder:
                (context, bluetoothProvider, _) => _buildSettingsCard(
                  context,
                  children: [
                    _buildBluetoothStatusTile(
                      context,
                      bluetoothProvider,
                      isRTL,
                      isTablet,
                    ),
                    if (bluetoothProvider.selectedPrinter != null) ...[
                      const Divider(height: 1),
                      _buildSelectedPrinterTile(
                        context,
                        bluetoothProvider,
                        isRTL,
                        isTablet,
                      ),
                    ],
                    const Divider(height: 1),
                    _buildBluetoothActionTile(
                      context,
                      bluetoothProvider,
                      isRTL,
                      isTablet,
                    ),
                  ],
                ),
          ),

          const SizedBox(height: 24),

          _buildSectionHeader(
            context,
            isRTL ? 'إعدادات عامة' : 'General Settings',
            FontAwesomeIcons.gear,
            isTablet,
          ),
          const SizedBox(height: 16),

          Consumer<StoreSettingsProvider>(
            builder:
                (context, storeSettings, _) => _buildSettingsCard(
                  context,
                  children: [
                    _buildDropdownTile(
                      context,
                      title: isRTL ? 'حجم الورق' : 'Paper Size',
                      subtitle: '${storeSettings.receiptSettings.paperWidth}mm',
                      icon: FontAwesomeIcons.fileLines,
                      items: ['80mm', '58mm'],
                      value: '${storeSettings.receiptSettings.paperWidth}mm',
                      onChanged: (value) {
                        final width = int.parse(value!.replaceAll('mm', ''));
                        storeSettings.updateReceiptSettings(paperWidth: width);
                      },
                      isTablet: isTablet,
                    ),
                  ],
                ),
          ),

          const SizedBox(height: 24),

          _buildSectionHeader(
            context,
            isRTL ? 'تنسيق الطباعة' : 'Print Format',
            FontAwesomeIcons.alignLeft,
            isTablet,
          ),
          const SizedBox(height: 16),

          Consumer<StoreSettingsProvider>(
            builder:
                (context, storeSettings, _) => _buildSettingsCard(
                  context,
                  children: [
                    _buildSliderTile(
                      context,
                      title: isRTL ? 'حجم الخط' : 'Font Size',
                      subtitle:
                          '${storeSettings.receiptSettings.fontSize.toInt()}pt',
                      icon: FontAwesomeIcons.textHeight,
                      value: storeSettings.receiptSettings.fontSize,
                      min: 8.0,
                      max: 20.0,
                      onChanged:
                          (value) => storeSettings.updateReceiptSettings(
                            fontSize: value,
                          ),
                      isTablet: isTablet,
                    ),
                  ],
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceSettings(
    BuildContext context,
    bool isRTL,
    bool isTablet,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            context,
            isRTL ? 'إعدادات الفواتير' : 'Invoice Settings',
            FontAwesomeIcons.receipt,
            isTablet,
          ),
          const SizedBox(height: 16),

          Consumer<StoreSettingsProvider>(
            builder:
                (context, storeSettings, _) => _buildSettingsCard(
                  context,
                  children: [
                    _buildSwitchTile(
                      context,
                      title: isRTL ? 'إظهار الشعار' : 'Show Logo',
                      subtitle:
                          isRTL
                              ? 'عرض شعار الشركة في الفاتورة'
                              : 'Display company logo on invoice',
                      icon: FontAwesomeIcons.image,
                      value: storeSettings.receiptSettings.showLogo,
                      onChanged:
                          (value) => storeSettings.updateReceiptSettings(
                            showLogo: value,
                          ),
                      isTablet: isTablet,
                    ),
                    const Divider(height: 1),
                    _buildSwitchTile(
                      context,
                      title: isRTL ? 'معلومات الشركة' : 'Business Info',
                      subtitle:
                          isRTL
                              ? 'عرض تفاصيل الشركة'
                              : 'Display business details',
                      icon: FontAwesomeIcons.building,
                      value: storeSettings.receiptSettings.showStoreInfo,
                      onChanged:
                          (value) => storeSettings.updateReceiptSettings(
                            showStoreInfo: value,
                          ),
                      isTablet: isTablet,
                    ),
                  ],
                ),
          ),

          const SizedBox(height: 24),

          _buildSectionHeader(
            context,
            isRTL ? 'تخصيص الفاتورة' : 'Invoice Customization',
            FontAwesomeIcons.paintbrush,
            isTablet,
          ),
          const SizedBox(height: 16),

          Consumer<StoreSettingsProvider>(
            builder:
                (context, storeSettings, _) => _buildSettingsCard(
                  context,
                  children: [
                    _buildTextFieldTile(
                      context,
                      title: isRTL ? 'نص التذييل' : 'Footer Text',
                      subtitle:
                          isRTL
                              ? 'النص الذي يظهر في أسفل الفاتورة'
                              : 'Text shown at bottom of invoice',
                      icon: FontAwesomeIcons.alignCenter,
                      value: storeSettings.receiptSettings.footerMessage,
                      onChanged:
                          (value) => storeSettings.updateReceiptSettings(
                            footerMessage: value,
                          ),
                      isTablet: isTablet,
                    ),
                  ],
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettings(
    BuildContext context,
    bool isRTL,
    bool isTablet,
  ) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            context,
            isRTL ? 'الأمان والخصوصية' : 'Security & Privacy',
            FontAwesomeIcons.shield,
            isTablet,
          ),
          const SizedBox(height: 16),

          _buildSettingsCard(
            context,
            children: [
              _buildActionTile(
                context,
                title: isRTL ? 'تغيير كلمة المرور' : 'Change Password',
                subtitle:
                    isRTL
                        ? 'تحديث كلمة مرور الحساب'
                        : 'Update account password',
                icon: FontAwesomeIcons.lock,
                onTap: () => _showChangePasswordDialog(context, isRTL),
                isTablet: isTablet,
              ),
              const Divider(height: 1),
              _buildActionTile(
                context,
                title: isRTL ? 'معلومات المتجر' : 'Store Information',
                subtitle:
                    isRTL
                        ? 'تحديث معلومات المتجر والشركة'
                        : 'Update store and business information',
                icon: FontAwesomeIcons.store,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const StoreSettingsScreen(),
                    ),
                  );
                },
                isTablet: isTablet,
              ),
              const Divider(height: 1),
              _buildActionTile(
                context,
                title: isRTL ? 'نسخ احتياطي' : 'Backup Data',
                subtitle:
                    isRTL
                        ? 'إنشاء نسخة احتياطية من البيانات'
                        : 'Create backup of your data',
                icon: FontAwesomeIcons.cloudArrowUp,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const BackupSettingsScreen(),
                    ),
                  );
                },
                isTablet: isTablet,
              ),
              const Divider(height: 1),
              _buildLogoutTile(context, isRTL, isTablet),
            ],
          ),
        ],
      ),
    );
  }

  String _getLanguageName(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.arabic:
        return 'العربية';
      case SupportedLanguage.english:
        return 'English';
      case SupportedLanguage.french:
        return 'Français';
      case SupportedLanguage.spanish:
        return 'Español';
      case SupportedLanguage.german:
        return 'Deutsch';
    }
  }

  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
    bool isTablet,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(
            icon,
            color: theme.colorScheme.primary,
            size: isTablet ? 20 : 18,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: GoogleFonts.cairo(
            fontSize: isTablet ? 18 : 16,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsCard(
    BuildContext context, {
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(children: children),
    );
  }

  Widget _buildSwitchTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
    required bool isTablet,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        icon,
        color: theme.colorScheme.primary,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 14 : 12,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: theme.colorScheme.primary,
      ),
    );
  }

  Widget _buildDropdownTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required List<String> items,
    required String value,
    required ValueChanged<String?> onChanged,
    required bool isTablet,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        icon,
        color: theme.colorScheme.primary,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 14 : 12,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        items:
            items.map((String item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(
                  item,
                  style: GoogleFonts.cairo(fontSize: isTablet ? 14 : 12),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildActionTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required bool isTablet,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        icon,
        color: isDestructive ? Colors.red : theme.colorScheme.primary,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
          color: isDestructive ? Colors.red : null,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 14 : 12,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: isTablet ? 18 : 16,
        color:
            isDestructive
                ? Colors.red.withValues(alpha: 0.7)
                : theme.colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }

  Widget _buildSliderTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required double value,
    required double min,
    required double max,
    required ValueChanged<double> onChanged,
    required bool isTablet,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        icon,
        color: theme.colorScheme.primary,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: isTablet ? 14 : 12,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: (max - min).toInt(),
            onChanged: onChanged,
            activeColor: theme.colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildTextFieldTile(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required String value,
    required ValueChanged<String> onChanged,
    required bool isTablet,
  }) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        icon,
        color: theme.colorScheme.primary,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: GoogleFonts.cairo(
              fontSize: isTablet ? 14 : 12,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: TextEditingController(text: value),
            onChanged: onChanged,
            style: GoogleFonts.cairo(fontSize: isTablet ? 14 : 12),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog(BuildContext context, bool isRTL) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isRTL ? 'تغيير كلمة المرور' : 'Change Password',
              style: GoogleFonts.cairo(),
            ),
            content: Text(
              isRTL
                  ? 'هذه الميزة ستكون متاحة قريباً'
                  : 'This feature will be available soon',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(isRTL ? 'موافق' : 'OK', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Widget _buildBluetoothStatusTile(
    BuildContext context,
    BluetoothProvider bluetoothProvider,
    bool isRTL,
    bool isTablet,
  ) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (bluetoothProvider.bluetoothState) {
      case BluetoothState.on:
        statusText = isRTL ? 'متاح' : 'Available';
        statusColor = Colors.green;
        statusIcon = FontAwesomeIcons.bluetooth;
        break;
      case BluetoothState.off:
        statusText = isRTL ? 'مغلق' : 'Disabled';
        statusColor = Colors.red;
        statusIcon = FontAwesomeIcons.bluetoothB;
        break;
      case BluetoothState.unavailable:
        statusText = isRTL ? 'غير متاح' : 'Unavailable';
        statusColor = Colors.grey;
        statusIcon = FontAwesomeIcons.bluetoothB;
        break;
      default:
        statusText = isRTL ? 'غير معروف' : 'Unknown';
        statusColor = Colors.orange;
        statusIcon = FontAwesomeIcons.question;
    }

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(statusIcon, color: statusColor, size: isTablet ? 22 : 20),
      title: Text(
        isRTL ? 'حالة البلوتوث' : 'Bluetooth Status',
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        statusText,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 14 : 12,
          color: statusColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildSelectedPrinterTile(
    BuildContext context,
    BluetoothProvider bluetoothProvider,
    bool isRTL,
    bool isTablet,
  ) {
    final theme = Theme.of(context);
    final printer = bluetoothProvider.selectedPrinter!;

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        FontAwesomeIcons.print,
        color: bluetoothProvider.isConnected ? Colors.green : Colors.grey,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        printer.name,
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            printer.address,
            style: GoogleFonts.cairo(
              fontSize: isTablet ? 12 : 10,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color:
                      bluetoothProvider.isConnected ? Colors.green : Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                bluetoothProvider.isConnected
                    ? (isRTL ? 'متصل' : 'Connected')
                    : (isRTL ? 'غير متصل' : 'Disconnected'),
                style: GoogleFonts.cairo(
                  fontSize: isTablet ? 12 : 10,
                  color:
                      bluetoothProvider.isConnected ? Colors.green : Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
      trailing:
          bluetoothProvider.isConnected
              ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => bluetoothProvider.disconnect(),
                tooltip: isRTL ? 'قطع الاتصال' : 'Disconnect',
              )
              : IconButton(
                icon: const Icon(Icons.link),
                onPressed: () => bluetoothProvider.connectToPrinter(printer),
                tooltip: isRTL ? 'اتصال' : 'Connect',
              ),
    );
  }

  Widget _buildBluetoothActionTile(
    BuildContext context,
    BluetoothProvider bluetoothProvider,
    bool isRTL,
    bool isTablet,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        bluetoothProvider.isScanning
            ? FontAwesomeIcons.spinner
            : FontAwesomeIcons.magnifyingGlass,
        color: Theme.of(context).colorScheme.primary,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        isRTL ? 'البحث عن طابعات' : 'Scan for Printers',
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        bluetoothProvider.isScanning
            ? (isRTL ? 'جاري البحث...' : 'Scanning...')
            : (isRTL
                ? 'اضغط للبحث عن طابعات البلوتوث'
                : 'Tap to scan for Bluetooth printers'),
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 14 : 12,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: isTablet ? 18 : 16,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap:
          bluetoothProvider.isScanning
              ? null
              : () => _showBluetoothScanDialog(context, isRTL),
    );
  }

  void _showBluetoothScanDialog(BuildContext context, bool isRTL) {
    showDialog(
      context: context,
      builder: (context) => BluetoothScanDialog(isRTL: isRTL),
    );
  }

  Widget _buildLogoutTile(BuildContext context, bool isRTL, bool isTablet) {
    final theme = Theme.of(context);

    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: isTablet ? 20 : 16,
        vertical: isTablet ? 8 : 4,
      ),
      leading: FaIcon(
        FontAwesomeIcons.rightFromBracket,
        color: Colors.red,
        size: isTablet ? 22 : 20,
      ),
      title: Text(
        isRTL ? 'تسجيل الخروج' : 'Logout',
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 16 : 14,
          fontWeight: FontWeight.w500,
          color: Colors.red,
        ),
      ),
      subtitle: Text(
        isRTL ? 'تسجيل الخروج من التطبيق' : 'Sign out of the application',
        style: GoogleFonts.cairo(
          fontSize: isTablet ? 14 : 12,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: isTablet ? 18 : 16,
        color: Colors.red.withValues(alpha: 0.7),
      ),
      onTap: () => _showLogoutDialog(context, isRTL),
    );
  }

  void _showLogoutDialog(BuildContext context, bool isRTL) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isRTL ? 'تسجيل الخروج' : 'Logout',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
            ),
            content: Text(
              isRTL
                  ? 'هل أنت متأكد من أنك تريد تسجيل الخروج؟'
                  : 'Are you sure you want to logout?',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  isRTL ? 'إلغاء' : 'Cancel',
                  style: GoogleFonts.cairo(),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _performLogout(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  isRTL ? 'تسجيل الخروج' : 'Logout',
                  style: GoogleFonts.cairo(),
                ),
              ),
            ],
          ),
    );
  }

  Future<void> _performLogout(BuildContext context) async {
    try {
      final sessionProvider = Provider.of<SessionProvider>(
        context,
        listen: false,
      );
      await sessionProvider.logout();

      if (context.mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      debugPrint('Error during logout: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LocaleProvider>(context, listen: false).isRTL
                  ? 'حدث خطأ أثناء تسجيل الخروج'
                  : 'Error occurred during logout',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showResetDialog(BuildContext context, bool isRTL) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isRTL ? 'إعادة تعيين الإعدادات' : 'Reset Settings',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
            ),
            content: Text(
              isRTL
                  ? 'هل أنت متأكد من أنك تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟'
                  : 'Are you sure you want to reset all settings to default values?',
              style: GoogleFonts.cairo(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  isRTL ? 'إلغاء' : 'Cancel',
                  style: GoogleFonts.cairo(),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _resetAllSettings();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  isRTL ? 'إعادة تعيين' : 'Reset',
                  style: GoogleFonts.cairo(),
                ),
              ),
            ],
          ),
    );
  }

  void _resetAllSettings() {
    // Reset all providers to default values
    final notificationProvider = Provider.of<NotificationProvider>(
      context,
      listen: false,
    );
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final currencyProvider = Provider.of<CurrencyProvider>(
      context,
      listen: false,
    );
    final storeSettingsProvider = Provider.of<StoreSettingsProvider>(
      context,
      listen: false,
    );
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    // Reset notification settings
    notificationProvider.setSystemAlertsEnabled(true);

    // Reset language to Arabic
    localeProvider.setLanguage(SupportedLanguage.arabic);

    // Reset currency to DZD
    currencyProvider.setCurrency(SupportedCurrency.dzd);

    // Reset store settings
    storeSettingsProvider.resetStoreInfo();
    storeSettingsProvider.resetReceiptSettings();

    // Reset theme to light mode
    if (themeProvider.isDarkMode) {
      themeProvider.toggleTheme();
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          Provider.of<LocaleProvider>(context, listen: false).isRTL
              ? 'تم إعادة تعيين الإعدادات بنجاح'
              : 'Settings reset successfully',
          style: GoogleFonts.cairo(),
        ),
        backgroundColor: Colors.green,
      ),
    );
  }
}
