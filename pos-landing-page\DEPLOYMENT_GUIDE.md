# 🚀 دليل رفع صفحة الهبوط إلى الإنترنت

## الطريقة الأولى: Vercel (الأسهل والأسرع)

### 1. إنشاء حساب على Vercel
- اذهب إلى [vercel.com](https://vercel.com)
- سجل دخول باستخدام GitHub أو Google أو Email

### 2. رفع المشروع
```bash
# تثبيت Vercel CLI
npm i -g vercel

# في مجلد المشروع
cd pos-landing-page
vercel

# اتبع التعليمات:
# - اختر "y" لإعداد ونشر المشروع
# - اختر اسم المشروع
# - اختر "y" لربطه بـ Vercel
```

### 3. الحصول على الرابط
بعد النشر ستحصل على رابط مثل:
`https://pos-landing-page-xxx.vercel.app`

---

## الطريقة الثانية: Netlify

### 1. إنشاء Build
```bash
cd pos-landing-page
npm run build
```

### 2. رفع إلى Netlify
- اذهب إلى [netlify.com](https://netlify.com)
- اسحب مجلد `dist` إلى منطقة الرفع
- احصل على الرابط

---

## الطريقة الثالثة: GitHub Pages

### 1. رفع إلى GitHub
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/username/pos-landing-page.git
git push -u origin main
```

### 2. تفعيل GitHub Pages
- اذهب إلى Settings > Pages
- اختر Source: GitHub Actions
- استخدم workflow للـ build والنشر

---

## ملاحظات مهمة:

### ✅ الملفات المطلوبة موجودة:
- `vercel.json` - إعدادات Vercel
- `package.json` - معلومات المشروع
- `vite.config.ts` - إعدادات Vite
- `dist/` - مجلد البناء (بعد تشغيل build)

### 🔧 إعدادات إضافية:
- تأكد من أن جميع الصور في مجلد `public/`
- تحقق من أن المسارات صحيحة
- اختبر المشروع محلياً قبل النشر

### 🌐 الروابط المتوقعة:
- Vercel: `https://pos-landing-page-xxx.vercel.app`
- Netlify: `https://xxx-pos-landing.netlify.app`
- GitHub Pages: `https://username.github.io/pos-landing-page`

---

## 🚀 النشر السريع (خطوة واحدة):

```bash
# تثبيت وتشغيل Vercel
npx vercel --prod
```

سيتم النشر تلقائياً وستحصل على الرابط فوراً!
