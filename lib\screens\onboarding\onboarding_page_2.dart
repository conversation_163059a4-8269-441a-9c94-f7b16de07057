import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../providers/locale_provider.dart';

class OnboardingPage2 extends StatefulWidget {
  const OnboardingPage2({super.key});

  @override
  State<OnboardingPage2> createState() => _OnboardingPage2State();
}

class _OnboardingPage2State extends State<OnboardingPage2>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Responsive sizing
    final isSmallScreen = screenHeight < 600;
    final titleFontSize = isSmallScreen ? 20.0 : 28.0;
    final descriptionFontSize = isSmallScreen ? 14.0 : 16.0;
    final crossAxisCount = screenWidth < 400 ? 1 : 2;

    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.06,
          vertical: 16.0,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(minHeight: screenHeight * 0.7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Animated Illustration
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 20.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              theme.colorScheme.secondary.withValues(
                                alpha: 0.1,
                              ),
                              theme.colorScheme.tertiary.withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Feature Cards Grid
                            GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: crossAxisCount,
                              mainAxisSpacing: isSmallScreen ? 12 : 16,
                              crossAxisSpacing: isSmallScreen ? 12 : 16,
                              childAspectRatio: crossAxisCount == 1 ? 3.0 : 1.1,
                              children: [
                                _buildFeatureCard(
                                  FontAwesomeIcons.chartBar,
                                  isRTL ? 'تتبع المبيعات' : 'Sales Tracking',
                                  theme.colorScheme.primary,
                                  isSmallScreen,
                                ),
                                _buildFeatureCard(
                                  FontAwesomeIcons.boxesStacked,
                                  isRTL
                                      ? 'إدارة المخزون'
                                      : 'Inventory Management',
                                  theme.colorScheme.secondary,
                                  isSmallScreen,
                                ),
                                _buildFeatureCard(
                                  FontAwesomeIcons.users,
                                  isRTL
                                      ? 'إدارة العملاء'
                                      : 'Customer Management',
                                  theme.colorScheme.tertiary,
                                  isSmallScreen,
                                ),
                                _buildFeatureCard(
                                  FontAwesomeIcons.fileInvoice,
                                  isRTL
                                      ? 'الفواتير والتقارير'
                                      : 'Invoices & Reports',
                                  Colors.orange,
                                  isSmallScreen,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: isSmallScreen ? 20 : 40),

              // Content
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Title
                          Text(
                            isRTL
                                ? 'ميزات قوية لإدارة أعمالك'
                                : 'Powerful Features for Your Business',
                            style: GoogleFonts.cairo(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                              height: 1.2,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: isSmallScreen ? 12 : 20),

                          // Description
                          Text(
                            isRTL
                                ? 'استفد من مجموعة شاملة من الأدوات المتقدمة لتنظيم وإدارة جميع جوانب متجرك بكفاءة عالية.'
                                : 'Take advantage of a comprehensive set of advanced tools to organize and manage all aspects of your store efficiently.',
                            style: GoogleFonts.cairo(
                              fontSize: descriptionFontSize,
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: isSmallScreen ? 16 : 30),

                          // Key Features List
                          _buildKeyFeaturesList(context, isRTL, isSmallScreen),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    IconData icon,
    String title,
    Color color,
    bool isSmallScreen,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: isSmallScreen ? 40.0 : 50.0,
            height: isSmallScreen ? 40.0 : 50.0,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: isSmallScreen ? 20.0 : 24.0, color: color),
          ),
          SizedBox(height: isSmallScreen ? 8 : 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Text(
              title,
              style: GoogleFonts.cairo(
                fontSize: isSmallScreen ? 10.0 : 12.0,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeyFeaturesList(
    BuildContext context,
    bool isRTL,
    bool isSmallScreen,
  ) {
    final theme = Theme.of(context);
    final fontSize = isSmallScreen ? 12.0 : 14.0;
    final iconSize = isSmallScreen ? 16.0 : 20.0;

    final features =
        isRTL
            ? [
              'تتبع المبيعات في الوقت الفعلي',
              'إدارة المخزون والتنبيهات',
              'تقارير مالية مفصلة',
            ]
            : [
              'Real-time sales tracking',
              'Inventory management & alerts',
              'Detailed financial reports',
            ];

    return Column(
      children:
          features
              .map(
                (feature) => Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: isSmallScreen ? 2 : 4,
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: iconSize,
                        height: iconSize,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          borderRadius: BorderRadius.circular(iconSize / 2),
                        ),
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                          size: iconSize * 0.7,
                        ),
                      ),
                      SizedBox(width: isSmallScreen ? 8 : 12),
                      Expanded(
                        child: Text(
                          feature,
                          style: GoogleFonts.cairo(
                            fontSize: fontSize,
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.8,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
    );
  }
}
