import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/models/category.dart';
import 'package:pos_app/widgets/category_widgets.dart';

class EnhancedCategoriesScreen extends StatefulWidget {
  const EnhancedCategoriesScreen({super.key});

  @override
  State<EnhancedCategoriesScreen> createState() =>
      _EnhancedCategoriesScreenState();
}

class _EnhancedCategoriesScreenState extends State<EnhancedCategoriesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name';
  bool _sortAscending = true;
  bool _showGridView = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CategoryProvider>(context, listen: false).loadCategories();
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(isRTL ? 'إدارة الفئات' : 'Categories Management'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () => setState(() => _showGridView = !_showGridView),
            icon: Icon(_showGridView ? Icons.list : Icons.grid_view),
            tooltip: isRTL ? 'تغيير العرض' : 'Change View',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            tooltip: isRTL ? 'ترتيب' : 'Sort',
            onSelected: (value) {
              if (value == _sortBy) {
                setState(() => _sortAscending = !_sortAscending);
              } else {
                setState(() {
                  _sortBy = value;
                  _sortAscending = true;
                });
              }
            },
            itemBuilder:
                (context) => [
                  _buildSortMenuItem('name', isRTL ? 'الاسم' : 'Name'),
                  _buildSortMenuItem(
                    'products',
                    isRTL ? 'عدد المنتجات' : 'Product Count',
                  ),
                  _buildSortMenuItem(
                    'created',
                    isRTL ? 'تاريخ الإنشاء' : 'Created Date',
                  ),
                ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              icon: const FaIcon(FontAwesomeIcons.list, size: 16),
              text: isRTL ? 'جميع الفئات' : 'All Categories',
            ),
            Tab(
              icon: const FaIcon(FontAwesomeIcons.chartPie, size: 16),
              text: isRTL ? 'الإحصائيات' : 'Statistics',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildSearchBar(isRTL, theme),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [_buildCategoriesTab(), _buildStatisticsTab()],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddCategoryDialog,
        icon: const Icon(Icons.add),
        label: Text(isRTL ? 'إضافة فئة' : 'Add Category'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildSearchBar(bool isRTL, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: isRTL ? 'البحث في الفئات...' : 'Search categories...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon:
              _searchQuery.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _searchQuery = '';
                        _searchController.clear();
                      });
                    },
                  )
                  : null,
          filled: true,
          fillColor: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.3,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
        ),
        onChanged: (value) => setState(() => _searchQuery = value),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return Consumer2<CategoryProvider, ProductProvider>(
      builder: (context, categoryProvider, productProvider, _) {
        final filteredCategories = _getFilteredAndSortedCategories(
          categoryProvider.categories,
          productProvider.products,
        );

        if (categoryProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (filteredCategories.isEmpty) {
          return _buildEmptyState();
        }

        return _showGridView
            ? _buildGridView(filteredCategories, productProvider)
            : _buildListView(filteredCategories, productProvider);
      },
    );
  }

  Widget _buildStatisticsTab() {
    return Consumer2<CategoryProvider, ProductProvider>(
      builder: (context, categoryProvider, productProvider, _) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildOverviewCards(categoryProvider, productProvider),
              const SizedBox(height: 24),
              _buildCategoryChart(categoryProvider, productProvider),
              const SizedBox(height: 24),
              _buildTopCategoriesTable(categoryProvider, productProvider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGridView(
    List<Category> categories,
    ProductProvider productProvider,
  ) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final productCount =
            productProvider.products
                .where((product) => product.category == category.name)
                .length;
        return _buildCategoryCard(category, productCount, isGrid: true);
      },
    );
  }

  Widget _buildListView(
    List<Category> categories,
    ProductProvider productProvider,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final productCount =
            productProvider.products
                .where((product) => product.category == category.name)
                .length;
        return _buildCategoryCard(category, productCount, isGrid: false);
      },
    );
  }

  Widget _buildCategoryCard(
    Category category,
    int productCount, {
    required bool isGrid,
  }) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    if (isGrid) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: InkWell(
          onTap: () => _editCategory(category),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(
                          category.name,
                        ).withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: FaIcon(
                        _getCategoryIcon(category.name),
                        color: _getCategoryColor(category.name),
                        size: 20,
                      ),
                    ),
                    PopupMenuButton(
                      itemBuilder:
                          (context) => [
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  const Icon(Icons.edit),
                                  const SizedBox(width: 8),
                                  Text(isRTL ? 'تعديل' : 'Edit'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  const Icon(Icons.delete, color: Colors.red),
                                  const SizedBox(width: 8),
                                  Text(
                                    isRTL ? 'حذف' : 'Delete',
                                    style: const TextStyle(color: Colors.red),
                                  ),
                                ],
                              ),
                            ),
                          ],
                      onSelected: (value) {
                        if (value == 'edit') {
                          _editCategory(category);
                        } else if (value == 'delete') {
                          _deleteCategory(category);
                        }
                      },
                    ),
                  ],
                ),
                const Spacer(),
                Text(
                  category.name,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '$productCount ${isRTL ? 'منتج' : 'products'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: ListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: _getCategoryColor(category.name).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: FaIcon(
              _getCategoryIcon(category.name),
              color: _getCategoryColor(category.name),
              size: 24,
            ),
          ),
          title: Text(
            category.name,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (category.description != null &&
                  category.description!.isNotEmpty)
                Text(category.description!),
              const SizedBox(height: 4),
              Text(
                '$productCount ${isRTL ? 'منتج' : 'products'}',
                style: TextStyle(
                  color: _getCategoryColor(category.name),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: () => _editCategory(category),
                icon: const Icon(Icons.edit),
                tooltip: isRTL ? 'تعديل' : 'Edit',
              ),
              IconButton(
                onPressed: () => _deleteCategory(category),
                icon: const Icon(Icons.delete, color: Colors.red),
                tooltip: isRTL ? 'حذف' : 'Delete',
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildOverviewCards(
    CategoryProvider categoryProvider,
    ProductProvider productProvider,
  ) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    final totalCategories = categoryProvider.categories.length;
    final totalProducts = productProvider.products.length;
    final avgProductsPerCategory =
        totalCategories > 0 ? totalProducts / totalCategories : 0;
    final emptyCategories =
        categoryProvider.categories
            .where(
              (cat) =>
                  productProvider.products
                      .where((prod) => prod.category == cat.name)
                      .isEmpty,
            )
            .length;

    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          title: isRTL ? 'إجمالي الفئات' : 'Total Categories',
          value: totalCategories.toString(),
          icon: FontAwesomeIcons.list,
          color: Colors.blue,
          theme: theme,
        ),
        _buildStatCard(
          title: isRTL ? 'متوسط المنتجات' : 'Avg Products',
          value: avgProductsPerCategory.toStringAsFixed(1),
          icon: FontAwesomeIcons.chartBar,
          color: Colors.green,
          theme: theme,
        ),
        _buildStatCard(
          title: isRTL ? 'فئات فارغة' : 'Empty Categories',
          value: emptyCategories.toString(),
          icon: FontAwesomeIcons.exclamationTriangle,
          color: Colors.orange,
          theme: theme,
        ),
        _buildStatCard(
          title: isRTL ? 'إجمالي المنتجات' : 'Total Products',
          value: totalProducts.toString(),
          icon: FontAwesomeIcons.box,
          color: Colors.purple,
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required ThemeData theme,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FaIcon(icon, color: color, size: 24),
            const Spacer(),
            Text(
              value,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.outline,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChart(
    CategoryProvider categoryProvider,
    ProductProvider productProvider,
  ) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRTL
                  ? 'توزيع المنتجات حسب الفئة'
                  : 'Product Distribution by Category',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  isRTL
                      ? 'مخطط التوزيع قيد التطوير'
                      : 'Distribution chart coming soon',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCategoriesTable(
    CategoryProvider categoryProvider,
    ProductProvider productProvider,
  ) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    final categoriesWithCounts =
        categoryProvider.categories.map((category) {
          final productCount =
              productProvider.products
                  .where((product) => product.category == category.name)
                  .length;
          return {'category': category, 'count': productCount};
        }).toList();

    categoriesWithCounts.sort(
      (a, b) => (b['count'] as int).compareTo(a['count'] as int),
    );

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isRTL ? 'أكثر الفئات استخداماً' : 'Most Used Categories',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...categoriesWithCounts.take(5).map((item) {
              final category = item['category'] as Category;
              final count = item['count'] as int;
              return ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getCategoryColor(
                      category.name,
                    ).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: FaIcon(
                    _getCategoryIcon(category.name),
                    color: _getCategoryColor(category.name),
                    size: 16,
                  ),
                ),
                title: Text(category.name),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    count.toString(),
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FaIcon(
            FontAwesomeIcons.list,
            size: 80,
            color: theme.colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            isRTL ? 'لا توجد فئات' : 'No categories found',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            isRTL ? 'ابدأ بإضافة فئة جديدة' : 'Start by adding a new category',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.outline,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddCategoryDialog,
            icon: const Icon(Icons.add),
            label: Text(isRTL ? 'إضافة فئة' : 'Add Category'),
          ),
        ],
      ),
    );
  }

  List<Category> _getFilteredAndSortedCategories(
    List<Category> categories,
    List<dynamic> products,
  ) {
    var filtered =
        categories.where((category) {
          return _searchQuery.isEmpty ||
              category.name.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              (category.description?.toLowerCase().contains(
                    _searchQuery.toLowerCase(),
                  ) ??
                  false);
        }).toList();

    filtered.sort((a, b) {
      switch (_sortBy) {
        case 'name':
          return _sortAscending
              ? a.name.compareTo(b.name)
              : b.name.compareTo(a.name);
        case 'products':
          final aCount = products.where((p) => p.category == a.name).length;
          final bCount = products.where((p) => p.category == b.name).length;
          return _sortAscending
              ? aCount.compareTo(bCount)
              : bCount.compareTo(aCount);
        case 'created':
          return _sortAscending
              ? a.id!.compareTo(b.id!)
              : b.id!.compareTo(a.id!);
        default:
          return 0;
      }
    });

    return filtered;
  }

  PopupMenuItem<String> _buildSortMenuItem(String value, String label) {
    final isSelected = _sortBy == value;
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Text(label),
          const Spacer(),
          if (isSelected)
            Icon(
              _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
              size: 16,
            ),
        ],
      ),
    );
  }

  Color _getCategoryColor(String categoryName) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];
    return colors[categoryName.hashCode % colors.length];
  }

  IconData _getCategoryIcon(String categoryName) {
    final icons = {
      'Electronics': FontAwesomeIcons.laptop,
      'Clothing': FontAwesomeIcons.shirt,
      'Food': FontAwesomeIcons.utensils,
      'Books': FontAwesomeIcons.book,
      'Sports': FontAwesomeIcons.dumbbell,
      'Beauty': FontAwesomeIcons.spa,
      'Home': FontAwesomeIcons.house,
      'Toys': FontAwesomeIcons.gamepad,
    };
    return icons[categoryName] ?? FontAwesomeIcons.tag;
  }

  void _showAddCategoryDialog() {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: Text(isRTL ? 'إضافة فئة جديدة' : 'Add New Category'),
            content: SingleChildScrollView(
              child: CategoryForm(
                onSave: (newCategory) {
                  Provider.of<CategoryProvider>(
                    context,
                    listen: false,
                  ).addCategory(newCategory);
                  Navigator.of(ctx).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isRTL
                            ? 'تمت إضافة الفئة بنجاح'
                            : 'Category added successfully',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                onCancel: () => Navigator.of(ctx).pop(),
                isDialog: true,
              ),
            ),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
            actionsPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
    );
  }

  void _editCategory(Category category) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: Text(isRTL ? 'تعديل الفئة' : 'Edit Category'),
            content: SingleChildScrollView(
              child: CategoryForm(
                category: category,
                onSave: (updatedCategory) {
                  Provider.of<CategoryProvider>(
                    context,
                    listen: false,
                  ).updateCategory(updatedCategory);
                  Navigator.of(ctx).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isRTL
                            ? 'تم تحديث الفئة بنجاح'
                            : 'Category updated successfully',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                onCancel: () => Navigator.of(ctx).pop(),
                isDialog: true,
              ),
            ),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
            actionsPadding: EdgeInsets.zero,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
    );
  }

  void _deleteCategory(Category category) {
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isRTL ? 'حذف الفئة' : 'Delete Category'),
            content: Text(
              isRTL
                  ? 'هل أنت متأكد من حذف فئة "${category.name}"؟'
                  : 'Are you sure you want to delete category "${category.name}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(isRTL ? 'إلغاء' : 'Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Provider.of<CategoryProvider>(
                    context,
                    listen: false,
                  ).deleteCategory(category.id!);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        isRTL
                            ? 'تم حذف الفئة بنجاح'
                            : 'Category deleted successfully',
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: Text(isRTL ? 'حذف' : 'Delete'),
              ),
            ],
          ),
    );
  }
}
