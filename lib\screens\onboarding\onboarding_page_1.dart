import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../providers/locale_provider.dart';

class OnboardingPage1 extends StatefulWidget {
  const OnboardingPage1({super.key});

  @override
  State<OnboardingPage1> createState() => _OnboardingPage1State();
}

class _OnboardingPage1State extends State<OnboardingPage1>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOut),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Responsive sizing
    final isSmallScreen = screenHeight < 600;
    final iconSize = isSmallScreen ? 50.0 : 60.0;
    final containerSize = isSmallScreen ? 100.0 : 120.0;
    final titleFontSize = isSmallScreen ? 20.0 : 28.0;
    final descriptionFontSize = isSmallScreen ? 14.0 : 16.0;

    return SafeArea(
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.06,
          vertical: 16.0,
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(minHeight: screenHeight * 0.7),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Animated Illustration
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              theme.colorScheme.primary.withValues(alpha: 0.1),
                              theme.colorScheme.secondary.withValues(
                                alpha: 0.1,
                              ),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Main Icon
                            Container(
                              width: containerSize,
                              height: containerSize,
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary,
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: theme.colorScheme.primary.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: Icon(
                                FontAwesomeIcons.cashRegister,
                                size: iconSize,
                                color: Colors.white,
                              ),
                            ),

                            SizedBox(height: isSmallScreen ? 20 : 30),

                            // Feature Icons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                _buildFeatureIcon(
                                  FontAwesomeIcons.chartLine,
                                  theme.colorScheme.secondary,
                                  isSmallScreen,
                                ),
                                _buildFeatureIcon(
                                  FontAwesomeIcons.boxesStacked,
                                  theme.colorScheme.tertiary,
                                  isSmallScreen,
                                ),
                                _buildFeatureIcon(
                                  FontAwesomeIcons.receipt,
                                  theme.colorScheme.primary,
                                  isSmallScreen,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),

              SizedBox(height: isSmallScreen ? 20 : 40),

              // Content
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Title
                          Text(
                            isRTL
                                ? 'مرحباً بك في نظام نقاط البيع'
                                : 'Welcome to POS System',
                            style: GoogleFonts.cairo(
                              fontSize: titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                              height: 1.2,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: isSmallScreen ? 12 : 20),

                          // Description
                          Text(
                            isRTL
                                ? 'نظام شامل لإدارة متجرك بكفاءة عالية. تتبع المبيعات، إدارة المخزون، وإنشاء التقارير بسهولة تامة.'
                                : 'A comprehensive system to manage your store efficiently. Track sales, manage inventory, and generate reports with ease.',
                            style: GoogleFonts.cairo(
                              fontSize: descriptionFontSize,
                              color: theme.colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                              height: 1.5,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          SizedBox(height: isSmallScreen ? 16 : 30),

                          // Benefits List
                          _buildBenefitsList(context, isRTL, isSmallScreen),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureIcon(IconData icon, Color color, bool isSmallScreen) {
    final size = isSmallScreen ? 40.0 : 50.0;
    final iconSize = isSmallScreen ? 20.0 : 24.0;

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Icon(icon, size: iconSize, color: color),
    );
  }

  Widget _buildBenefitsList(
    BuildContext context,
    bool isRTL,
    bool isSmallScreen,
  ) {
    final theme = Theme.of(context);
    final fontSize = isSmallScreen ? 12.0 : 14.0;
    final iconSize = isSmallScreen ? 18.0 : 20.0;

    final benefits =
        isRTL
            ? [
              'إدارة سهلة للمبيعات والمخزون',
              'تقارير مفصلة ومتقدمة',
              'واجهة سهلة الاستخدام',
            ]
            : [
              'Easy sales and inventory management',
              'Detailed and advanced reports',
              'User-friendly interface',
            ];

    return Column(
      children:
          benefits
              .map(
                (benefit) => Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: isSmallScreen ? 2 : 4,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: theme.colorScheme.primary,
                        size: iconSize,
                      ),
                      SizedBox(width: isSmallScreen ? 8 : 12),
                      Expanded(
                        child: Text(
                          benefit,
                          style: GoogleFonts.cairo(
                            fontSize: fontSize,
                            color: theme.colorScheme.onSurface.withValues(
                              alpha: 0.8,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
    );
  }
}
