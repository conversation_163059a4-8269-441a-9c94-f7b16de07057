/// هذا الملف يحتوي على الثوابت والإعدادات المتعلقة بالطباعة
/// This file contains constants and settings related to printing
library;

/// أنواع الطابعات المدعومة
/// Supported printer types
enum PrinterType { bluetooth, network, usb }

/// مقاسات الورق المدعومة
/// Supported paper sizes
enum PaperSize {
  mm58, // 58mm
  mm80, // 80mm
  a4, // A4
  custom, // مقاس مخصص / Custom size
}

/// تفاصيل حول مقاسات الورق
/// Details about paper sizes
class PaperSizeDetails {
  static const Map<PaperSize, Map<String, dynamic>> sizes = {
    PaperSize.mm58: {
      'width': 384, // عرض الورقة بالنقاط / Paper width in dots
      'name': '58mm',
      'charactersPerLine': 32,
    },
    PaperSize.mm80: {
      'width': 576, // عرض الورقة بالنقاط / Paper width in dots
      'name': '80mm',
      'charactersPerLine': 48,
    },
    PaperSize.a4: {
      'width':
          8.27 *
          72, // عرض A4 بالبوصة × 72 نقطة في البوصة / A4 width in inches × 72 dpi
      'name': 'A4',
      'charactersPerLine': 100,
    },
    PaperSize.custom: {
      'width': 0, // يتم تعيينه من قبل المستخدم / Set by user
      'name': 'مخصص',
      'charactersPerLine': 0,
    },
  };
}

/// أنماط قص الورق
/// Paper cutting modes
enum CutMode {
  full, // قص كامل / Full cut
  partial, // قص جزئي / Partial cut
  none, // بدون قص / No cut
}

/// نص افتراضي لاختبار الطباعة
/// Default text for print testing
class PrinterTestText {
  static const String arabic = '''
مرحبا بك في نظام نقاط البيع
--------------------------
هذا اختبار للطباعة باللغة العربية
تم تنفيذه بنجاح
''';

  static const String english = '''
Welcome to POS System
--------------------------
This is a test for printer
Successfully completed
''';
}
