import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

class CartView extends StatelessWidget {
  const CartView({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.green.shade700,
                  Colors.green.shade600,
                ],
              ),
            ),
            child: Row(
              children: [
                FaIcon(
                  FontAwesomeIcons.cartShopping,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Consumer<LocaleProvider>(
                  builder: (context, localeProvider, _) => Text(
                    localeProvider.isRTL ? 'سلة المشتريات' : 'Shopping Cart',
                    style: GoogleFonts.cairo(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                const Spacer(),
                Consumer<CartProvider>(
                  builder: (context, cart, _) => Text(
                    '${cart.itemCount} ${Provider.of<LocaleProvider>(context).isRTL ? 'منتج' : 'items'}',
                    style: GoogleFonts.cairo(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Consumer<CartProvider>(
              builder: (context, cart, _) {
                if (cart.items.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FaIcon(
                          FontAwesomeIcons.cartPlus,
                          size: 48,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        Consumer<LocaleProvider>(
                          builder: (context, localeProvider, _) => Text(
                            localeProvider.isRTL
                                ? 'السلة فارغة'
                                : 'Cart is empty',
                            style: GoogleFonts.cairo(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: cart.items.length,
                  itemBuilder: (context, index) {
                    final item = cart.items[index];
                    return Card(
                      elevation: 0,
                      color: Colors.grey.shade50,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: ListTile(
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        leading: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: FaIcon(
                            FontAwesomeIcons.box,
                            color: Colors.green.shade700,
                            size: 20,
                          ),
                        ),
                        title: Text(
                          item.product.name,
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: Text(
                          NumberFormat.currency(
                            symbol: '₪',
                            decimalDigits: 2,
                          ).format(item.product.price * item.quantity),
                          style: GoogleFonts.cairo(
                            color: Colors.green.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const FaIcon(
                                FontAwesomeIcons.minus,
                                size: 16,
                              ),
                              onPressed: () => cart.decrementQuantity(item.product),
                              color: Colors.red.shade400,
                            ),
                            Text(
                              item.quantity.toString(),
                              style: GoogleFonts.cairo(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            IconButton(
                              icon: const FaIcon(
                                FontAwesomeIcons.plus,
                                size: 16,
                              ),
                              onPressed: () => cart.incrementQuantity(item.product),
                              color: Colors.green.shade700,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              children: [
                Consumer<CartProvider>(
                  builder: (context, cart, _) => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Consumer<LocaleProvider>(
                        builder: (context, localeProvider, _) => Text(
                          localeProvider.isRTL ? 'المجموع' : 'Total',
                          style: GoogleFonts.cairo(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Text(
                        NumberFormat.currency(
                          symbol: '₪',
                          decimalDigits: 2,
                        ).format(cart.totalAmount),
                        style: GoogleFonts.cairo(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Consumer<CartProvider>(
                  builder: (context, cart, _) => ElevatedButton.icon(
                    onPressed: cart.items.isEmpty
                        ? null
                        : () {
                            // Navigate to checkout
                          },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade700,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    icon: const FaIcon(FontAwesomeIcons.moneyBill),
                    label: Consumer<LocaleProvider>(
                      builder: (context, localeProvider, _) => Text(
                        localeProvider.isRTL ? 'الدفع' : 'Checkout',
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
