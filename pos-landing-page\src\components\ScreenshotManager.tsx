import React, { useState, useEffect } from 'react'
import { Camera, Download, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface Screenshot {
  id: string
  name: string
  description: string
  url: string
  category: 'dashboard' | 'cart' | 'payment' | 'settings' | 'inventory' | 'reports'
}

const ScreenshotManager: React.FC = () => {
  const [screenshots, setScreenshots] = useState<Screenshot[]>([])
  const [isCapturing, setIsCapturing] = useState(false)

  // Default screenshots (placeholders that will be replaced with real ones)
  const defaultScreenshots: Screenshot[] = [
    {
      id: 'dashboard',
      name: 'Dashboard Overview',
      description: 'Main dashboard with sales analytics and quick stats',
      url: '/screenshots/dashboard.png',
      category: 'dashboard'
    },
    {
      id: 'cart-management',
      name: '<PERSON> Cart',
      description: 'Advanced cart management with auto-save functionality',
      url: '/screenshots/cart.png',
      category: 'cart'
    },
    {
      id: 'partial-payment',
      name: 'Partial Payment',
      description: 'Revolutionary partial payment system with debt tracking',
      url: '/screenshots/payment.png',
      category: 'payment'
    },
    {
      id: 'settings-screen',
      name: 'Professional Settings',
      description: 'Comprehensive settings with printer configuration',
      url: '/screenshots/settings.png',
      category: 'settings'
    },
    {
      id: 'inventory-management',
      name: 'Inventory Control',
      description: 'Complete inventory management with stock alerts',
      url: '/screenshots/inventory.png',
      category: 'inventory'
    },
    {
      id: 'reports-analytics',
      name: 'Reports & Analytics',
      description: 'Advanced reporting with charts and insights',
      url: '/screenshots/reports.png',
      category: 'reports'
    }
  ]

  useEffect(() => {
    setScreenshots(defaultScreenshots)
  }, [])

  const captureScreenshot = async (url: string, filename: string) => {
    setIsCapturing(true)
    try {
      // This would typically use a screenshot service or API
      // For now, we'll simulate the process
      console.log(`Capturing screenshot from ${url} as ${filename}`)
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real implementation, you would:
      // 1. Use a service like Puppeteer, Playwright, or a screenshot API
      // 2. Capture the specific screen from localhost:8080
      // 3. Save it to the public/screenshots folder
      // 4. Update the screenshot URL in the state
      
      alert(`Screenshot captured: ${filename}`)
    } catch (error) {
      console.error('Failed to capture screenshot:', error)
      alert('Failed to capture screenshot. Please try again.')
    } finally {
      setIsCapturing(false)
    }
  }

  const downloadScreenshot = (screenshot: Screenshot) => {
    const link = document.createElement('a')
    link.href = screenshot.url
    link.download = `${screenshot.name.toLowerCase().replace(/\s+/g, '-')}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const refreshScreenshots = () => {
    // Refresh all screenshots from the POS app
    setIsCapturing(true)
    setTimeout(() => {
      setIsCapturing(false)
      alert('Screenshots refreshed successfully!')
    }, 3000)
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">POS App Screenshots</h1>
          <p className="text-gray-600">
            Manage and capture screenshots from the live POS application for the landing page.
          </p>
        </div>

        {/* Controls */}
        <div className="mb-8 flex flex-wrap gap-4">
          <Button
            onClick={refreshScreenshots}
            disabled={isCapturing}
            className="bg-green-600 hover:bg-green-700"
          >
            {isCapturing ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Camera className="w-4 h-4 mr-2" />
            )}
            {isCapturing ? 'Capturing...' : 'Refresh All Screenshots'}
          </Button>

          <Button
            variant="outline"
            onClick={() => window.open('http://localhost:8080', '_blank')}
          >
            Open POS App
          </Button>
        </div>

        {/* Screenshots Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {screenshots.map((screenshot) => (
            <Card key={screenshot.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">{screenshot.name}</CardTitle>
                <p className="text-sm text-gray-600">{screenshot.description}</p>
              </CardHeader>
              <CardContent>
                {/* Screenshot Preview */}
                <div className="relative mb-4 bg-gray-100 rounded-lg overflow-hidden aspect-[9/16]">
                  <img
                    src={screenshot.url}
                    alt={screenshot.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      target.src = `https://via.placeholder.com/280x600/f3f4f6/6b7280?text=${encodeURIComponent(screenshot.name)}`
                    }}
                  />
                  
                  {/* Category Badge */}
                  <div className="absolute top-2 left-2">
                    <span className="px-2 py-1 bg-black/70 text-white text-xs rounded-full">
                      {screenshot.category}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => captureScreenshot('http://localhost:8080', screenshot.name)}
                    disabled={isCapturing}
                    className="flex-1"
                  >
                    <Camera className="w-4 h-4 mr-1" />
                    Capture
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => downloadScreenshot(screenshot)}
                    className="flex-1"
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Instructions */}
        <div className="mt-12 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            📸 How to Capture Real Screenshots
          </h3>
          <div className="space-y-2 text-blue-800">
            <p><strong>1.</strong> Make sure your POS app is running on http://localhost:8080</p>
            <p><strong>2.</strong> Navigate to different screens in the POS app (Dashboard, Cart, Settings, etc.)</p>
            <p><strong>3.</strong> Use browser developer tools or screenshot tools to capture each screen</p>
            <p><strong>4.</strong> Save screenshots as PNG files in the <code className="bg-blue-100 px-1 rounded">public/screenshots/</code> folder</p>
            <p><strong>5.</strong> Name them: dashboard.png, cart.png, payment.png, settings.png, inventory.png, reports.png</p>
            <p><strong>6.</strong> Recommended size: 280x600 pixels (mobile portrait)</p>
          </div>
        </div>

        {/* File Structure Guide */}
        <div className="mt-6 bg-gray-100 border border-gray-300 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            📁 Expected File Structure
          </h3>
          <pre className="text-sm text-gray-700 bg-white p-4 rounded border overflow-x-auto">
{`pos-landing-page/
├── public/
│   ├── screenshots/
│   │   ├── dashboard.png     # Main dashboard screen
│   │   ├── cart.png          # Cart management screen
│   │   ├── payment.png       # Partial payment screen
│   │   ├── settings.png      # Settings configuration
│   │   ├── inventory.png     # Inventory management
│   │   └── reports.png       # Reports and analytics
│   └── ...
└── src/
    └── ...`}
          </pre>
        </div>
      </div>
    </div>
  )
}

export default ScreenshotManager
