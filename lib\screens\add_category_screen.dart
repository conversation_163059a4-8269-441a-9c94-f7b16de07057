import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/models/category.dart';

class AddCategoryScreen extends StatefulWidget {
  final Category? category; // For editing existing category

  const AddCategoryScreen({super.key, this.category});

  @override
  State<AddCategoryScreen> createState() => _AddCategoryScreenState();
}

class _AddCategoryScreenState extends State<AddCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _selectedIcon = 'category';
  bool _isLoading = false;

  // Available icons for categories
  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'category', 'icon': Icons.category},
    {'name': 'local_cafe', 'icon': Icons.local_cafe},
    {'name': 'bakery_dining', 'icon': Icons.bakery_dining},
    {'name': 'lunch_dining', 'icon': Icons.lunch_dining},
    {'name': 'restaurant', 'icon': Icons.restaurant},
    {'name': 'egg', 'icon': Icons.egg},
    {'name': 'eco', 'icon': Icons.eco},
    {'name': 'shopping_cart', 'icon': Icons.shopping_cart},
    {'name': 'local_grocery_store', 'icon': Icons.local_grocery_store},
    {'name': 'fastfood', 'icon': Icons.fastfood},
    {'name': 'cake', 'icon': Icons.cake},
    {'name': 'wine_bar', 'icon': Icons.wine_bar},
    {'name': 'icecream', 'icon': Icons.icecream},
    {'name': 'local_pizza', 'icon': Icons.local_pizza},
    {'name': 'ramen_dining', 'icon': Icons.ramen_dining},
    {'name': 'set_meal', 'icon': Icons.set_meal},
  ];

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category!.description ?? '';
      _selectedIcon = widget.category!.iconName ?? 'category';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categoryProvider = Provider.of<CategoryProvider>(context, listen: false);
      
      if (widget.category != null) {
        // Update existing category
        final updatedCategory = widget.category!.copyWith(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty 
              ? null 
              : _descriptionController.text.trim(),
          iconName: _selectedIcon,
        );
        await categoryProvider.updateCategory(updatedCategory);
      } else {
        // Create new category
        final newCategory = Category(
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty 
              ? null 
              : _descriptionController.text.trim(),
          iconName: _selectedIcon,
        );
        await categoryProvider.addCategory(newCategory);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 10),
                Text(widget.category != null 
                    ? 'Category updated successfully' 
                    : 'Category added successfully'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildIconSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Icon',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        Container(
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: GridView.builder(
            padding: const EdgeInsets.all(8),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: _availableIcons.length,
            itemBuilder: (context, index) {
              final iconData = _availableIcons[index];
              final isSelected = _selectedIcon == iconData['name'];
              
              return InkWell(
                onTap: () {
                  setState(() {
                    _selectedIcon = iconData['name'];
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected 
                        ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
                        : Colors.transparent,
                    border: Border.all(
                      color: isSelected 
                          ? Theme.of(context).primaryColor 
                          : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    iconData['icon'],
                    color: isSelected 
                        ? Theme.of(context).primaryColor 
                        : Colors.grey.shade600,
                    size: 24,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleProvider>(
      builder: (context, localeProvider, _) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.category != null
                  ? (localeProvider.isRTL ? 'تعديل الفئة' : 'Edit Category')
                  : (localeProvider.isRTL ? 'إضافة فئة جديدة' : 'Add New Category'),
            ),
            actions: [
              if (_isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ),
                )
              else
                TextButton(
                  onPressed: _saveCategory,
                  child: Text(
                    localeProvider.isRTL ? 'حفظ' : 'Save',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
            ],
          ),
          body: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Category Name Field
                  TextFormField(
                    controller: _nameController,
                    textDirection: localeProvider.isRTL 
                        ? TextDirection.rtl 
                        : TextDirection.ltr,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'اسم الفئة*' : 'Category Name*',
                      hintText: localeProvider.isRTL 
                          ? 'أدخل اسم الفئة' 
                          : 'Enter category name',
                      prefixIcon: const Icon(Icons.category),
                      border: const OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return localeProvider.isRTL 
                            ? 'اسم الفئة مطلوب' 
                            : 'Category name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Category Description Field
                  TextFormField(
                    controller: _descriptionController,
                    textDirection: localeProvider.isRTL 
                        ? TextDirection.rtl 
                        : TextDirection.ltr,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: localeProvider.isRTL ? 'الوصف (اختياري)' : 'Description (Optional)',
                      hintText: localeProvider.isRTL 
                          ? 'أدخل وصف الفئة' 
                          : 'Enter category description',
                      prefixIcon: const Icon(Icons.description),
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Icon Selector
                  _buildIconSelector(),
                  const SizedBox(height: 32),
                  
                  // Save Button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveCategory,
                      child: _isLoading
                          ? const CircularProgressIndicator(color: Colors.white)
                          : Text(
                              widget.category != null
                                  ? (localeProvider.isRTL ? 'تحديث الفئة' : 'Update Category')
                                  : (localeProvider.isRTL ? 'إضافة الفئة' : 'Add Category'),
                              style: const TextStyle(fontSize: 16),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
