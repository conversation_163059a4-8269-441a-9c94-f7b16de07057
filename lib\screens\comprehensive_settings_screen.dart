import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/localization_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/store_settings_provider.dart';
import '../providers/backup_provider.dart';
import 'store_settings_screen.dart';
import 'backup_settings_screen.dart';
import 'notification_settings_screen.dart';

class ComprehensiveSettingsScreen extends StatefulWidget {
  const ComprehensiveSettingsScreen({super.key});

  @override
  State<ComprehensiveSettingsScreen> createState() => _ComprehensiveSettingsScreenState();
}

class _ComprehensiveSettingsScreenState extends State<ComprehensiveSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocalizationProvider>(
          builder: (context, localization, _) => Text(
            localization.translate('settings'),
          ),
        ),
        elevation: 0,
      ),
      body: Consumer<LocalizationProvider>(
        builder: (context, localization, _) => ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // Store Information Section
            _buildSectionHeader(context, localization.translate('store_name')),
            _buildStoreInfoCard(context),
            
            const SizedBox(height: 16),
            
            // Language & Currency Section
            _buildSectionHeader(context, localization.translate('general_settings')),
            _buildLanguageCard(context),
            _buildCurrencyCard(context),
            _buildThemeCard(context),
            
            const SizedBox(height: 16),
            
            // Notifications Section
            _buildSectionHeader(context, localization.translate('notifications')),
            _buildNotificationCard(context),
            
            const SizedBox(height: 16),
            
            // Backup & Data Section
            _buildSectionHeader(context, localization.translate('backup')),
            _buildBackupCard(context),
            
            const SizedBox(height: 16),
            
            // System Section
            _buildSectionHeader(context, 'System'),
            _buildSystemCard(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildStoreInfoCard(BuildContext context) {
    return Consumer<StoreSettingsProvider>(
      builder: (context, storeSettings, _) => Card(
        child: ListTile(
          leading: Icon(
            FontAwesomeIcons.store,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: Text(storeSettings.storeInfo.name.isEmpty 
              ? 'Configure Store Information' 
              : storeSettings.storeInfo.name),
          subtitle: Text(storeSettings.storeInfo.address.isEmpty 
              ? 'Tap to set up your store details' 
              : storeSettings.storeInfo.address),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const StoreSettingsScreen(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLanguageCard(BuildContext context) {
    return Consumer<LocalizationProvider>(
      builder: (context, localization, _) => Card(
        child: ListTile(
          leading: Icon(
            FontAwesomeIcons.language,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: Text(localization.translate('language')),
          subtitle: Text(_getLanguageName(localization.currentLanguage)),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () => _showLanguageDialog(context),
        ),
      ),
    );
  }

  Widget _buildCurrencyCard(BuildContext context) {
    return Consumer2<CurrencyProvider, LocalizationProvider>(
      builder: (context, currency, localization, _) => Card(
        child: ListTile(
          leading: Icon(
            FontAwesomeIcons.moneyBill,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: Text(localization.translate('currency')),
          subtitle: Text(currency.getCurrencyName(localization.languageCode)),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (currency.isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
          onTap: () => _showCurrencyDialog(context),
        ),
      ),
    );
  }

  Widget _buildThemeCard(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, theme, _) => Card(
        child: ListTile(
          leading: Icon(
            theme.isDarkMode ? FontAwesomeIcons.moon : FontAwesomeIcons.sun,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('Theme'),
          subtitle: Text(theme.isDarkMode ? 'Dark Mode' : 'Light Mode'),
          trailing: Switch(
            value: theme.isDarkMode,
            onChanged: (value) => theme.toggleTheme(),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationCard(BuildContext context) {
    return Consumer<NotificationProvider>(
      builder: (context, notifications, _) => Card(
        child: ListTile(
          leading: Icon(
            FontAwesomeIcons.bell,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('Notifications'),
          subtitle: Text('${notifications.unreadCount} unread notifications'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const NotificationSettingsScreen(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBackupCard(BuildContext context) {
    return Consumer<BackupProvider>(
      builder: (context, backup, _) => Card(
        child: ListTile(
          leading: Icon(
            FontAwesomeIcons.database,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: const Text('Backup & Restore'),
          subtitle: Text(backup.lastBackupDate != null 
              ? 'Last backup: ${_formatDate(backup.lastBackupDate!)}' 
              : 'No backups yet'),
          trailing: const Icon(Icons.arrow_forward_ios, size: 16),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const BackupSettingsScreen(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSystemCard(BuildContext context) {
    return Card(
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              FontAwesomeIcons.circleInfo,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: const Text('About App'),
            subtitle: const Text('Version 1.0.0'),
            onTap: () => _showAboutDialog(context),
          ),
          ListTile(
            leading: Icon(
              FontAwesomeIcons.broom,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: const Text('Clear Cache'),
            subtitle: const Text('Free up storage space'),
            onTap: () => _clearCache(context),
          ),
        ],
      ),
    );
  }

  String _getLanguageName(SupportedLanguage language) {
    switch (language) {
      case SupportedLanguage.arabic:
        return 'العربية';
      case SupportedLanguage.english:
        return 'English';
      case SupportedLanguage.french:
        return 'Français';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Consumer<LocalizationProvider>(
        builder: (context, localization, _) => AlertDialog(
          title: Text(localization.translate('language')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: SupportedLanguage.values.map((language) {
              return RadioListTile<SupportedLanguage>(
                title: Text(_getLanguageName(language)),
                value: language,
                groupValue: localization.currentLanguage,
                onChanged: (value) {
                  if (value != null) {
                    localization.setLanguage(value);
                    Navigator.pop(context);
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localization.translate('cancel')),
            ),
          ],
        ),
      ),
    );
  }

  void _showCurrencyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Consumer2<CurrencyProvider, LocalizationProvider>(
        builder: (context, currency, localization, _) => AlertDialog(
          title: Text(localization.translate('currency')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: SupportedCurrency.values.map((currencyType) {
              final info = currency.allCurrencies[currencyType]!;
              return RadioListTile<SupportedCurrency>(
                title: Text('${info.nameEn} (${info.code})'),
                subtitle: Text(info.symbol),
                value: currencyType,
                groupValue: currency.currentCurrency,
                onChanged: (value) {
                  if (value != null) {
                    currency.setCurrency(value);
                    Navigator.pop(context);
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(localization.translate('cancel')),
            ),
            TextButton(
              onPressed: () {
                currency.updateExchangeRates();
                Navigator.pop(context);
              },
              child: const Text('Update Rates'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'POS App',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.point_of_sale, size: 48),
      children: [
        const Text('A comprehensive Point of Sale application with multi-language support, backup functionality, and real-time notifications.'),
      ],
    );
  }

  void _clearCache(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Are you sure you want to clear the app cache? This will free up storage space but may slow down the app temporarily.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Implement cache clearing logic here
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Cache cleared successfully')),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }
}
