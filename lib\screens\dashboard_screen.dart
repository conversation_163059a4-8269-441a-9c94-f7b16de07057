import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui' as ui;
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/dashboard_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/utils/rtl_helper.dart';
import 'package:pos_app/screens/product_form_new.dart';
import 'package:pos_app/screens/customer_form_screen.dart';
import 'package:pos_app/screens/pos_screen.dart';
import 'package:pos_app/screens/invoice_details_screen.dart';
import 'package:pos_app/utils/translations.dart';
import 'package:intl/intl.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load dashboard data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DashboardProvider>(
        context,
        listen: false,
      ).loadDashboardData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<LocaleProvider, DashboardProvider, CurrencyProvider>(
      builder: (
        context,
        localeProvider,
        dashboardProvider,
        currencyProvider,
        _,
      ) {
        final theme = Theme.of(context);

        return Scaffold(
          body: Directionality(
            textDirection: RTLHelper.getTextDirection(context),
            child: RefreshIndicator(
              onRefresh: () => dashboardProvider.loadDashboardData(),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    _buildHeader(localeProvider, theme),
                    const SizedBox(height: 24),

                    // Daily Summary Section
                    _buildDailySummarySection(
                      localeProvider,
                      theme,
                      dashboardProvider,
                      currencyProvider,
                    ),
                    const SizedBox(height: 24),

                    // Quick Actions Section
                    _buildQuickActionsSection(localeProvider),
                    const SizedBox(height: 24),

                    // Stats Grid with real data
                    _buildStatsGrid(
                      localeProvider,
                      theme,
                      dashboardProvider,
                      currencyProvider,
                    ),
                    const SizedBox(height: 24),

                    // Recent Transactions with real data
                    _buildRecentTransactions(
                      localeProvider,
                      theme,
                      dashboardProvider,
                      currencyProvider,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(LocaleProvider localeProvider, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade600, Colors.green.shade800],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Icon(FontAwesomeIcons.chartLine, color: Colors.white, size: 32),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localeProvider.isRTL ? 'لوحة التحكم' : 'Dashboard',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textDirection:
                      localeProvider.isRTL
                          ? ui.TextDirection.rtl
                          : ui.TextDirection.ltr,
                ),
                const SizedBox(height: 4),
                Text(
                  localeProvider.isRTL
                      ? 'مرحباً بك في نظام إدارة نقاط البيع'
                      : 'Welcome to your POS Management System',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                  textDirection:
                      localeProvider.isRTL
                          ? ui.TextDirection.rtl
                          : ui.TextDirection.ltr,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(LocaleProvider localeProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade700, Colors.green.shade900],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.green.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localeProvider.isRTL ? 'الإجراءات السريعة' : 'Quick Actions',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textDirection:
                localeProvider.isRTL
                    ? ui.TextDirection.rtl
                    : ui.TextDirection.ltr,
          ),
          const SizedBox(height: 16),
          LayoutBuilder(
            builder: (context, constraints) {
              final screenWidth = constraints.maxWidth;
              int crossAxisCount = 2;
              double childAspectRatio = 1.2;

              if (screenWidth > 1200) {
                crossAxisCount = 4;
                childAspectRatio = 1.0;
              } else if (screenWidth > 800) {
                crossAxisCount = 3;
                childAspectRatio = 1.1;
              } else if (screenWidth < 400) {
                crossAxisCount = 1;
                childAspectRatio = 2.0;
              }

              return GridView.count(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: childAspectRatio,
                children: [
                  _buildQuickActionCard(
                    icon: Icons.add,
                    title: localeProvider.isRTL ? 'إضافة منتج' : 'Add Product',
                    onTap:
                        () => Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const ProductFormScreen(),
                          ),
                        ),
                    localeProvider: localeProvider,
                  ),
                  _buildQuickActionCard(
                    icon: FontAwesomeIcons.userPlus,
                    title: localeProvider.isRTL ? 'إضافة عميل' : 'Add Customer',
                    onTap:
                        () => Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const CustomerFormScreen(),
                          ),
                        ),
                    localeProvider: localeProvider,
                  ),
                  _buildQuickActionCard(
                    icon: FontAwesomeIcons.cashRegister,
                    title: localeProvider.isRTL ? 'بيع جديد' : 'New Sale',
                    onTap:
                        () => Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const POSScreen(),
                          ),
                        ),
                    localeProvider: localeProvider,
                  ),
                  _buildQuickActionCard(
                    icon: FontAwesomeIcons.chartBar,
                    title: localeProvider.isRTL ? 'التقارير' : 'Reports',
                    onTap: () {
                      // Navigate to reports
                    },
                    localeProvider: localeProvider,
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    required LocaleProvider localeProvider,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.white, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
              textDirection:
                  localeProvider.isRTL
                      ? ui.TextDirection.rtl
                      : ui.TextDirection.ltr,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsGrid(
    LocaleProvider localeProvider,
    ThemeData theme,
    DashboardProvider dashboardProvider,
    CurrencyProvider currencyProvider,
  ) {
    if (dashboardProvider.isLoading) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32.0),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (dashboardProvider.error != null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Icon(
                FontAwesomeIcons.triangleExclamation,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                localeProvider.isRTL
                    ? 'خطأ في تحميل البيانات'
                    : 'Error loading data',
                style: theme.textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              Text(dashboardProvider.error!),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => dashboardProvider.loadDashboardData(),
                child: Text(localeProvider.isRTL ? 'إعادة المحاولة' : 'Retry'),
              ),
            ],
          ),
        ),
      );
    }

    final stats = dashboardProvider.stats;
    if (stats == null) {
      return const SizedBox.shrink();
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        int crossAxisCount = 2;

        if (screenWidth > 1200) {
          crossAxisCount = 4;
        } else if (screenWidth > 800) {
          crossAxisCount = 3;
        } else if (screenWidth < 400) {
          crossAxisCount = 1;
        }

        return GridView.count(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            _buildStatCard(
              title: localeProvider.isRTL ? 'العملاء' : 'Customers',
              value: '${stats.totalCustomers}',
              icon: FontAwesomeIcons.userGroup,
              color: Colors.blue,
              localeProvider: localeProvider,
              theme: theme,
            ),
            _buildStatCard(
              title: localeProvider.isRTL ? 'المبيعات اليومية' : 'Daily Sales',
              value: currencyProvider.formatCurrency(stats.dailySales),
              icon: FontAwesomeIcons.coins,
              color: Colors.green,
              localeProvider: localeProvider,
              theme: theme,
            ),
            _buildStatCard(
              title: localeProvider.isRTL ? 'المنتجات' : 'Products',
              value: '${stats.totalProducts}',
              icon: FontAwesomeIcons.box,
              color: Colors.purple,
              localeProvider: localeProvider,
              theme: theme,
            ),
            _buildStatCard(
              title: localeProvider.isRTL ? 'مخزون قليل' : 'Low Stock',
              value: '${stats.lowStockProducts}',
              icon: FontAwesomeIcons.triangleExclamation,
              color: Colors.orange,
              localeProvider: localeProvider,
              theme: theme,
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecentTransactions(
    LocaleProvider localeProvider,
    ThemeData theme,
    DashboardProvider dashboardProvider,
    CurrencyProvider currencyProvider,
  ) {
    final stats = dashboardProvider.stats;
    if (stats == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localeProvider.isRTL
                      ? 'المعاملات الأخيرة'
                      : 'Recent Transactions',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textDirection:
                      localeProvider.isRTL
                          ? ui.TextDirection.rtl
                          : ui.TextDirection.ltr,
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to invoices screen
                    // TODO: Implement navigation
                  },
                  child: Text(localeProvider.isRTL ? 'عرض الكل' : 'View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (stats.recentInvoices.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(
                        FontAwesomeIcons.receipt,
                        size: 48,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        localeProvider.isRTL
                            ? 'لا توجد معاملات حديثة'
                            : 'No recent transactions',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...stats.recentInvoices
                  .take(5)
                  .map(
                    (invoice) => _buildTransactionItem(
                      invoice,
                      localeProvider,
                      theme,
                      currencyProvider,
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required LocaleProvider localeProvider,
    required ThemeData theme,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                color: Colors.grey[700],
              ),
              textDirection:
                  localeProvider.isRTL
                      ? ui.TextDirection.rtl
                      : ui.TextDirection.ltr,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: theme.textTheme.headlineMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
              textDirection:
                  localeProvider.isRTL
                      ? ui.TextDirection.rtl
                      : ui.TextDirection.ltr,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(
    invoice,
    LocaleProvider localeProvider,
    ThemeData theme,
    CurrencyProvider currencyProvider,
  ) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => InvoiceDetailsScreen(invoice: invoice),
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.only(bottom: 12.0),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Center(
                child: Icon(
                  FontAwesomeIcons.receipt,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    invoice.customerName ??
                        (localeProvider.isRTL ? 'عميل نقدي' : 'Cash Customer'),
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textDirection:
                        localeProvider.isRTL
                            ? ui.TextDirection.rtl
                            : ui.TextDirection.ltr,
                  ),
                  Text(
                    '${localeProvider.isRTL ? 'فاتورة رقم' : 'Invoice'} ${invoice.invoiceNumber}',
                    style: theme.textTheme.bodySmall,
                  ),
                  Text(
                    DateFormat('dd/MM/yyyy HH:mm').format(invoice.date),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  currencyProvider.formatCurrency(invoice.finalAmount),
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      invoice.status,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getStatusText(invoice.status, localeProvider.isRTL),
                    style: TextStyle(
                      color: _getStatusColor(invoice.status),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'partial':
        return Colors.orange;
      case 'unpaid':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status, bool isRTL) {
    switch (status.toLowerCase()) {
      case 'paid':
        return isRTL ? 'مدفوعة' : 'Paid';
      case 'partial':
        return isRTL ? 'جزئية' : 'Partial';
      case 'unpaid':
        return isRTL ? 'غير مدفوعة' : 'Unpaid';
      default:
        return status;
    }
  }

  Widget _buildDailySummarySection(
    LocaleProvider localeProvider,
    ThemeData theme,
    DashboardProvider dashboardProvider,
    CurrencyProvider currencyProvider,
  ) {
    if (dashboardProvider.isLoading) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: const Padding(
          padding: EdgeInsets.all(32.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    final stats = dashboardProvider.stats;
    if (stats == null) {
      return const SizedBox.shrink();
    }

    final dailySummary = stats.dailySummary;
    final languageCode = localeProvider.locale.languageCode;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade600,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    FontAwesomeIcons.calendarDay,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  AppTranslations.translate('daily_summary', languageCode),
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade800,
                  ),
                  textDirection:
                      localeProvider.isRTL
                          ? ui.TextDirection.rtl
                          : ui.TextDirection.ltr,
                ),
              ],
            ),
            const SizedBox(height: 20),
            LayoutBuilder(
              builder: (context, constraints) {
                final screenWidth = constraints.maxWidth;
                int crossAxisCount = 2;
                double childAspectRatio = 1.8;

                if (screenWidth > 1200) {
                  crossAxisCount = 4;
                  childAspectRatio = 1.5;
                } else if (screenWidth > 800) {
                  crossAxisCount = 3;
                  childAspectRatio = 1.6;
                } else if (screenWidth < 400) {
                  crossAxisCount = 1;
                  childAspectRatio = 2.5;
                }

                return GridView.count(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  childAspectRatio: childAspectRatio,
                  children: [
                    _buildDailySummaryCard(
                      title: AppTranslations.translate(
                        'products_sold_today',
                        languageCode,
                      ),
                      value: '${dailySummary.productsSoldCount}',
                      subtitle:
                          dailySummary.productsSoldList.isNotEmpty
                              ? dailySummary.productsSoldList.take(3).join(', ')
                              : AppTranslations.translate(
                                'no_data',
                                languageCode,
                              ),
                      icon: FontAwesomeIcons.cartShopping,
                      color: Colors.green,
                      localeProvider: localeProvider,
                      theme: theme,
                    ),
                    _buildDailySummaryCard(
                      title: AppTranslations.translate(
                        'daily_revenue',
                        languageCode,
                      ),
                      value: currencyProvider.formatCurrency(
                        dailySummary.dailyRevenue,
                      ),
                      subtitle:
                          '${AppTranslations.translate('total', languageCode)} ${AppTranslations.translate('daily_revenue', languageCode).toLowerCase()}',
                      icon: FontAwesomeIcons.coins,
                      color: Colors.blue,
                      localeProvider: localeProvider,
                      theme: theme,
                    ),
                    _buildDailySummaryCard(
                      title: AppTranslations.translate(
                        'daily_profit',
                        languageCode,
                      ),
                      value: currencyProvider.formatCurrency(
                        dailySummary.dailyProfit,
                      ),
                      subtitle:
                          '${((dailySummary.dailyRevenue > 0 ? dailySummary.dailyProfit / dailySummary.dailyRevenue : 0) * 100).toStringAsFixed(1)}% margin',
                      icon: FontAwesomeIcons.chartLine,
                      color: Colors.purple,
                      localeProvider: localeProvider,
                      theme: theme,
                    ),
                    _buildDailySummaryCard(
                      title: AppTranslations.translate(
                        'daily_expenses',
                        languageCode,
                      ),
                      value: currencyProvider.formatCurrency(
                        dailySummary.dailyExpenses,
                      ),
                      subtitle:
                          '${AppTranslations.translate('total', languageCode)} ${AppTranslations.translate('daily_expenses', languageCode).toLowerCase()}',
                      icon: FontAwesomeIcons.receipt,
                      color: Colors.orange,
                      localeProvider: localeProvider,
                      theme: theme,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailySummaryCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required LocaleProvider localeProvider,
    required ThemeData theme,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(icon, color: color, size: 16),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                  textDirection:
                      localeProvider.isRTL
                          ? ui.TextDirection.rtl
                          : ui.TextDirection.ltr,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textDirection:
                localeProvider.isRTL
                    ? ui.TextDirection.rtl
                    : ui.TextDirection.ltr,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey[500]),
            textDirection:
                localeProvider.isRTL
                    ? ui.TextDirection.rtl
                    : ui.TextDirection.ltr,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
