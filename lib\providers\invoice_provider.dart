import 'package:flutter/foundation.dart';
import 'package:pos_app/models/invoice.dart';
import 'package:pos_app/db/database_helper.dart';

class InvoiceProvider with ChangeNotifier {
  List<Invoice> _invoices = [];
  bool _isLoading = false;
  DateTime? _startDate;
  DateTime? _endDate;
  String _selectedStatus = 'all';
  String _searchQuery = '';

  List<Invoice> get invoices => _filterInvoices();
  bool get isLoading => _isLoading;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  String get selectedStatus => _selectedStatus;
  String get searchQuery => _searchQuery;

  void setDateRange(DateTime? start, DateTime? end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }

  void setStatus(String status) {
    _selectedStatus = status;
    notifyListeners();
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  List<Invoice> _filterInvoices() {
    return _invoices.where((invoice) {
      // Search filter
      if (_searchQuery.isNotEmpty) {
        final searchTerm = _searchQuery.toLowerCase();
        if (!invoice.invoiceNumber.toLowerCase().contains(searchTerm) &&
            !(invoice.customerName?.toLowerCase().contains(searchTerm) ??
                false)) {
          return false;
        }
      }

      // Date range filter
      if (_startDate != null && _endDate != null) {
        final invoiceDate = invoice.date;
        if (!invoiceDate.isAfter(_startDate!) ||
            !invoiceDate.isBefore(_endDate!.add(const Duration(days: 1)))) {
          return false;
        }
      }

      // Status filter
      if (_selectedStatus != 'all') {
        return invoice.status.toLowerCase() == _selectedStatus;
      }

      return true;
    }).toList();
  }

  Future<void> loadInvoices() async {
    _isLoading = true;
    notifyListeners();

    try {
      _invoices = await DatabaseHelper.instance.getAllInvoices();
      _invoices.sort(
        (a, b) => b.date.compareTo(a.date),
      ); // Sort by date descending
    } catch (e) {
      debugPrint('Error loading invoices: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> loadInvoicesByDateRange(DateTime start, DateTime end) async {
    _isLoading = true;
    notifyListeners();

    try {
      _invoices = await DatabaseHelper.instance.getInvoicesByDateRange(
        start,
        end,
      );
      _invoices.sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      debugPrint('Error loading invoices by date range: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> loadInvoicesByCustomerId(int customerId) async {
    _isLoading = true;
    notifyListeners();

    try {
      _invoices = await DatabaseHelper.instance.getInvoicesByCustomerId(
        customerId,
      );
      _invoices.sort((a, b) => b.date.compareTo(a.date));
    } catch (e) {
      debugPrint('Error loading invoices by customer ID: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> refreshInvoices() async {
    await loadInvoices();
  }

  Future<void> addInvoice(Invoice invoice) async {
    try {
      final id = await DatabaseHelper.instance.insertInvoice(invoice);
      final newInvoice = invoice.copyWith(id: id);
      _invoices.add(newInvoice);
      notifyListeners();
    } catch (e) {
      print('Error adding invoice: $e');
    }
  }

  Future<void> updateInvoice(Invoice invoice) async {
    try {
      await DatabaseHelper.instance.updateInvoice(invoice);
      final index = _invoices.indexWhere((i) => i.id == invoice.id);
      if (index >= 0) {
        _invoices[index] = invoice;
        notifyListeners();
      }
    } catch (e) {
      print('Error updating invoice: $e');
    }
  }

  Future<void> deleteInvoice(int id) async {
    try {
      await DatabaseHelper.instance.deleteInvoice(id);
      _invoices.removeWhere((invoice) => invoice.id == id);
      notifyListeners();
    } catch (e) {
      print('Error deleting invoice: $e');
    }
  }

  Future<List<Invoice>> getInvoicesByDateRange(
    DateTime start,
    DateTime end,
  ) async {
    try {
      return await DatabaseHelper.instance.getInvoicesByDateRange(start, end);
    } catch (e) {
      print('Error getting invoices by date range: $e');
      return [];
    }
  }

  Future<List<Invoice>> getInvoicesByCustomerId(int customerId) async {
    try {
      return await DatabaseHelper.instance.getInvoicesByCustomerId(customerId);
    } catch (e) {
      print('Error getting invoices by customer ID: $e');
      return [];
    }
  }

  Future<Invoice?> getInvoiceByNumber(String invoiceNumber) async {
    try {
      return await DatabaseHelper.instance.getInvoiceByNumber(invoiceNumber);
    } catch (e) {
      print('Error getting invoice by number: $e');
      return null;
    }
  }

  double getTotalRevenue() {
    return _invoices.fold(0, (sum, invoice) => sum + invoice.finalAmount);
  }

  double getTotalRevenueForPeriod(DateTime start, DateTime end) {
    return _invoices
        .where(
          (invoice) =>
              invoice.date.isAfter(start.subtract(const Duration(days: 1))) &&
              invoice.date.isBefore(end.add(const Duration(days: 1))),
        )
        .fold(0, (sum, invoice) => sum + invoice.finalAmount);
  }

  int getInvoiceCount() {
    return _invoices.length;
  }

  int getInvoiceCountForPeriod(DateTime start, DateTime end) {
    return _invoices
        .where(
          (invoice) =>
              invoice.date.isAfter(start.subtract(const Duration(days: 1))) &&
              invoice.date.isBefore(end.add(const Duration(days: 1))),
        )
        .length;
  }

  List<String> getPaymentMethods() {
    return _invoices.map((invoice) => invoice.paymentMethod).toSet().toList();
  }

  Map<String, double> getRevenueByPaymentMethod() {
    final Map<String, double> result = {};
    for (var invoice in _invoices) {
      final method = invoice.paymentMethod;
      if (result.containsKey(method)) {
        result[method] = result[method]! + invoice.finalAmount;
      } else {
        result[method] = invoice.finalAmount;
      }
    }
    return result;
  }

  // Get unpaid invoices for a specific customer
  List<Invoice> getUnpaidInvoicesByCustomerId(int customerId) {
    return _invoices
        .where(
          (invoice) =>
              invoice.customerId == customerId &&
              (invoice.status == 'unpaid' || invoice.status == 'partial'),
        )
        .toList();
  }

  // Get all unpaid invoices
  List<Invoice> get unpaidInvoices {
    return _invoices
        .where(
          (invoice) =>
              invoice.status == 'unpaid' || invoice.status == 'partial',
        )
        .toList();
  }

  // Calculate total debt for a customer
  double getCustomerDebt(int customerId) {
    return getUnpaidInvoicesByCustomerId(
      customerId,
    ).fold(0, (sum, invoice) => sum + invoice.finalAmount);
  }

  // Get all invoices (unfiltered)
  List<Invoice> get allInvoices => _invoices;
}
