# تطبيق نقاط البيع (POS App)

تطبيق نقاط بيع متكامل مع دعم كامل للغة العربية وواجهة مستخدم حديثة.

## الميزات الرئيسية

- واجهة مستخدم حديثة وسهلة الاستخدام
- دعم كامل للغة العربية واتجاه RTL
- شريط تنقل سفلي متطور مع 4 خيارات رئيسية: لوحة التحكم، نقطة البيع، التقارير، الملف الشخصي
- لوحة تحكم مع إحصاءات وتقارير مرئية
- نظام إدارة المنتجات والفئات
- قاعدة بيانات محلية للمنتجات والمعاملات
- إدارة المخزون وتتبع المبيعات

## بدء الاستخدام

### إعداد دعم اللغة العربية

للتأكد من عمل دعم اللغة العربية بشكل صحيح، يجب اتباع الخطوات التالية:

1. تأكد من تنزيل خط Cairo للغة العربية ووضعه في مجلد `assets/fonts/`:
   - Cairo-Regular.ttf
   - Cairo-Bold.ttf
   - Cairo-SemiBold.ttf
   - Cairo-Medium.ttf

2. تأكد من تنزيل خط Poppins للغة الإنجليزية ووضعه في نفس المجلد:
   - Poppins-Regular.ttf
   - Poppins-Bold.ttf
   - Poppins-SemiBold.ttf
   - Poppins-Medium.ttf

3. قم بتشغيل الأمر التالي لتحديث التطبيق والتأكد من تحميل جميع الحزم المطلوبة:
   ```
   flutter pub get
   ```

4. يمكنك تغيير اللغة من خلال شاشة الإعدادات > اللغة.

### الميزات الجديدة

- نظام تنقل حديث مع شريط تنقل سفلي
- لوحة تحكم مع إحصائيات ورسوم بيانية
- نظام تصفية المنتجات حسب الفئات
- دعم كامل لاتجاه RTL للغة العربية
- شاشة خاصة لإعدادات اللغة

## المراجع

- [Flutter الموقع الرسمي](https://flutter.dev)
- [Flutter التوثيق](https://docs.flutter.dev/)
