import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum SupportedLanguage { english, arabic, french, spanish, german }

class LanguageInfo {
  final String code;
  final String countryCode;
  final String nativeName;
  final String englishName;
  final bool isRTL;

  const LanguageInfo({
    required this.code,
    required this.countryCode,
    required this.nativeName,
    required this.englishName,
    required this.isRTL,
  });
}

class LocaleProvider extends ChangeNotifier {
  // Default language changed to English
  Locale _locale = const Locale('en', 'US');
  bool _isRTL = false;
  SupportedLanguage _currentLanguage = SupportedLanguage.english;

  // Language information map
  static const Map<SupportedLanguage, LanguageInfo> _languageInfo = {
    SupportedLanguage.english: LanguageInfo(
      code: 'en',
      countryCode: 'US',
      nativeName: 'English',
      englishName: 'English',
      isRTL: false,
    ),
    SupportedLanguage.arabic: LanguageInfo(
      code: 'ar',
      countryCode: 'DZ',
      nativeName: 'العربية',
      englishName: 'Arabic',
      isRTL: true,
    ),
    SupportedLanguage.french: LanguageInfo(
      code: 'fr',
      countryCode: 'FR',
      nativeName: 'Français',
      englishName: 'French',
      isRTL: false,
    ),
    SupportedLanguage.spanish: LanguageInfo(
      code: 'es',
      countryCode: 'ES',
      nativeName: 'Español',
      englishName: 'Spanish',
      isRTL: false,
    ),
    SupportedLanguage.german: LanguageInfo(
      code: 'de',
      countryCode: 'DE',
      nativeName: 'Deutsch',
      englishName: 'German',
      isRTL: false,
    ),
  };

  LocaleProvider() {
    _loadLocale();
  }

  // Getters
  Locale get locale => _locale;
  bool get isRTL => _isRTL;
  SupportedLanguage get currentLanguage => _currentLanguage;
  LanguageInfo get currentLanguageInfo => _languageInfo[_currentLanguage]!;
  Map<SupportedLanguage, LanguageInfo> get allLanguages => _languageInfo;

  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final String languageCode = prefs.getString('languageCode') ?? 'en';
    final String countryCode = prefs.getString('countryCode') ?? 'US';

    // Find the language by code
    _currentLanguage =
        _languageInfo.entries
            .firstWhere(
              (entry) => entry.value.code == languageCode,
              orElse:
                  () => const MapEntry(
                    SupportedLanguage.english,
                    LanguageInfo(
                      code: 'en',
                      countryCode: 'US',
                      nativeName: 'English',
                      englishName: 'English',
                      isRTL: false,
                    ),
                  ),
            )
            .key;

    final info = _languageInfo[_currentLanguage]!;
    _locale = Locale(info.code, info.countryCode);
    _isRTL = info.isRTL;
    notifyListeners();
  }

  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;

    // Find the language by locale
    final languageEntry = _languageInfo.entries.firstWhere(
      (entry) => entry.value.code == locale.languageCode,
      orElse:
          () => const MapEntry(
            SupportedLanguage.english,
            LanguageInfo(
              code: 'en',
              countryCode: 'US',
              nativeName: 'English',
              englishName: 'English',
              isRTL: false,
            ),
          ),
    );

    _currentLanguage = languageEntry.key;
    final info = languageEntry.value;
    _locale = Locale(info.code, info.countryCode);
    _isRTL = info.isRTL;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', info.code);
    await prefs.setString('countryCode', info.countryCode);

    notifyListeners();
  }

  Future<void> setLanguage(SupportedLanguage language) async {
    if (_currentLanguage == language) return;

    _currentLanguage = language;
    final info = _languageInfo[language]!;
    _locale = Locale(info.code, info.countryCode);
    _isRTL = info.isRTL;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('languageCode', info.code);
    await prefs.setString('countryCode', info.countryCode);

    notifyListeners();
  }

  void toggleDirection() {
    // Cycle through languages: English -> Arabic -> French -> Spanish -> German -> English
    switch (_currentLanguage) {
      case SupportedLanguage.english:
        setLanguage(SupportedLanguage.arabic);
        break;
      case SupportedLanguage.arabic:
        setLanguage(SupportedLanguage.french);
        break;
      case SupportedLanguage.french:
        setLanguage(SupportedLanguage.spanish);
        break;
      case SupportedLanguage.spanish:
        setLanguage(SupportedLanguage.german);
        break;
      case SupportedLanguage.german:
        setLanguage(SupportedLanguage.english);
        break;
    }
  }

  void setLanguageFromCode(String languageCode) {
    final languageEntry = _languageInfo.entries.firstWhere(
      (entry) => entry.value.code == languageCode,
      orElse:
          () => const MapEntry(
            SupportedLanguage.english,
            LanguageInfo(
              code: 'en',
              countryCode: 'US',
              nativeName: 'English',
              englishName: 'English',
              isRTL: false,
            ),
          ),
    );
    setLanguage(languageEntry.key);
  }

  String getLanguageName(SupportedLanguage language) {
    return _languageInfo[language]?.nativeName ?? 'English';
  }

  String getLanguageNameInEnglish(SupportedLanguage language) {
    return _languageInfo[language]?.englishName ?? 'English';
  }
}
