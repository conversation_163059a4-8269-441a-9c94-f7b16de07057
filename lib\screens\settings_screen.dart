import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/screens/language_settings_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _darkMode = false;
  bool _enableNotifications = true;
  String _currencyValue = 'دينار جزائري (DZD)';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocaleProvider>(
          builder: (context, localeProvider, _) => Text(
            localeProvider.isRTL ? 'الإعدادات' : 'Settings',
          ),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Store Information Section
          _buildSectionHeader(context, 'معلومات المتجر'),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.store,
            title: 'اسم المتجر',
            subtitle: 'متجر التطبيق',
            onTap: () {
              // Edit store name
            },
          ),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.phone,
            title: 'رقم الهاتف',
            subtitle: '+213 123456789',
            onTap: () {
              // Edit phone number
            },
          ),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.locationDot,
            title: 'العنوان',
            subtitle: 'الجزائر العاصمة، الجزائر',
            onTap: () {
              // Edit address
            },
          ),

          // Appearance Section
          _buildSectionHeader(context, 'المظهر'),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.palette,
            title: 'سمة التطبيق',
            trailing: Switch(
              value: _darkMode,
              onChanged: (value) {
                setState(() {
                  _darkMode = value;
                });
              },
              activeColor: theme.colorScheme.primary,
            ),
          ),

          // Preferences Section
          _buildSectionHeader(context, 'التفضيلات'),
          Consumer<LocaleProvider>(
            builder: (context, localeProvider, _) => _buildSettingCard(
              context,
              icon: FontAwesomeIcons.language,
              title: localeProvider.isRTL ? 'اللغة' : 'Language',
              subtitle: localeProvider.isRTL ? 'العربية' : 'English',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LanguageSettingsScreen(),
                  ),
                );
              },
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: Icon(
                      FontAwesomeIcons.rightLeft,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    onPressed: () {
                      localeProvider.toggleDirection();
                    },
                    tooltip: localeProvider.isRTL
                        ? 'تبديل إلى الإنجليزية'
                        : 'Switch to Arabic',
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.moneyBill,
            title: 'العملة',
            subtitle: _currencyValue,
            onTap: () {
              _showCurrencyDialog(context);
            },
          ),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.bell,
            title: 'الإشعارات',
            trailing: Switch(
              value: _enableNotifications,
              onChanged: (value) {
                setState(() {
                  _enableNotifications = value;
                });
              },
              activeColor: theme.colorScheme.primary,
            ),
          ),


          
          // System Section
          _buildSectionHeader(context, 'النظام'),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.database,
            title: 'النسخ الاحتياطي واستعادة البيانات',
            onTap: () {
              // Backup/restore options
            },
          ),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.broom,
            title: 'مسح ذاكرة التخزين المؤقت',
            onTap: () {
              // Clear cache
            },
          ),
          _buildSettingCard(
            context,
            icon: FontAwesomeIcons.circleInfo,
            title: 'حول التطبيق',
            subtitle: 'الإصدار 1.0.0',
            onTap: () {
              // Show about dialog
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSettingCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    String? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Icon(icon, color: theme.colorScheme.primary),
        title: Text(title),
        subtitle: subtitle != null ? Text(subtitle) : null,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }

  void _showCurrencyDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر العملة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('دينار جزائري (DZD)'),
              value: 'دينار جزائري (DZD)',
              groupValue: _currencyValue,
              onChanged: (value) {
                setState(() {
                  _currencyValue = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('دولار أمريكي (USD)'),
              value: 'دولار أمريكي (USD)',
              groupValue: _currencyValue,
              onChanged: (value) {
                setState(() {
                  _currencyValue = value!;
                });
                Navigator.pop(context);
              },
            ),
            RadioListTile<String>(
              title: const Text('يورو (EUR)'),
              value: 'يورو (EUR)',
              groupValue: _currencyValue,
              onChanged: (value) {
                setState(() {
                  _currencyValue = value!;
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }
}
