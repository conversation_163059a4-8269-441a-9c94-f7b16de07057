import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_barcode_scanner/flutter_barcode_scanner.dart';
import 'dart:ui' as ui;
import 'package:pos_app/providers/cart_provider.dart';
import 'package:pos_app/providers/product_provider.dart';
import 'package:pos_app/providers/category_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';
import 'package:pos_app/providers/currency_provider.dart';
import 'package:pos_app/screens/checkout_screen.dart';
import 'package:pos_app/screens/products_screen.dart';
import 'package:pos_app/screens/product_selection_screen.dart';
import 'package:pos_app/screens/invoices_screen.dart';
import 'package:pos_app/screens/categories_screen.dart';
import 'package:pos_app/models/cart_item.dart';

class POSScreen extends StatefulWidget {
  const POSScreen({super.key});

  @override
  State<POSScreen> createState() => _POSScreenState();
}

class _POSScreenState extends State<POSScreen> with TickerProviderStateMixin {
  Widget _buildCartView() {
    return Consumer3<CartProvider, LocaleProvider, CurrencyProvider>(
      builder: (context, cartProvider, localeProvider, currencyProvider, _) {
        final cartItems = cartProvider.items;
        final total = cartProvider.totalAmount;
        final theme = Theme.of(context);

        if (cartItems.isEmpty) {
          return _buildEmptyCart(localeProvider, theme);
        }

        return Column(
          children: [
            // Cart Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.blue.shade800],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.shopping_cart,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    localeProvider.isRTL ? 'السلة' : 'Cart',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${cartItems.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Cart Items
            Expanded(
              child: Container(
                decoration: BoxDecoration(color: Colors.grey.shade50),
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  itemCount: cartItems.length,
                  itemBuilder: (context, index) {
                    final item = cartItems[index];
                    return _buildEnhancedCartItem(
                      item,
                      cartProvider,
                      localeProvider,
                      currencyProvider,
                      theme,
                      index,
                    );
                  },
                ),
              ),
            ),

            // Cart Total
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade600, Colors.green.shade800],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        localeProvider.isRTL ? 'المجموع الفرعي:' : 'Subtotal:',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        currencyProvider.formatCurrency(total),
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        localeProvider.isRTL ? 'المجموع الكلي:' : 'Total:',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        currencyProvider.formatCurrency(total),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  final _searchController = TextEditingController();
  bool _isSearching = false;
  late TabController _tabController;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // Load categories and products
    Future.microtask(() {
      Provider.of<CategoryProvider>(context, listen: false).loadCategories();
      Provider.of<ProductProvider>(context, listen: false).loadProducts();
    });
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _scanBarcode(BuildContext context) async {
    try {
      final barcode = await FlutterBarcodeScanner.scanBarcode(
        '#ff6666',
        'Cancel',
        true,
        ScanMode.BARCODE,
      );

      if (barcode != '-1') {
        final productProvider = Provider.of<ProductProvider>(
          context,
          listen: false,
        );
        final product = await productProvider.getProductByBarcode(barcode);

        if (product != null) {
          final cartProvider = Provider.of<CartProvider>(
            context,
            listen: false,
          );
          cartProvider.addItem(product);
        } else {
          if (!mounted) return;
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('Product not found')));
        }
      }
    } catch (e) {
      print('Error scanning barcode: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  const Icon(
                    Icons.point_of_sale,
                    size: 70,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'POS System',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.point_of_sale),
              title: const Text('Point of Sale'),
              selected: true,
              onTap: () {
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: const Icon(Icons.inventory),
              title: const Text('Products'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ProductsScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.category),
              title: const Text('Categories'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CategoriesScreen(),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.receipt_long),
              title: const Text('Invoices'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const InvoicesScreen(),
                  ),
                );
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Navigate to settings screen
              },
            ),
          ],
        ),
      ),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black87),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        title:
            !_isSearching
                ? Consumer<LocaleProvider>(
                  builder:
                      (context, localeProvider, _) => Text(
                        localeProvider.isRTL ? 'نقطة البيع' : 'Point of Sale',
                        style: const TextStyle(
                          color: Colors.black87,
                          fontWeight: FontWeight.w600,
                          fontSize: 20,
                        ),
                      ),
                )
                : Consumer<LocaleProvider>(
                  builder:
                      (context, localeProvider, _) => TextField(
                        controller: _searchController,
                        textDirection:
                            localeProvider.isRTL
                                ? ui.TextDirection.rtl
                                : ui.TextDirection.ltr,
                        decoration: InputDecoration(
                          hintText:
                              localeProvider.isRTL
                                  ? 'بحث عن منتج...'
                                  : 'Search products...',
                          border: InputBorder.none,
                        ),
                        onChanged: (value) {
                          Provider.of<ProductProvider>(context, listen: false)
                              .searchQuery = value;
                        },
                      ),
                ),
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: Colors.black87,
            ),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                  Provider.of<ProductProvider>(context, listen: false)
                      .searchQuery = '';
                }
              });
            },
            tooltip:
                Provider.of<LocaleProvider>(context, listen: false).isRTL
                    ? 'بحث'
                    : 'Search',
          ),
          IconButton(
            icon: const Icon(Icons.qr_code_scanner, color: Colors.black87),
            onPressed: () => _scanBarcode(context),
            tooltip:
                Provider.of<LocaleProvider>(context, listen: false).isRTL
                    ? 'مسح الباركود'
                    : 'Scan Barcode',
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1),
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.grey.shade200,
                  Colors.grey.shade100,
                  Colors.grey.shade200,
                ],
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Action Buttons Row
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: Consumer<LocaleProvider>(
              builder: (context, localeProvider, _) {
                return Row(
                  children: [
                    // Add Products Button
                    Expanded(
                      flex: 2,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder:
                                  (context) => const ProductSelectionScreen(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.add_shopping_cart),
                        label: Text(
                          localeProvider.isRTL
                              ? 'إضافة منتجات'
                              : 'Add Products',
                          style: const TextStyle(fontSize: 16),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Clear Cart Button
                    Consumer<CartProvider>(
                      builder: (context, cartProvider, _) {
                        return Expanded(
                          flex: 1,
                          child: OutlinedButton.icon(
                            onPressed:
                                cartProvider.itemCount > 0
                                    ? () => _showClearCartDialog(
                                      context,
                                      localeProvider,
                                    )
                                    : null,
                            icon: const Icon(Icons.clear_all),
                            label: Text(
                              localeProvider.isRTL ? 'إفراغ' : 'Clear',
                              style: const TextStyle(fontSize: 14),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              side: BorderSide(
                                color:
                                    cartProvider.itemCount > 0
                                        ? Colors.red
                                        : Colors.grey,
                              ),
                              foregroundColor:
                                  cartProvider.itemCount > 0
                                      ? Colors.red
                                      : Colors.grey,
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                );
              },
            ),
          ),

          // Cart View
          Expanded(child: _buildCartView()),
        ],
      ),
      floatingActionButton: Consumer2<CartProvider, LocaleProvider>(
        builder: (context, cartProvider, localeProvider, _) {
          if (cartProvider.itemCount == 0) return const SizedBox();

          return FloatingActionButton.extended(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (context) => const CheckoutScreen()),
              );
            },
            icon: const Icon(Icons.payment),
            label: Text(
              localeProvider.isRTL ? 'متابعة الدفع' : 'Proceed to Checkout',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          );
        },
      ),
    );
  }

  void _showClearCartDialog(
    BuildContext context,
    LocaleProvider localeProvider,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            localeProvider.isRTL ? 'تأكيد إفراغ السلة' : 'Confirm Clear Cart',
            textDirection:
                localeProvider.isRTL
                    ? ui.TextDirection.rtl
                    : ui.TextDirection.ltr,
          ),
          content: Text(
            localeProvider.isRTL
                ? 'هل أنت متأكد من أنك تريد إفراغ السلة؟ سيتم حذف جميع المنتجات.'
                : 'Are you sure you want to clear the cart? All products will be removed.',
            textDirection:
                localeProvider.isRTL
                    ? ui.TextDirection.rtl
                    : ui.TextDirection.ltr,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Provider.of<CartProvider>(context, listen: false).clear();
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      localeProvider.isRTL
                          ? 'تم إفراغ السلة بنجاح'
                          : 'Cart cleared successfully',
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(localeProvider.isRTL ? 'إفراغ' : 'Clear'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyCart(LocaleProvider localeProvider, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            localeProvider.isRTL ? 'السلة فارغة' : 'Cart is Empty',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            localeProvider.isRTL
                ? 'اضغط على "إضافة منتجات" لبدء التسوق'
                : 'Tap "Add Products" to start shopping',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedCartItem(
    CartItem item,
    CartProvider cartProvider,
    LocaleProvider localeProvider,
    CurrencyProvider currencyProvider,
    ThemeData theme,
    int index,
  ) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.only(bottom: 8),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              // Product Image/Icon
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory_2,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),

              // Product Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.product.name,
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currencyProvider.formatCurrency(item.product.price),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),

              // Quantity Controls
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Decrease Button
                    InkWell(
                      onTap: () {
                        if (item.quantity > 1) {
                          cartProvider.updateQuantity(
                            item.product.id!,
                            item.quantity - 1,
                          );
                        }
                      },
                      borderRadius: BorderRadius.circular(6),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        child: Icon(
                          Icons.remove,
                          size: 16,
                          color: item.quantity > 1 ? Colors.red : Colors.grey,
                        ),
                      ),
                    ),

                    // Quantity Display
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      child: Text(
                        '${item.quantity}',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    // Increase Button
                    InkWell(
                      onTap: () {
                        cartProvider.updateQuantity(
                          item.product.id!,
                          item.quantity + 1,
                        );
                      },
                      borderRadius: BorderRadius.circular(6),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        child: Icon(
                          Icons.add,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 8),

              // Total Price
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    currencyProvider.formatCurrency(item.total),
                    style: theme.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  InkWell(
                    onTap:
                        () => _showRemoveItemDialog(
                          item,
                          cartProvider,
                          localeProvider,
                        ),
                    borderRadius: BorderRadius.circular(4),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      child: Icon(
                        Icons.delete_outline,
                        size: 16,
                        color: Colors.red.shade400,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showRemoveItemDialog(
    CartItem item,
    CartProvider cartProvider,
    LocaleProvider localeProvider,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              localeProvider.isRTL ? 'إزالة المنتج' : 'Remove Product',
            ),
            content: Text(
              localeProvider.isRTL
                  ? 'هل تريد إزالة "${item.product.name}" من السلة؟'
                  : 'Do you want to remove "${item.product.name}" from cart?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(localeProvider.isRTL ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  cartProvider.removeItem(item.product.id!);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        localeProvider.isRTL
                            ? 'تم إزالة المنتج من السلة'
                            : 'Product removed from cart',
                      ),
                      backgroundColor: Colors.green,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(localeProvider.isRTL ? 'إزالة' : 'Remove'),
              ),
            ],
          ),
    );
  }
}
