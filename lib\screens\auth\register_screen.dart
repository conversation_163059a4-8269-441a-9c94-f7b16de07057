import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../providers/auth_provider.dart';
import '../../providers/locale_provider.dart';
import '../main_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();

  // Personal Information Controllers
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // Business Information Controllers
  final _businessNameController = TextEditingController();
  final _businessTypeController = TextEditingController();
  final _addressController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _acceptTerms = false;
  int _currentStep = 0;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _businessTypes = [
    'Retail Store',
    'Restaurant',
    'Pharmacy',
    'Grocery Store',
    'Electronics Store',
    'Clothing Store',
    'Service Business',
    'Other',
  ];

  final List<String> _businessTypesAr = [
    'متجر تجزئة',
    'مطعم',
    'صيدلية',
    'بقالة',
    'متجر إلكترونيات',
    'متجر ملابس',
    'خدمات',
    'أخرى',
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _businessNameController.dispose();
    _businessTypeController.dispose();
    _addressController.dispose();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep == 0) {
      if (_validatePersonalInfo()) {
        setState(() => _currentStep = 1);
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else {
      _handleRegister();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() => _currentStep = 0);
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validatePersonalInfo() {
    if (_fullNameController.text.trim().isEmpty) {
      _showError('الاسم الكامل مطلوب', 'Full name is required');
      return false;
    }
    if (_emailController.text.trim().isEmpty ||
        !_emailController.text.contains('@')) {
      _showError('البريد الإلكتروني غير صحيح', 'Invalid email address');
      return false;
    }
    if (_phoneController.text.trim().isEmpty) {
      _showError('رقم الهاتف مطلوب', 'Phone number is required');
      return false;
    }
    if (_passwordController.text.length < 6) {
      _showError(
        'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        'Password must be at least 6 characters',
      );
      return false;
    }
    if (_passwordController.text != _confirmPasswordController.text) {
      _showError('كلمات المرور غير متطابقة', 'Passwords do not match');
      return false;
    }
    return true;
  }

  void _showError(String messageAr, String messageEn) {
    final isRTL = Provider.of<LocaleProvider>(context, listen: false).isRTL;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isRTL ? messageAr : messageEn),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _handleRegister() async {
    if (!_validateBusinessInfo()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final success = await authProvider.register(
      fullName: _fullNameController.text.trim(),
      email: _emailController.text.trim(),
      phone: _phoneController.text.trim(),
      password: _passwordController.text,
      businessName: _businessNameController.text.trim(),
      businessType: _businessTypeController.text.trim(),
      address: _addressController.text.trim(),
    );

    if (success && mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => const MainScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  bool _validateBusinessInfo() {
    if (_businessNameController.text.trim().isEmpty) {
      _showError('اسم النشاط التجاري مطلوب', 'Business name is required');
      return false;
    }
    if (!_acceptTerms) {
      _showError(
        'يجب الموافقة على الشروط والأحكام',
        'You must accept the terms and conditions',
      );
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isRTL = Provider.of<LocaleProvider>(context).isRTL;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isRTL ? 'إنشاء حساب جديد' : 'Create Account',
          style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                children: [
                  // Progress Indicator
                  _buildProgressIndicator(theme, isRTL),

                  // Form Content
                  Expanded(
                    child: PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildPersonalInfoStep(theme, isRTL),
                        _buildBusinessInfoStep(theme, isRTL),
                      ],
                    ),
                  ),

                  // Navigation Buttons
                  _buildNavigationButtons(theme, isRTL),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(ThemeData theme, bool isRTL) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Step 1
          Expanded(
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  isRTL ? 'المعلومات الشخصية' : 'Personal Info',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),

          // Connector
          Expanded(
            child: Container(
              height: 2,
              margin: const EdgeInsets.only(bottom: 30),
              color:
                  _currentStep >= 1
                      ? theme.colorScheme.primary
                      : theme.colorScheme.outline.withValues(alpha: 0.3),
            ),
          ),

          // Step 2
          Expanded(
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color:
                        _currentStep >= 1
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.business,
                    color:
                        _currentStep >= 1
                            ? Colors.white
                            : theme.colorScheme.outline,
                    size: 20,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  isRTL ? 'معلومات النشاط' : 'Business Info',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color:
                        _currentStep >= 1
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline.withValues(alpha: 0.7),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoStep(ThemeData theme, bool isRTL) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Text(
              isRTL ? 'المعلومات الشخصية' : 'Personal Information',
              style: GoogleFonts.cairo(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            Text(
              isRTL
                  ? 'أدخل معلوماتك الشخصية لإنشاء حسابك'
                  : 'Enter your personal information to create your account',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 40),

            // Full Name Field
            TextFormField(
              controller: _fullNameController,
              decoration: InputDecoration(
                labelText: isRTL ? 'الاسم الكامل' : 'Full Name',
                hintText: isRTL ? 'أدخل اسمك الكامل' : 'Enter your full name',
                prefixIcon: const Icon(FontAwesomeIcons.user),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
            ),

            const SizedBox(height: 20),

            // Email Field
            TextFormField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              textDirection: TextDirection.ltr,
              decoration: InputDecoration(
                labelText: isRTL ? 'البريد الإلكتروني' : 'Email',
                hintText: isRTL ? 'أدخل بريدك الإلكتروني' : 'Enter your email',
                prefixIcon: const Icon(FontAwesomeIcons.envelope),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
            ),

            const SizedBox(height: 20),

            // Phone Field
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: isRTL ? 'رقم الهاتف' : 'Phone Number',
                hintText: isRTL ? 'أدخل رقم هاتفك' : 'Enter your phone number',
                prefixIcon: const Icon(FontAwesomeIcons.phone),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
            ),

            const SizedBox(height: 20),

            // Password Field
            TextFormField(
              controller: _passwordController,
              obscureText: _obscurePassword,
              decoration: InputDecoration(
                labelText: isRTL ? 'كلمة المرور' : 'Password',
                hintText: isRTL ? 'أدخل كلمة المرور' : 'Enter your password',
                prefixIcon: const Icon(FontAwesomeIcons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscurePassword
                        ? FontAwesomeIcons.eyeSlash
                        : FontAwesomeIcons.eye,
                  ),
                  onPressed:
                      () =>
                          setState(() => _obscurePassword = !_obscurePassword),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
            ),

            const SizedBox(height: 20),

            // Confirm Password Field
            TextFormField(
              controller: _confirmPasswordController,
              obscureText: _obscureConfirmPassword,
              decoration: InputDecoration(
                labelText: isRTL ? 'تأكيد كلمة المرور' : 'Confirm Password',
                hintText:
                    isRTL ? 'أعد إدخال كلمة المرور' : 'Re-enter your password',
                prefixIcon: const Icon(FontAwesomeIcons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureConfirmPassword
                        ? FontAwesomeIcons.eyeSlash
                        : FontAwesomeIcons.eye,
                  ),
                  onPressed:
                      () => setState(
                        () =>
                            _obscureConfirmPassword = !_obscureConfirmPassword,
                      ),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
              ),
            ),

            const SizedBox(height: 20),

            // Password Requirements
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isRTL ? 'متطلبات كلمة المرور:' : 'Password Requirements:',
                    style: GoogleFonts.cairo(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isRTL
                        ? '• 6 أحرف على الأقل\n• يُفضل استخدام أحرف وأرقام'
                        : '• At least 6 characters\n• Preferably use letters and numbers',
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessInfoStep(ThemeData theme, bool isRTL) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Text(
            isRTL ? 'معلومات النشاط التجاري' : 'Business Information',
            style: GoogleFonts.cairo(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          Text(
            isRTL
                ? 'أدخل معلومات نشاطك التجاري لإكمال إعداد حسابك'
                : 'Enter your business information to complete your account setup',
            style: GoogleFonts.cairo(
              fontSize: 16,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),

          // Business Name Field
          TextFormField(
            controller: _businessNameController,
            decoration: InputDecoration(
              labelText: isRTL ? 'اسم النشاط التجاري' : 'Business Name',
              hintText:
                  isRTL ? 'أدخل اسم نشاطك التجاري' : 'Enter your business name',
              prefixIcon: const Icon(FontAwesomeIcons.store),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: theme.colorScheme.surface,
            ),
          ),

          const SizedBox(height: 20),

          // Business Type Dropdown
          DropdownButtonFormField<String>(
            value:
                _businessTypeController.text.isEmpty
                    ? null
                    : _businessTypeController.text,
            decoration: InputDecoration(
              labelText: isRTL ? 'نوع النشاط التجاري' : 'Business Type',
              prefixIcon: const Icon(FontAwesomeIcons.briefcase),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: theme.colorScheme.surface,
            ),
            items:
                (isRTL ? _businessTypesAr : _businessTypes).map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type, style: GoogleFonts.cairo()),
                  );
                }).toList(),
            onChanged: (String? value) {
              setState(() {
                _businessTypeController.text = value ?? '';
              });
            },
          ),

          const SizedBox(height: 20),

          // Address Field
          TextFormField(
            controller: _addressController,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: isRTL ? 'العنوان' : 'Address',
              hintText:
                  isRTL
                      ? 'أدخل عنوان نشاطك التجاري'
                      : 'Enter your business address',
              prefixIcon: const Icon(FontAwesomeIcons.locationDot),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: theme.colorScheme.surface,
            ),
          ),

          const SizedBox(height: 30),

          // Terms and Conditions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Checkbox(
                      value: _acceptTerms,
                      onChanged: (value) {
                        setState(() {
                          _acceptTerms = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(
                        isRTL
                            ? 'أوافق على الشروط والأحكام وسياسة الخصوصية'
                            : 'I agree to the Terms and Conditions and Privacy Policy',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        // TODO: Show terms and conditions
                      },
                      child: Text(
                        isRTL ? 'الشروط والأحكام' : 'Terms & Conditions',
                        style: GoogleFonts.cairo(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 20,
                      color: theme.colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    TextButton(
                      onPressed: () {
                        // TODO: Show privacy policy
                      },
                      child: Text(
                        isRTL ? 'سياسة الخصوصية' : 'Privacy Policy',
                        style: GoogleFonts.cairo(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons(ThemeData theme, bool isRTL) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Container(
          padding: const EdgeInsets.all(24),
          child: Row(
            children: [
              // Back Button
              if (_currentStep > 0)
                Expanded(
                  child: OutlinedButton(
                    onPressed: authProvider.isLoading ? null : _previousStep,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: theme.colorScheme.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      isRTL ? 'السابق' : 'Previous',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ),

              if (_currentStep > 0) const SizedBox(width: 16),

              // Next/Create Account Button
              Expanded(
                flex: _currentStep > 0 ? 1 : 2,
                child: ElevatedButton(
                  onPressed: authProvider.isLoading ? null : _nextStep,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child:
                      authProvider.isLoading
                          ? const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : Text(
                            _currentStep == 0
                                ? (isRTL ? 'التالي' : 'Next')
                                : (isRTL ? 'إنشاء الحساب' : 'Create Account'),
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
