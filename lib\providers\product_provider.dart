import 'package:flutter/foundation.dart';
import 'package:pos_app/models/product.dart';
import 'package:pos_app/db/database_helper.dart';

class ProductProvider with ChangeNotifier {
  List<Product> _products = [];
  List<String> _categories = [];
  bool _isLoading = false;
  String _searchQuery = '';
  String _selectedCategory = 'All';

  ProductProvider() {
    loadProducts();
  }

  List<Product> get products {
    if (_searchQuery.isEmpty && _selectedCategory == 'All') {
      return [..._products];
    } else if (_searchQuery.isNotEmpty && _selectedCategory == 'All') {
      return _products
          .where(
            (product) =>
                product.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                product.barcode.contains(_searchQuery),
          )
          .toList();
    } else if (_searchQuery.isEmpty && _selectedCategory != 'All') {
      return _products
          .where((product) => product.category == _selectedCategory)
          .toList();
    } else {
      return _products
          .where(
            (product) =>
                (product.name.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ) ||
                    product.barcode.contains(_searchQuery)) &&
                product.category == _selectedCategory,
          )
          .toList();
    }
  }

  bool get isLoading => _isLoading;
  List<String> get categories => ['All', ..._categories];
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;

  set searchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  set selectedCategory(String category) {
    _selectedCategory = category;
    notifyListeners();
  }

  Future<void> loadProducts() async {
    _isLoading = true;
    notifyListeners();

    try {
      if (kIsWeb) {
        // For web, use sample products since SQLite is not fully supported
        _products = [
          Product(
            id: 1,
            name: 'Coffee',
            barcode: '1234567890123',
            price: 3.99,
            costPrice: 2.25,
            wholesalePrice: 2.99,
            enableWholesale: true,
            minWholesaleQty: 10,
            stock: 100,
            category: 'Beverages',
          ),
          Product(
            id: 2,
            name: 'Green Tea',
            barcode: '1234567890124',
            price: 2.99,
            costPrice: 1.75,
            wholesalePrice: 2.25,
            enableWholesale: true,
            minWholesaleQty: 10,
            stock: 80,
            category: 'Beverages',
          ),
          Product(
            id: 3,
            name: 'Chocolate Muffin',
            barcode: '1234567890125',
            price: 2.49,
            costPrice: 1.20,
            wholesalePrice: 1.85,
            enableWholesale: true,
            minWholesaleQty: 20,
            stock: 50,
            category: 'Bakery',
          ),
          Product(
            id: 4,
            name: 'Sandwich',
            barcode: '1234567890126',
            price: 5.99,
            costPrice: 3.50,
            wholesalePrice: 4.75,
            enableWholesale: true,
            minWholesaleQty: 10,
            stock: 30,
            category: 'Food',
          ),
          Product(
            id: 5,
            name: 'Bottled Water',
            barcode: '1234567890127',
            price: 1.49,
            costPrice: 0.65,
            wholesalePrice: 0.99,
            enableWholesale: true,
            minWholesaleQty: 24,
            stock: 200,
            category: 'Beverages',
          ),
          Product(
            id: 6,
            name: 'Chocolate Bar',
            barcode: '1234567890128',
            price: 1.99,
            costPrice: 1.10,
            wholesalePrice: 1.50,
            enableWholesale: true,
            minWholesaleQty: 20,
            stock: 75,
            category: 'Snacks',
          ),
        ];
      } else {
        // For mobile, use SQLite database
        _products = await DatabaseHelper.instance.getAllProducts();
      }

      _updateCategories();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      print('Error loading products: $e');
      _isLoading = false;
      notifyListeners();
    }
  }

  void _updateCategories() {
    final categories =
        _products.map((product) => product.category).toSet().toList();
    categories.sort();
    _categories = categories;
  }

  Future<void> addProduct(Product product) async {
    try {
      if (kIsWeb) {
        // For web, just add to the in-memory list
        final id = _products.isNotEmpty ? _products.last.id! + 1 : 1;
        final newProduct = product.copyWith(id: id);
        _products.add(newProduct);
      } else {
        // For mobile, use SQLite database
        final id = await DatabaseHelper.instance.insertProduct(product);
        final newProduct = product.copyWith(id: id);
        _products.add(newProduct);
      }
      _updateCategories();
      notifyListeners();
    } catch (e) {
      print('Error adding product: $e');
    }
  }

  Future<void> updateProduct(Product product) async {
    try {
      if (!kIsWeb) {
        // For mobile, use SQLite database
        await DatabaseHelper.instance.updateProduct(product);
      }

      // Update in-memory list for both web and mobile
      final index = _products.indexWhere((p) => p.id == product.id);
      if (index >= 0) {
        _products[index] = product;
        _updateCategories();
        notifyListeners();
      }
    } catch (e) {
      print('Error updating product: $e');
    }
  }

  Future<void> deleteProduct(int id) async {
    try {
      if (!kIsWeb) {
        // For mobile, use SQLite database
        await DatabaseHelper.instance.deleteProduct(id);
      }

      // Update in-memory list for both web and mobile
      _products.removeWhere((product) => product.id == id);
      _updateCategories();
      notifyListeners();
    } catch (e) {
      print('Error deleting product: $e');
    }
  }

  // Method to get products filtered by category
  List<Product> getFilteredProducts(String category) {
    if (category == 'كل الفئات' || category == 'All') {
      return [..._products];
    } else {
      return _products
          .where((product) => product.category == category)
          .toList();
    }
  }

  Future<Product?> getProductByBarcode(String barcode) async {
    try {
      if (kIsWeb) {
        // For web, search in the in-memory list
        return _products.firstWhere(
          (product) => product.barcode == barcode,
          orElse: () => throw Exception('Product not found'),
        );
      } else {
        // For mobile, use SQLite database
        return await DatabaseHelper.instance.getProductByBarcode(barcode);
      }
    } catch (e) {
      print('Error getting product by barcode: $e');
      return null;
    }
  }
}
