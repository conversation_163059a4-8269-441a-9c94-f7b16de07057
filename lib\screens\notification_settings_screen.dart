import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../providers/notification_provider.dart';
import '../providers/localization_provider.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Consumer<LocalizationProvider>(
          builder:
              (context, localization, _) =>
                  Text(localization.translate('notifications')),
        ),
        actions: [
          Consumer<NotificationProvider>(
            builder:
                (context, notifications, _) =>
                    notifications.unreadCount > 0
                        ? IconButton(
                          icon: Badge(
                            label: Text(notifications.unreadCount.toString()),
                            child: const Icon(FontAwesomeIcons.bell),
                          ),
                          onPressed:
                              () => _showNotificationHistory(notifications),
                        )
                        : IconButton(
                          icon: const Icon(FontAwesomeIcons.bell),
                          onPressed:
                              () => _showNotificationHistory(notifications),
                        ),
          ),
        ],
      ),
      body: Consumer<NotificationProvider>(
        builder:
            (context, notifications, _) => ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // Notification Status Card
                _buildStatusCard(notifications),

                const SizedBox(height: 16),

                // Notification Types Settings
                _buildNotificationTypesCard(notifications),

                const SizedBox(height: 16),

                // General Settings
                _buildGeneralSettingsCard(notifications),

                const SizedBox(height: 16),

                // Test Notifications
                _buildTestNotificationsCard(notifications),

                const SizedBox(height: 16),

                // Recent Notifications
                _buildRecentNotificationsCard(notifications),
              ],
            ),
      ),
    );
  }

  Widget _buildStatusCard(NotificationProvider notifications) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  FontAwesomeIcons.bell,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Notification Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusItem(
                  'Total',
                  notifications.notifications.length.toString(),
                  FontAwesomeIcons.list,
                ),
                _buildStatusItem(
                  'Unread',
                  notifications.unreadCount.toString(),
                  FontAwesomeIcons.envelope,
                ),
                _buildStatusItem(
                  'Today',
                  _getTodayNotificationsCount(notifications).toString(),
                  FontAwesomeIcons.calendar,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildNotificationTypesCard(NotificationProvider notifications) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Types',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Low Stock Alerts'),
              subtitle: const Text(
                'Get notified when products are running low',
              ),
              value: notifications.lowStockEnabled,
              onChanged: notifications.setLowStockEnabled,
              secondary: const Icon(FontAwesomeIcons.boxOpen),
            ),

            if (notifications.lowStockEnabled) ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    const Text('Alert when stock is below: '),
                    Expanded(
                      child: Slider(
                        value: notifications.lowStockThreshold.toDouble(),
                        min: 1,
                        max: 50,
                        divisions: 49,
                        label: notifications.lowStockThreshold.toString(),
                        onChanged: (value) {
                          notifications.setLowStockThreshold(value.toInt());
                        },
                      ),
                    ),
                    Text(notifications.lowStockThreshold.toString()),
                  ],
                ),
              ),
            ],

            SwitchListTile(
              title: const Text('Sale Notifications'),
              subtitle: const Text('Get notified when sales are completed'),
              value: notifications.saleNotificationsEnabled,
              onChanged: notifications.setSaleNotificationsEnabled,
              secondary: const Icon(FontAwesomeIcons.cashRegister),
            ),

            SwitchListTile(
              title: const Text('Backup Notifications'),
              subtitle: const Text('Get notified about backup status'),
              value: notifications.backupNotificationsEnabled,
              onChanged: notifications.setBackupNotificationsEnabled,
              secondary: const Icon(FontAwesomeIcons.database),
            ),

            SwitchListTile(
              title: const Text('System Alerts'),
              subtitle: const Text('Get notified about system events'),
              value: notifications.systemAlertsEnabled,
              onChanged: notifications.setSystemAlertsEnabled,
              secondary: const Icon(FontAwesomeIcons.triangleExclamation),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralSettingsCard(NotificationProvider notifications) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'General Settings',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            SwitchListTile(
              title: const Text('Sound'),
              subtitle: const Text('Play sound for notifications'),
              value: notifications.soundEnabled,
              onChanged: notifications.setSoundEnabled,
              secondary: const Icon(FontAwesomeIcons.volumeHigh),
            ),

            SwitchListTile(
              title: const Text('Vibration'),
              subtitle: const Text('Vibrate for notifications'),
              value: notifications.vibrationEnabled,
              onChanged: notifications.setVibrationEnabled,
              secondary: const Icon(FontAwesomeIcons.mobile),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestNotificationsCard(NotificationProvider notifications) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Notifications',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed:
                      () => notifications.showLowStockAlert('Test Product', 5),
                  icon: const Icon(FontAwesomeIcons.boxOpen, size: 16),
                  label: const Text('Low Stock'),
                ),
                ElevatedButton.icon(
                  onPressed:
                      () => notifications.showSaleCompletedNotification(
                        150.0,
                        'DZD',
                      ),
                  icon: const Icon(FontAwesomeIcons.cashRegister, size: 16),
                  label: const Text('Sale Complete'),
                ),
                ElevatedButton.icon(
                  onPressed:
                      () => notifications.showBackupCompletedNotification(),
                  icon: const Icon(FontAwesomeIcons.database, size: 16),
                  label: const Text('Backup Done'),
                ),
                ElevatedButton.icon(
                  onPressed:
                      () => notifications.showNotification(
                        title: 'System Alert',
                        body: 'This is a test system notification',
                        type: NotificationType.systemAlert,
                      ),
                  icon: const Icon(
                    FontAwesomeIcons.triangleExclamation,
                    size: 16,
                  ),
                  label: const Text('System Alert'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentNotificationsCard(NotificationProvider notifications) {
    final recentNotifications = notifications.notifications.take(5).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Notifications',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _showNotificationHistory(notifications),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (recentNotifications.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Column(
                    children: [
                      Icon(FontAwesomeIcons.bell, size: 48, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No notifications yet'),
                    ],
                  ),
                ),
              )
            else
              ...recentNotifications.map((notification) {
                return ListTile(
                  leading: Icon(
                    _getNotificationIcon(notification.type),
                    color:
                        notification.isRead
                            ? Colors.grey
                            : Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    notification.title,
                    style: TextStyle(
                      fontWeight:
                          notification.isRead
                              ? FontWeight.normal
                              : FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(notification.body),
                      Text(
                        _formatNotificationDate(notification.createdAt),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                  trailing:
                      notification.isRead
                          ? null
                          : Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              shape: BoxShape.circle,
                            ),
                          ),
                  onTap: () {
                    if (!notification.isRead) {
                      notifications.markAsRead(notification.id);
                    }
                  },
                );
              }),
          ],
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.lowStock:
      case NotificationType.outOfStock:
        return FontAwesomeIcons.boxOpen;
      case NotificationType.saleCompleted:
        return FontAwesomeIcons.cashRegister;
      case NotificationType.backupCompleted:
      case NotificationType.backupFailed:
        return FontAwesomeIcons.database;
      case NotificationType.systemAlert:
        return FontAwesomeIcons.triangleExclamation;
      case NotificationType.reminder:
        return FontAwesomeIcons.clock;
    }
  }

  String _formatNotificationDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  int _getTodayNotificationsCount(NotificationProvider notifications) {
    final today = DateTime.now();
    return notifications.notifications.where((notification) {
      return notification.createdAt.year == today.year &&
          notification.createdAt.month == today.month &&
          notification.createdAt.day == today.day;
    }).length;
  }

  void _showNotificationHistory(NotificationProvider notifications) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            builder:
                (context, scrollController) => Container(
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Notification History',
                              style: Theme.of(context).textTheme.titleLarge
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                            Row(
                              children: [
                                if (notifications.unreadCount > 0)
                                  TextButton(
                                    onPressed: notifications.markAllAsRead,
                                    child: const Text('Mark All Read'),
                                  ),
                                IconButton(
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder:
                                          (context) => AlertDialog(
                                            title: const Text(
                                              'Clear All Notifications',
                                            ),
                                            content: const Text(
                                              'Are you sure you want to clear all notifications?',
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed:
                                                    () =>
                                                        Navigator.pop(context),
                                                child: const Text('Cancel'),
                                              ),
                                              TextButton(
                                                onPressed: () {
                                                  notifications
                                                      .clearAllNotifications();
                                                  Navigator.pop(context);
                                                },
                                                child: const Text('Clear All'),
                                              ),
                                            ],
                                          ),
                                    );
                                  },
                                  icon: const Icon(FontAwesomeIcons.trash),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: notifications.notifications.length,
                          itemBuilder: (context, index) {
                            final notification =
                                notifications.notifications[index];
                            return ListTile(
                              leading: Icon(
                                _getNotificationIcon(notification.type),
                                color:
                                    notification.isRead
                                        ? Colors.grey
                                        : Theme.of(context).colorScheme.primary,
                              ),
                              title: Text(
                                notification.title,
                                style: TextStyle(
                                  fontWeight:
                                      notification.isRead
                                          ? FontWeight.normal
                                          : FontWeight.bold,
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(notification.body),
                                  Text(
                                    _formatNotificationDate(
                                      notification.createdAt,
                                    ),
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                ],
                              ),
                              trailing: PopupMenuButton(
                                itemBuilder:
                                    (context) => [
                                      if (!notification.isRead)
                                        PopupMenuItem(
                                          value: 'mark_read',
                                          child: const Row(
                                            children: [
                                              Icon(FontAwesomeIcons.check),
                                              SizedBox(width: 8),
                                              Text('Mark as Read'),
                                            ],
                                          ),
                                        ),
                                      PopupMenuItem(
                                        value: 'delete',
                                        child: const Row(
                                          children: [
                                            Icon(
                                              FontAwesomeIcons.trash,
                                              color: Colors.red,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              'Delete',
                                              style: TextStyle(
                                                color: Colors.red,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                onSelected: (value) {
                                  if (value == 'mark_read') {
                                    notifications.markAsRead(notification.id);
                                  } else if (value == 'delete') {
                                    notifications.deleteNotification(
                                      notification.id,
                                    );
                                  }
                                },
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }
}
