import 'package:flutter/foundation.dart';
import 'package:pos_app/db/database_helper.dart';
import 'package:pos_app/models/invoice.dart';

// جسر قاعدة البيانات - واجهة مبسطة للتعامل مع قاعدة البيانات
class DatabaseBridge {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  // إضافة فاتورة جديدة
  Future<int> addInvoice(Invoice invoice) async {
    try {
      // إضافة الفاتورة الرئيسية
      final invoiceMap = {
        'invoice_number': invoice.invoiceNumber,
        'date': invoice.date.toIso8601String(),
        'customer_id': invoice.customerId,
        'customer_name': invoice.customerName,
        'total_amount': invoice.totalAmount,
        'discount_amount': invoice.discountAmount,
        'tax_amount': invoice.taxAmount,
        'final_amount': invoice.finalAmount,
        'payment_method': invoice.paymentMethod,
        'status': invoice.status,
        'notes': invoice.notes,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final invoiceId = await _dbHelper.insert('invoices', invoiceMap);

      // إضافة عناصر الفاتورة
      for (var item in invoice.items) {
        final itemMap = {
          'invoice_id': invoiceId,
          'product_id': item.productId,
          'product_name': item.productName,
          'quantity': item.quantity,
          'unit_price': item.unitPrice,
          'total_price': item.totalPrice,
        };
        await _dbHelper.insert('invoice_items', itemMap);
      }

      return invoiceId;
    } catch (e) {
      debugPrint('خطأ في إضافة الفاتورة: $e');
      return -1;
    }
  }

  // زيادة رقم الفاتورة التالي (نسخة مبسطة)
  Future<bool> incrementNextInvoiceNumber() async {
    // في هذه النسخة المبسطة، لا نقوم بتخزين رقم الفاتورة التالي
    return true;
  }

  // الحصول على إعدادات الفاتورة (نسخة مبسطة)
  Future<dynamic> getInvoiceSettings() async {
    // في هذه النسخة المبسطة، نعيد null
    return null;
  }

  // الحصول على إعدادات الطابعة النشطة (نسخة مبسطة)
  Future<dynamic> getActivePrinterSettings() async {
    // في هذه النسخة المبسطة، نعيد null
    return null;
  }
}
