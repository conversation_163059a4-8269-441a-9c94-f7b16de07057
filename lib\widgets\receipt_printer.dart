import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';
import 'package:pos_app/models/transaction.dart' as model;
import 'package:pos_app/models/invoice.dart';

class ReceiptPrinter {
  // طباعة إيصال (نسخة مبسطة)
  static Future<void> printReceipt(model.Transaction transaction) async {
    final pdf = pw.Document();
    final formatter = NumberFormat.currency(locale: 'en_US', symbol: 'DZD ');
    final dateFormatter = DateFormat('dd/MM/yyyy hh:mm a');

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.roll80,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text('RECEIPT',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 20)),
              pw.SizedBox(height: 8),
              pw.Text('Transaction ID: ${transaction.transactionId}'),
              pw.Text('Date: ${dateFormatter.format(transaction.date)}'),
              pw.SizedBox(height: 16),
              pw.Divider(),
              pw.SizedBox(height: 8),
              pw.Table(
                border: null,
                columnWidths: {
                  0: const pw.FlexColumnWidth(4),
                  1: const pw.FlexColumnWidth(1),
                  2: const pw.FlexColumnWidth(2),
                  3: const pw.FlexColumnWidth(2),
                },
                children: [
                  pw.TableRow(
                    children: [
                      pw.Text('Item',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      pw.Text('Qty',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                      pw.Text('Price',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          textAlign: pw.TextAlign.right),
                      pw.Text('Total',
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          textAlign: pw.TextAlign.right),
                    ],
                  ),
                  ...transaction.items.map(
                    (item) => pw.TableRow(
                      children: [
                        pw.Text(item.product.name),
                        pw.Text('${item.quantity}x'),
                        pw.Text(formatter.format(item.price),
                            textAlign: pw.TextAlign.right),
                        pw.Text(formatter.format(item.total),
                            textAlign: pw.TextAlign.right),
                      ],
                    ),
                  ),
                ],
              ),
              pw.SizedBox(height: 8),
              pw.Divider(),
              pw.SizedBox(height: 8),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('Total:',
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                  pw.Text(formatter.format(transaction.total),
                      style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
                ],
              ),
              pw.SizedBox(height: 4),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('Payment Method: ${transaction.paymentMethod}'),
                  pw.Text('Amount Paid: ${formatter.format(transaction.amountPaid)}'),
                ],
              ),
              pw.SizedBox(height: 4),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('Change:'),
                  pw.Text(formatter.format(transaction.change)),
                ],
              ),
              pw.SizedBox(height: 16),
              pw.Text('Thank you for your purchase!',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'Receipt_${transaction.transactionId}',
      format: PdfPageFormat.roll80,
    );
  }
  
  // طباعة فاتورة (نسخة مبسطة)
  Future<bool> printInvoice(Invoice invoice) async {
    // في هذه النسخة المبسطة، نطبع الفاتورة باستخدام PDF
    final pdf = pw.Document();
    final formatter = NumberFormat.currency(locale: 'en_US', symbol: 'DZD ');
    final dateFormatter = DateFormat('dd/MM/yyyy');
    
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.roll80,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text('INVOICE',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold, fontSize: 20)),
              pw.SizedBox(height: 8),
              pw.Text('Invoice Number: ${invoice.invoiceNumber}'),
              pw.Text('Date: ${invoice.date}'),
              pw.SizedBox(height: 16),
              pw.Divider(),
              pw.SizedBox(height: 16),
              pw.Text('Thank you for your business!',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
      name: 'Invoice_${invoice.invoiceNumber}',
      format: PdfPageFormat.roll80,
    );
    
    return true;
  }
}
