import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pos_app/providers/bluetooth_provider.dart';
import 'package:pos_app/providers/locale_provider.dart';

class PrinterSettingsScreen extends StatefulWidget {
  const PrinterSettingsScreen({super.key});

  @override
  State<PrinterSettingsScreen> createState() => _PrinterSettingsScreenState();
}

class _PrinterSettingsScreenState extends State<PrinterSettingsScreen> {
  String _selectedPaperSize = '58mm';
  bool _enableAutoCut = true;
  bool _printLogo = true;
  int _printCopies = 1;
  String _printDensity = 'Medium';

  @override
  Widget build(BuildContext context) {
    return Consumer2<BluetoothProvider, LocaleProvider>(
      builder: (context, bluetoothProvider, localeProvider, _) {
        final theme = Theme.of(context);
        final isRTL = localeProvider.isRTL;

        return Scaffold(
          appBar: AppBar(
            title: Text(isRTL ? 'إعدادات الطابعة' : 'Printer Settings'),
            elevation: 0,
            backgroundColor: theme.colorScheme.surface,
            foregroundColor: theme.colorScheme.onSurface,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Bluetooth Connection Section
                _buildSectionHeader(
                  isRTL ? 'اتصال البلوتوث' : 'Bluetooth Connection',
                  FontAwesomeIcons.bluetooth,
                  theme,
                ),
                const SizedBox(height: 12),
                _buildBluetoothCard(bluetoothProvider, theme, isRTL),

                const SizedBox(height: 24),

                // Printer Configuration Section
                _buildSectionHeader(
                  isRTL ? 'إعدادات الطباعة' : 'Print Configuration',
                  FontAwesomeIcons.gear,
                  theme,
                ),
                const SizedBox(height: 12),
                _buildPrintConfigCard(theme, isRTL),

                const SizedBox(height: 24),

                // Test Print Section
                _buildSectionHeader(
                  isRTL ? 'اختبار الطباعة' : 'Test Print',
                  FontAwesomeIcons.print,
                  theme,
                ),
                const SizedBox(height: 12),
                _buildTestPrintCard(bluetoothProvider, theme, isRTL),

                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title, IconData icon, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: theme.colorScheme.primary),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildBluetoothCard(
    BluetoothProvider bluetoothProvider,
    ThemeData theme,
    bool isRTL,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connection Status
            Row(
              children: [
                Icon(
                  bluetoothProvider.isConnected
                      ? FontAwesomeIcons.circleCheck
                      : FontAwesomeIcons.circleXmark,
                  color:
                      bluetoothProvider.isConnected ? Colors.green : Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        bluetoothProvider.isConnected
                            ? (isRTL ? 'متصل' : 'Connected')
                            : (isRTL ? 'غير متصل' : 'Disconnected'),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color:
                              bluetoothProvider.isConnected
                                  ? Colors.green
                                  : Colors.red,
                        ),
                      ),
                      if (bluetoothProvider.selectedPrinter != null)
                        Text(
                          bluetoothProvider.selectedPrinter!.name,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                    ],
                  ),
                ),
                if (bluetoothProvider.isScanning)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),

            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        bluetoothProvider.isScanning
                            ? null
                            : () => bluetoothProvider.startScan(),
                    icon: Icon(
                      bluetoothProvider.isScanning
                          ? FontAwesomeIcons.spinner
                          : FontAwesomeIcons.magnifyingGlass,
                      size: 16,
                    ),
                    label: Text(
                      bluetoothProvider.isScanning
                          ? (isRTL ? 'جاري البحث...' : 'Scanning...')
                          : (isRTL ? 'بحث عن طابعات' : 'Scan for Printers'),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                if (bluetoothProvider.isConnected) ...[
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () => bluetoothProvider.disconnect(),
                    icon: const Icon(FontAwesomeIcons.linkSlash, size: 16),
                    label: Text(isRTL ? 'قطع الاتصال' : 'Disconnect'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ],
            ),

            // Available Devices List
            if (bluetoothProvider.availableDevices.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                isRTL ? 'الطابعات المتاحة:' : 'Available Printers:',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              ...bluetoothProvider.availableDevices.map((device) {
                final isSelected =
                    bluetoothProvider.selectedPrinter?.address ==
                    device.address;
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  color: isSelected ? theme.colorScheme.primaryContainer : null,
                  child: ListTile(
                    leading: Icon(
                      FontAwesomeIcons.print,
                      color: isSelected ? theme.colorScheme.primary : null,
                    ),
                    title: Text(device.name),
                    subtitle: Text(device.address),
                    trailing:
                        isSelected
                            ? Icon(
                              FontAwesomeIcons.check,
                              color: theme.colorScheme.primary,
                            )
                            : null,
                    onTap: () => bluetoothProvider.connectToDevice(device),
                  ),
                );
              }),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPrintConfigCard(ThemeData theme, bool isRTL) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Paper Size
            _buildConfigTile(
              title: isRTL ? 'حجم الورق' : 'Paper Size',
              child: DropdownButton<String>(
                value: _selectedPaperSize,
                items:
                    ['58mm', '80mm'].map((size) {
                      return DropdownMenuItem(value: size, child: Text(size));
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPaperSize = value;
                    });
                  }
                },
              ),
            ),

            const Divider(),

            // Print Density
            _buildConfigTile(
              title: isRTL ? 'كثافة الطباعة' : 'Print Density',
              child: DropdownButton<String>(
                value: _printDensity,
                items:
                    ['Light', 'Medium', 'Dark'].map((density) {
                      return DropdownMenuItem(
                        value: density,
                        child: Text(density),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _printDensity = value;
                    });
                  }
                },
              ),
            ),

            const Divider(),

            // Print Copies
            _buildConfigTile(
              title: isRTL ? 'عدد النسخ' : 'Print Copies',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed:
                        _printCopies > 1
                            ? () {
                              setState(() {
                                _printCopies--;
                              });
                            }
                            : null,
                    icon: const Icon(Icons.remove),
                  ),
                  Text(
                    '$_printCopies',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed:
                        _printCopies < 5
                            ? () {
                              setState(() {
                                _printCopies++;
                              });
                            }
                            : null,
                    icon: const Icon(Icons.add),
                  ),
                ],
              ),
            ),

            const Divider(),

            // Auto Cut
            _buildSwitchTile(
              title: isRTL ? 'القطع التلقائي' : 'Auto Cut',
              subtitle:
                  isRTL
                      ? 'قطع الورق تلقائياً بعد الطباعة'
                      : 'Automatically cut paper after printing',
              value: _enableAutoCut,
              onChanged: (value) {
                setState(() {
                  _enableAutoCut = value;
                });
              },
            ),

            const Divider(),

            // Print Logo
            _buildSwitchTile(
              title: isRTL ? 'طباعة الشعار' : 'Print Logo',
              subtitle:
                  isRTL
                      ? 'إضافة شعار المتجر للفواتير'
                      : 'Include store logo on receipts',
              value: _printLogo,
              onChanged: (value) {
                setState(() {
                  _printLogo = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestPrintCard(
    BluetoothProvider bluetoothProvider,
    ThemeData theme,
    bool isRTL,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              FontAwesomeIcons.print,
              size: 48,
              color: theme.colorScheme.primary.withOpacity(0.7),
            ),
            const SizedBox(height: 16),
            Text(
              isRTL ? 'اختبار الطابعة' : 'Test Your Printer',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isRTL
                  ? 'اطبع صفحة اختبار للتأكد من عمل الطابعة بشكل صحيح'
                  : 'Print a test page to verify your printer is working correctly',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    bluetoothProvider.isConnected
                        ? () => _printTestPage(bluetoothProvider, isRTL)
                        : null,
                icon: const Icon(FontAwesomeIcons.print),
                label: Text(isRTL ? 'طباعة صفحة اختبار' : 'Print Test Page'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigTile({required String title, required Widget child}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
          child,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _printTestPage(BluetoothProvider bluetoothProvider, bool isRTL) {
    // Implement test print functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isRTL ? 'جاري طباعة صفحة الاختبار...' : 'Printing test page...',
        ),
        backgroundColor: Colors.blue,
      ),
    );

    // TODO: Implement actual test print
    bluetoothProvider.printTestPage();
  }
}
