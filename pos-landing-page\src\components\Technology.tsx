import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { 
  Smartphone, 
  Database, 
  Wifi, 
  Shield, 
  Zap, 
  Cloud,
  Code,
  Palette,
  Globe,
  Users,
  TrendingUp,
  Award
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

const Technology: React.FC = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  })

  // Animated counter hook
  const useCounter = (end: number, duration: number = 2000) => {
    const [count, setCount] = useState(0)
    
    useEffect(() => {
      if (!inView) return
      
      let startTime: number
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime
        const progress = Math.min((currentTime - startTime) / duration, 1)
        setCount(Math.floor(progress * end))
        
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      
      requestAnimationFrame(animate)
    }, [inView, end, duration])
    
    return count
  }

  const technologies = [
    {
      icon: Smartphone,
      title: 'Flutter Framework',
      description: 'Cross-platform mobile development with native performance',
      color: 'from-blue-500 to-blue-600',
    },
    {
      icon: Database,
      title: 'SQLite Database',
      description: 'Reliable local storage with real-time synchronization',
      color: 'from-green-500 to-green-600',
    },
    {
      icon: Wifi,
      title: 'Bluetooth Integration',
      description: 'Seamless thermal printer connectivity and management',
      color: 'from-purple-500 to-purple-600',
    },
    {
      icon: Shield,
      title: 'Secure Architecture',
      description: 'Enterprise-grade security with encrypted data storage',
      color: 'from-red-500 to-red-600',
    },
    {
      icon: Zap,
      title: 'Real-time Updates',
      description: 'Instant data synchronization and live notifications',
      color: 'from-yellow-500 to-yellow-600',
    },
    {
      icon: Cloud,
      title: 'Cloud Backup',
      description: 'Automatic Google Drive integration for data safety',
      color: 'from-indigo-500 to-indigo-600',
    },
  ]

  const stats = [
    {
      icon: Users,
      label: 'Active Users',
      value: useCounter(10000),
      suffix: '+',
      color: 'text-blue-600',
    },
    {
      icon: TrendingUp,
      label: 'Transactions Processed',
      value: useCounter(1000000),
      suffix: '+',
      color: 'text-green-600',
    },
    {
      icon: Globe,
      label: 'Countries Supported',
      value: useCounter(25),
      suffix: '+',
      color: 'text-purple-600',
    },
    {
      icon: Award,
      label: 'Customer Satisfaction',
      value: useCounter(99),
      suffix: '%',
      color: 'text-orange-600',
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
      },
    },
  }

  return (
    <section id="technology" className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow" />
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow" />
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            Built with
            <span className="bg-gradient-to-r from-primary-400 to-blue-400 bg-clip-text text-transparent"> Modern Technology</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our POS system leverages cutting-edge technologies to deliver exceptional performance, 
            security, and user experience across all platforms.
          </p>
        </motion.div>

        {/* Technology Grid */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-20"
        >
          {technologies.map((tech) => (
            <motion.div
              key={tech.title}
              variants={itemVariants}
              className="group"
            >
              <Card className="h-full bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 transition-all duration-300 group-hover:scale-105">
                <CardHeader className="pb-4">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${tech.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    <tech.icon className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="text-lg font-semibold text-white">
                    {tech.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-300 leading-relaxed">
                    {tech.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-white mb-2">Trusted by Businesses Worldwide</h3>
            <p className="text-gray-300">Join thousands of satisfied customers using our POS system</p>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={inView ? { opacity: 1, scale: 1 } : {}}
                transition={{ delay: 0.6 + index * 0.1 }}
                className="text-center"
              >
                <div className="flex items-center justify-center mb-3">
                  <stat.icon className={`w-8 h-8 ${stat.color} mr-2`} />
                  <span className="text-3xl lg:text-4xl font-bold text-white stats-counter">
                    {stat.value.toLocaleString()}{stat.suffix}
                  </span>
                </div>
                <p className="text-gray-300 text-sm">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Tech Stack */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mt-16 text-center"
        >
          <h3 className="text-xl font-semibold text-white mb-6">Powered by Industry-Leading Technologies</h3>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {[
              { name: 'Flutter', icon: Code },
              { name: 'Dart', icon: Zap },
              { name: 'SQLite', icon: Database },
              { name: 'Material Design', icon: Palette },
              { name: 'Provider', icon: Smartphone },
              { name: 'Bluetooth', icon: Wifi },
            ].map((tech, index) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, y: 10 }}
                animate={inView ? { opacity: 0.6, y: 0 } : {}}
                transition={{ delay: 1 + index * 0.1 }}
                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors"
              >
                <tech.icon className="w-5 h-5" />
                <span className="text-sm font-medium">{tech.name}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Technology
